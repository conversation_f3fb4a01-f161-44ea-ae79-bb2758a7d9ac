﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace AMprover.Data;

/// <summary>
/// Startup filter that ensure database migration are executed on startup process of the application
/// </summary>
/// <remarks>The culprit of IStartupFilter is that it only allows synchronous execution of code. For database migrations this is not an issue since we have a synchronous Migrate() method.</remarks>
/// <typeparam name="T"></typeparam>
public class AMproverDataContextAutomaticMigrationStartupFilter<T> : IStartupFilter where T : DbContext
{
    /// <inheritdoc />
    public Action<IApplicationBuilder> Configure(Action<IApplicationBuilder> next)
    {
        return app =>
        {
            using (var scope = app.ApplicationServices.CreateScope())
            {
                var dbContext = scope.ServiceProvider.GetRequiredService<T>();
                dbContext.Database.SetCommandTimeout(160);
                
                // Check if database exists, if not create it
                if (!dbContext.Database.CanConnect())
                {
                    dbContext.Database.EnsureCreated();
                }
                
                // Apply any pending migrations
                dbContext.Database.Migrate();
            }
            next(app);
        };
    }
}
