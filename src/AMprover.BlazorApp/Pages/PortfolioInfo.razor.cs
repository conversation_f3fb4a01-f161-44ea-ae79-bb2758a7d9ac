using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Entities.Identity;
using AMprover.Data.Infrastructure;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;

namespace AMprover.BlazorApp.Pages;

public partial class PortfolioInfo
{
    [Inject]
    private AuthenticationStateProvider AuthenticationStateProvider { get; set; }

    [Inject]
    private UserManager<UserAccount> UserManager { get; set; }

    [Inject]
    private IPortfolioManager PortfolioManager { get; set; }

    [Inject]
    private AssetManagementDbContext AssetMgtDbContext { get; set; }

    private string _authUsername;
    private string _authFullname;
    private string _authMessage;
    private IEnumerable<Claim> _authClaims = Enumerable.Empty<Claim>();

    private IEnumerable<PortfolioModel> _userPortfolios = Enumerable.Empty<PortfolioModel>();
    private int? _userPorfolioSelectedId;

    private string _dbName;
    private bool _dbAvailable;

    private static void ThrowUp()
    {
            // Any unhandled exception is fatal to a circuit. For more information, see the preceding section on how a Blazor Server app reacts to unhandled exceptions.
            // https://docs.microsoft.com/en-us/aspnet/core/blazor/fundamentals/handle-errors?view=aspnetcore-5.0&pivots=server#how-a-blazor-server-app-reacts-to-unhandled-exceptions
            throw new Exception("kapoet boem knal");
        }

    protected override async Task OnInitializedAsync()
    {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity?.IsAuthenticated == true)
            {
                _authMessage = $"{user.Identity.Name} is authenticated.";
                _authClaims = user.Claims;

                if (user.Identity.Name != null)
                {
                    var loggedInUser = await UserManager.FindByNameAsync(user.Identity.Name);

                    _authUsername = loggedInUser?.UserName;
                    _authFullname = loggedInUser?.Name;
                }

                // Portfolio information
                _userPortfolios = PortfolioManager.GetPortfoliosForLoggedInUser().ToList();

                var userPortfolioSelected = PortfolioManager.GetCurrentPortfolio();

                _userPorfolioSelectedId = userPortfolioSelected?.Id;
                _dbName = userPortfolioSelected?.DatabaseName;
                _dbAvailable = await AssetMgtDbContext.Database.CanConnectAsync();
            }
            else
            {
                _authMessage = "The user is NOT authenticated.";
            }
        }
}