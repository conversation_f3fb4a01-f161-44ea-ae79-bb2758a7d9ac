﻿@page "/"
@using AMprover.BlazorApp
@using Microsoft.AspNetCore.Mvc.TagHelpers
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>AMprover</title>
    <base href="~/"/>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css" integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css">
    <link rel="stylesheet" href="_content/Radzen.Blazor/css/material-base.css">
    <link href="css/site.css" rel="stylesheet"/>
    <link href="css/custom.css" rel="stylesheet"/>
    <link href="css/print.css" rel="stylesheet" media="print"/>
</head>
<body>
<app>
    <component type="typeof(App)" render-mode="Server"/>
</app>

<div id="blazor-error-ui">
    <environment include="Staging,Production">
        An error has occurred. This application may no longer respond until reloaded.
    </environment>
    <environment include="Development">
        An unhandled exception has occurred. See browser dev tools for details.
    </environment>
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>


<script src="_content/Radzen.Blazor/Radzen.Blazor.js?v=0.6"></script>
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js?v=0.6" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js?v=0.6" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js?v=0.6" integrity="sha384-OgVRvuATP1z7JjHLkuOU7Xw704+h835Lr+6QL9UvYjZE3Ipu6Tp75j7Bh/kR0JKI" crossorigin="anonymous"></script>

<script src="_framework/blazor.server.js?v=0.6" autostart="false"></script>
<script>
    Blazor.start({
        configureSignalR: function (builder){
         let c = builder.build();
             c.serverTimeoutInMilliseconds = 3000000;
             c.keepAliveIntervalInMilliseconds = 1500000;
         builder.build = () => {
            return c;
         };
        }
    });
</script>

<script src="/js/zoom.js?v=0.6"></script>
<script src="/js/FileInputValueClearer.js?v=0.6"></script>
<script src="/js/CookieHelper.js?v=0.6"></script>
<script src="/js/ConvertToImage.js?v=0.6"></script>
<script src="/js/html2canvas.min.js?v=0.6"></script>
<script src="/js/iframe-pdf.js?v=0.6"></script>
<script src="/js/lineHelper.js?v=0.6"></script>
<script src="/js/UtilityGridWidthHelper.js?v=0.6"></script>
<script src="/js/AMDropdown.js"></script>

</body>
</html>
