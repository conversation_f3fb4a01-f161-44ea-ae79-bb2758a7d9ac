using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;

namespace AMprover.BlazorApp.Pages;

public partial class TableColumns
{
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private DialogService DialogService { get; set; }

    [Parameter] public string ControlName { get; set; }
    [Parameter] public EventCallback<object> ReseedFunction { get; set; }
    [Parameter] public EventCallback<object> SaveCallback { get; set; }

    private ILogger _logger;
    private List<GridColumnModel> GridColumns { get; set; }

    private List<FieldType> NumberFieldTypes { get; set; } =
    [
        FieldType.Integer, FieldType.IntegerNoStyling, FieldType.Currency, FieldType.DetailCurrency, FieldType.Number,
        FieldType.Percentage
    ];

    protected override void OnInitialized()
    {
        base.OnInitialized();
        _logger = LoggerFactory.CreateLogger<TableColumns>();
        LoadData();
    }

    private void LoadData()
    {
        GridColumns = LookupManager.GetColumns(ControlName, false).ToList();
    }

    private async void ValidTaskSubmitted(EditContext editContext)
    {
        var gridColumns = (List<GridColumnModel>)editContext.Model;
        CreateOrUpdateObjectAndNavigate(gridColumns);

        if (SaveCallback.HasDelegate)
            await SaveCallback.InvokeAsync();
    }

    private void InvalidTaskSubmitted(EditContext editContext)
    {
        _logger.LogWarning($"Invalid form submitted for grid columns update.");
        _logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject((List<GridColumnModel>)editContext.Model));
    }

    private void CreateOrUpdateObjectAndNavigate(IReadOnlyCollection<GridColumnModel> columns)
    {
        LookupManager.UpdateColumns(columns);
        _logger.LogInformation($"Updating columns {columns.FirstOrDefault()?.ControlName} succeeded.");
        DialogService.Close(true);
    }
}