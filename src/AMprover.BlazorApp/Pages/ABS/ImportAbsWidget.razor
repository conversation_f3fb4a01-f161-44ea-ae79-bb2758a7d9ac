﻿@page "/import-abs-widget"

<div class="row">
    <div class="col-12">

        <div class="row mb-3">
            <div class="col-12">

                <div class="row">
                    <div class="col-12">
                        @((MarkupString)Localizer["AbsUploadTemplateTxt"].Value)
                    </div>
                </div>

                @if (HasAssetsInDatabase)
                {
                    <div class="row my-3">
                        <div class="col-12" style="color:darkorange;">
                            @Localizer["AbsCreateTreeBasedOnAssetCatNotAllowed"]
                        </div>
                    </div>
                }
                else
                {
                    <div class="row my-3">
                        <div class="col-12">
                            <RadzenCheckBox @bind-Value=@CreateTreeBasedOnAssetCategory Disabled=@HasAssetsInDatabase /> 
                            @Localizer["AbsCreateTreeBasedOnAssetCat"]
                        </div>
                    </div>
                }

                <div class="row mt-4">
                    <div class="col-12">
                        <RadzenFileInput @ref=@UploadRef 
                                                                                                                                                                                                                                                                                                                                        @bind-Value=@FileUpload 
                                         Accept=".xlsx,.xls" TValue="string" 
                                         ChooseText=@Localizer["AbsImportTxt"]
                                         Change=@(args => OnChange(args)) 
                                         Error=@(args => OnError(args)) />
                    </div>
                </div>

                @if (!string.IsNullOrWhiteSpace(UploadError))
                {
                    <div class="row mt-4">
                        <div class="col-12" style="color:red;">
                            @UploadError
                        </div>
                    </div>
                }
            </div>
        </div>

    </div>
</div>