using AMprover.BusinessLogic;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON><PERSON>;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Models;
using Radzen.Blazor;
using AMprover.BlazorApp.Components;
using Microsoft.JSInterop;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.ABS;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.ABS;

public partial class ImportAbsWidget
{
    [Inject] private ILogger<ImportAbsWidget> Logger { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IAssetBreakdownManager AssetBreakdownManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IJSRuntime JsRuntime { get; set; }
    [Inject] private IStringLocalizer<AssetBreakdownOverview> Localizer { get; set; }

    [Parameter] public EventCallback ConfirmCallback { get; set; }
    [Parameter] public bool HasAssetsInDatabase { get; set; }

    private List<AssetModel> Assets { get; set; }

    public AssetModel Asset { get; set; }

    public List<LookupUserDefinedModel> AssetCategories { get; set; }

    public string FileUpload { get; set; }
    private RadzenFileInput<string> UploadRef { get; set; }
    private string UploadError { get; set; }
    private bool CreateTreeBasedOnAssetCategory { get; set; }

    protected override void OnInitialized()
    {
        Assets = AssetBreakdownManager.GetAllAssets();
        Asset = Assets.FirstOrDefault();
        AssetCategories = LookupManager.GetAssetCategories();
        UploadError = FileUpload = null;
        CreateTreeBasedOnAssetCategory = false;
    }

    private async Task OnChange(string value)
    {
        // clear ref, the component wants to display an image preview which does not apply
        UploadError = FileUpload = null;
        await UploadAssetBreakdownStructure(value?.Split(',').LastOrDefault()?.Trim()).ConfigureAwait(false);
        await JsRuntime.InvokeAsync<bool>("ClearFileInputValue", UploadRef.Element).ConfigureAwait(false);
    }

    private void OnError(UploadErrorEventArgs args)
    {
        UploadError = args.Message;
    }

    private async Task UploadAssetBreakdownStructure(string base64Input)
    {
        if (string.IsNullOrWhiteSpace(base64Input))
            return;

        ShowLoadingDialog();
        var result = new ImportResult();

        var uploadedItems = base64Input.GetExcelData<AssetModel>(1)
            .Select(x => x.Value).Where(x => !string.IsNullOrWhiteSpace(x.Code)).ToList();

        if (Assets.Count == 0)
        {
            uploadedItems.ForEach(x => { x.Id = 0; x.ParentId = null; });
        }

        if (AssetBreakdownManager.ImportFileHasIssues(uploadedItems, out var errorMesage))
        {
            result.SetFailed();
            result.ErrorMessage = errorMesage;
        }
        else
        {
            result = await AssetBreakdownManager.ProcessImportedAssets(uploadedItems);

            if (result.Success)
            {
                Assets = CreateTreeBasedOnAssetCategory
                    ? AssetBreakdownManager.SetParentIdsBasedOnSequenceAndAssetCategory()
                    : AssetBreakdownManager.GetAllAssets();

                Asset = Assets.FirstOrDefault();
            }
        }

        // Close loading widget
        DialogService.Close();

        // Close current widget
        DialogService.Close();

        // Show result Widget
        ShowImportResultDialog(result);

        await ConfirmCallback.InvokeAsync();
    }

    private void ShowLoadingDialog()
    {
        DialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    private void ShowImportResultDialog(ImportResult importResult)
    {
        var style = importResult.Success
            ? "min-height: 180px;"
            : "min-height: 280px;";

        DialogService.Open<ImportResultDialog>(importResult.Success ? "Success" : "Error importing assets",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), importResult }
            }, new DialogOptions { Draggable = true, Style = style });
    }
}