using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON><PERSON>;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.ABS;

public partial class DeleteAbsWidget
{
    [Inject] private ILogger<DeleteAbsWidget> Logger { get; set; }
    [Inject] private IAssetBreakdownManager AssetBreakdownManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IStringLocalizer<AssetBreakdownOverview> Localizer { get; set; }

    [Parameter] public EventCallback ConfirmCallback { get; set; }

    public async Task DeleteAllAssets()
    {
        AssetBreakdownManager.DeleteAllAssets();
        DialogService.Close();
        await ConfirmCallback.InvokeAsync();
    }
}