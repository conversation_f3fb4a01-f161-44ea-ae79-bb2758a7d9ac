using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.ABS;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Radzen;
using Radzen.Blazor;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Models;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.ABS;

public partial class AssetBreakdownOverview
{
    [Inject] private ILogger<AssetBreakdownOverview> Logger { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IAssetBreakdownManager AssetBreakdownManager { get; set; }
    [Inject] private IJSRuntime JsRuntime { get; set; }
    [Inject] private IStringLocalizer<AssetBreakdownOverview> Localizer { get; set; }
    [Inject] private IGlobalDataService GlobalDataService { get; set; }

    private RadzenTabs Tabs { get; set; }

    public List<AssetModel> Assets { get; set; }
    private UtilityGrid<AssetModel> AssetGrid { get; set; }

    public TreeGeneric<AssetModel> AssetTree { get; } = new();

    private AssetModel _asset { get; set; }
    public AssetModel Asset
    {
        get => _asset;
        set
        {
            _asset = value;
            AssetDetails = GetAssetDetails(value);
        }
    }

    private List<LookupUserDefinedModel> AssetCategories { get; set; }
    private UtilityGrid<LookupUserDefinedModel> AssetCategoryGrid { get; set; }

    private List<KeyValuePair<string, string>> AssetDetails { get; set; } = [];

    protected override void OnInitialized()
    {
        Assets = AssetBreakdownManager.GetAllAssets();
        Asset = Assets.FirstOrDefault();
        AssetCategories = LookupManager.GetAssetCategories();
    }

    private bool ValidateCanSaveAssetCategory(LookupUserDefinedModel model)
    {
        return AssetCategories.Count(x => x.Value == model.Value) <= 1;
    }

    private void ValidateCanSaveAssetCategoryFailed(LookupUserDefinedModel model)
    {
        ShowErrorMessage(Localizer["AbsCatValMustBeUniqueTxt"]);
    }

    private void TabsChange(int index)
    {
        if (index == 1)
        {
            AssetTree.Initialize(AssetBreakdownManager.GetAssetTree(Assets));
        }
    }

    private void SelectAsset(TreeNodeGeneric<AssetModel> node)
    {
        Asset = node.Source;
    }

    private void SaveAsset(AssetModel asset)
    {
        var updatedAsset = AssetBreakdownManager.SaveAsset(asset);
        if (updatedAsset.ParentId != null && Assets.All(x => x.Id != updatedAsset.ParentId))
            updatedAsset.ParentId = null;

        if (!Assets.Contains(asset))
            Assets.Add(updatedAsset);
        else
            Assets[Assets.IndexOf(asset)] = updatedAsset;

        AssetGrid.Reload();
    }

    private void DeleteAsset(AssetModel asset)
    {
        AssetBreakdownManager.DeleteAsset(asset.Id);
        Assets.Remove(asset);
        AssetGrid.Reload();
    }

    private void UpdateAssetCategory(LookupUserDefinedModel model)
    {
        var original = AssetCategories.FirstOrDefault(x => x.Id == model.Id);
        var result = LookupManager.SaveAssetCategory(model);

        if (original != null)
            AssetCategories[AssetCategories.IndexOf(original)] = result;
        else
            AssetCategories.Add(result);

        AssetCategoryGrid.Reload();
    }

    private void DeleteAssetCategory(LookupUserDefinedModel model)
    {
        if (model?.Id == null) return;
        if (LookupManager.DeleteAssetCategory(model.Id, out var result))
        {
            AssetCategories.Remove(model);
            AssetCategoryGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            AssetCategoryGrid.EditItem(model);
        }
    }

    private static Dictionary<string, Dictionary<int, string>> GetDropdownOverride()
    {
        return new Dictionary<string, Dictionary<int, string>>();
    }

    private static List<KeyValuePair<string, string>> GetAssetDetails(AssetModel asset)
    {
        var dict = new List<KeyValuePair<string, string>>();

        if (asset == null)
            return dict;

        var fields = asset.GetType().GetFields(BindingFlags.NonPublic | BindingFlags.Instance);

        dict.AddRange(from field in fields let name = field.Name let value = field.GetValue(asset) where value != null select new KeyValuePair<string, string>(name.Split('<')[1].Split('>')[0], value.ToString()));
        return dict;
    }

    private void PasteNode((List<AssetModel> source, AssetModel destination) args)
    {
        if (args.source?.Any() != true || args.destination == null)
            return;
            
        args.source.First().ParentId = args.destination.Id;
        args.source.First().ParentCode = args.destination.Code;
        AssetBreakdownManager.SaveAsset(args.source.First());
    }

    private void OpenDeleteWidget()
    {
        DialogService.Open<DeleteAbsWidget>
        (Localizer["AbsDeleteTabTxt"],
            new Dictionary<string, object>
            {
                {
                    nameof(DeleteAbsWidget.ConfirmCallback),
                    EventCallback.Factory.Create(this, WidgetCallBack)
                }
            });
    }

    private void OpenImportAbsWidget()
    {
        DialogService.Open<ImportAbsWidget>
        (Localizer["AbsImportTabTxt"],
            new Dictionary<string, object>
            {
                {
                    nameof(ImportAbsWidget.ConfirmCallback),
                    EventCallback.Factory.Create(this, WidgetCallBack)
                },
                {
                    nameof(ImportAbsWidget.HasAssetsInDatabase),
                    Assets.Any()
                }
            });
    }

    private void WidgetCallBack()
    {
        OnInitialized();
        Tabs.Reload();
    }

    private void ShowErrorMessage(string errorMessage)
    {
        DialogService.Open<NotificationDialog>("Error",
            new Dictionary<string, object>
            {
                {nameof(NotificationDialog.Texts), new List<string> {errorMessage}}
            });
    }

    private void ShowDbErrorMessage(DbOperationResult dbResult)
    {
        DialogService.Open<NotificationDialog>("Error",
            new Dictionary<string, object>
            {
                {nameof(NotificationDialog.Texts), dbResult.ErrorMessages}
            });
    }
}