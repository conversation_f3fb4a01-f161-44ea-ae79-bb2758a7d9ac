﻿@page "/asset-breakdown-structure"

<h2>@Localizer["AbsHeaderTxt"]</h2>
<div class="row header-navigation">
    <div class="col-6">
        <Breadcrumbs Category="Asset Breakdown Structure" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right btn-centered my-2 mx-2" DialogTitle=@Localizer["AbsMenuTitle"] DialogContent=@Localizer["AbsMenuTxt"] />
        <RadzenButton Disabled=!GlobalDataService.CanEdit ButtonStyle="ButtonStyle.Secondary" Icon="input" Text=@(Localizer["AbsImportTabTxt"]) class="float-right my-2 mx-0" Click=@OpenImportAbsWidget />
        <RadzenButton Text="Delete Assets" Icon="delete" class="float-right my-2 mx-2" Click=@OpenDeleteWidget />
    </div>
</div>

<p>@((MarkupString)Localizer["AbsSubHeaderTxt"].Value)</p>

<br />

<RadzenTabs @ref=@Tabs Change=@TabsChange>
    <Tabs>
        <RadzenTabsItem Text=@Localizer["AbsAssetTabTxt"]>
            <UtilityGrid TItem=AssetModel
                         @ref=AssetGrid
                         Data=Assets
                         FileName=@GridNames.Portfolio.AssetBreakdownStructure
                         Interactive=true
                         AllowXlsExport=true
                         AllowSorting=true
                         AllowFiltering=true
                         AddNewButton=false
                         MaxRows=25
                         SaveCallback=@SaveAsset
                         DeleteCallback=@DeleteAsset
                         DropDownOverrides=@GetDropdownOverride() />
        </RadzenTabsItem>

        <RadzenTabsItem Text=@Localizer["AbsTreeViewTabTxt"]>
            <div class="row">
                <div class="col-6">
                    <TreeComponent TItem=AssetModel
                                   Treeview=AssetTree
                                   CssClass="abs-tree"
                                   NodeClickCallback=@SelectAsset
                                   PasteCallback=@PasteNode
                                   HandleCutPasteInternally=true />
                </div>

                <div class="col-6">
                    <RadzenDataGrid AllowFiltering="true" AllowPaging="true" AllowSorting="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                    PageSize="12" Data="@AssetDetails" TItem="KeyValuePair<string,string>">
                        <Columns>
                            <RadzenDataGridColumn Width="30%" TItem="KeyValuePair<string,string>" Property="@nameof(KeyValuePair<string, string>.Key)" Title="@nameof(KeyValuePair<string, string>.Key)" />
                            <RadzenDataGridColumn Width="70%" TItem="KeyValuePair<string,string>" Property="@nameof(KeyValuePair<string, string>.Value)" Title="@nameof(KeyValuePair<string, string>.Value)" />
                        </Columns>
                    </RadzenDataGrid>
                </div>
            </div>
        </RadzenTabsItem>

        @if (GlobalDataService.CanEdit)
        {
            <!-- Si / Asset Category -->
            <RadzenTabsItem Text=@Localizer["AbsCategorySettingTabTxt"]>
                <div class="row mb-3">
                    <div class="col-12">
                        <UtilityGrid TItem=LookupUserDefinedModel
                                     @ref=@AssetCategoryGrid
                                     Data=@AssetCategories
                                     FileName=@GridNames.Portfolio.AssetCategories
                                     Interactive=true
                                     AddNewButton=true
                                     AllowFiltering=true
                                     SaveCallback=@UpdateAssetCategory
                                     DeleteCallback=@DeleteAssetCategory
                                     NewItemTemplate=@(new LookupUserDefinedModel{ Filter = "SICategory"})
                                     ExternalSaveValidation=@ValidateCanSaveAssetCategory
                                     ExternalSaveValidationFailed=@ValidateCanSaveAssetCategoryFailed />
                    </div>
                </div>
            </RadzenTabsItem>
        }
    </Tabs>
</RadzenTabs>
