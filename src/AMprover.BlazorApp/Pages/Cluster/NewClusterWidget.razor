﻿@page "/new-cluster-widget"

<div class="form-group">@Localizer["CwHeaderTxt"]</div>

<div class="form-group">
    <label>@Localizer["CwClustNameLbl"]:</label>
    <RadzenTextBox @bind-Value=@Name Name="Name" class="form-control"/>
    <RadzenRequiredValidator Component="Name"/>
</div>

<div class="form-group">
    <label>@Localizer["CwPartOfClustLbl"]:</label>
    <RadzenDropDown Name="PartOfCluster" AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    TextProperty="Value" ValueProperty="Key" class="form-control"
                    Data="@ClusterDict"
                    @bind-Value="@PartOfCluster"/>
    <RadzenRequiredValidator DefaultValue="0" Component="PartOfCluster"></RadzenRequiredValidator>
</div>

<RadzenButton IsBusy=@IsBusy Text=@Localizer["CwCreateBtn"] Click=@CreateNewCluster></RadzenButton>
