using System;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON><PERSON>;
using Ra<PERSON>zen.Blazor;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BlazorApp.Pages.Cluster.Costs;
using AMprover.BusinessLogic.Extensions;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using ClusterModel = AMprover.BusinessLogic.Models.Cluster.ClusterModel;
using AMprover.BlazorApp.Components.Pagination;
using AutoMapper;
using Npoi.Mapper;

namespace AMprover.BlazorApp.Pages.Cluster
{
    public partial class ClusterPage
    {
        [Inject] private IClusterManager ClusterManager { get; set; }
        [Inject] private ILookupManager LookupManager { get; set; }
        [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
        [Inject] private IDropdownManager DropdownManager { get; set; }
        [Inject] private IPageNavigationManager PageNavigation { get; set; }
        [Inject] private IMapper Mapper { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; }
        [Inject] private ILoggerFactory LoggerFactory { get; set; }
        [Inject] private DialogService DialogService { get; set; }
        [Inject] private IStringLocalizer<ClusterPage> Localizer { get; set; }
        [Inject] private IGlobalDataService GlobalDataService { get; set; }

        [Parameter]
        public int? ClusterId { get; set; }

        private ILogger _logger;
        private ClusterTreeObject CurrentCluster { get; set; }
        private int SelectedScenario { get; set; }

        public ClusterModel SelectedCluster { get; set; }
        public List<ClusterTaskModel> SelectedTasks { get; set; } = [];
        private TaskModel SelectedTask { get; set; }
        public List<ClusterTaskPlanModelWithExtraTaskProperties> ClusterTaskPlanModels { get; set; }

        private Dictionary<int, string> StatusDict { get;  set; } = new();
        public List<ClusterModel> ClusterList { get; set; } = [];
        public TreeGeneric<ClusterTreeObject> ClusterTree { get; } = new();

        private RadzenTabs ClusterTabs { get; set; }
        private RadzenTabs ClusterDataTabs { get; set; }
        private RadzenTabs TreeTab { get; set; }
        private RadzenTabs DetailsTab { get; set; }

        public UtilityGrid<ClusterTaskModel> TaskConceptGrid { get; set; }
        public UtilityGrid<ClusterCostModel> ClusterCostGrid { get; set; }
        private UtilityGrid<ClusterCostModel> ClusterCostDetailGrid { get; set; }
        public UtilityGrid<ClusterTaskPlanModelWithExtraTaskProperties> TaskPlanGrid { get; set; }
        public UtilityGrid<ClusterModel> ClusterGrid { get; set; }

        private Dictionary<string, Dictionary<int, string>> ClusterTaskPlanDropdownOverrides { get;  set; }
        private Dictionary<string, Dictionary<int, string>> ClusterCostDropdownOverrides { get;  set; }
        private Dictionary<string, Dictionary<int, string>> TaskDropdownOverrides { get;  set; }

        private Dictionary<int, string> IntervalDict { get;  set; }
        private Dictionary<int, string> InitiatorDict { get;  set; }
        private Dictionary<int, string> ExecutorDict { get;  set; }
        private Dictionary<int, string> RiskObjectDict { get; set; }
        private Dictionary<int, string> ScenarioDict { get;  set; }

        private Paginator Paginator { get; set; }
        private bool IsSapaView { get; set; }

        protected override void OnInitialized()
        {
            _logger = LoggerFactory.CreateLogger<ClusterPage>();

            StatusDict = DropdownManager.GetStatusDict();

            ClusterTaskPlanDropdownOverrides = DropdownManager.GetClusterTaskPlanDropdownOverrides();
            ClusterCostDropdownOverrides = DropdownManager.GetClusterCostDropDownOverrides();
            TaskDropdownOverrides = DropdownManager.GetClusterTaskModelDropDownOverrides();

            ExecutorDict = DropdownManager.GetExecutorDict();
            InitiatorDict = DropdownManager.GetInitiatorDict();
            IntervalDict = DropdownManager.GetIntervalUnitDict();
            RiskObjectDict = DropdownManager.GetRiskObjectDict();
            ScenarioDict = DropdownManager.GetScenarioDict();

            SelectedScenario = PageNavigation.GetSelectedScenario() ?? ScenarioDict.FirstOrDefault().Key;
            
            ClusterList = ClusterManager.GetClustersForScenario(SelectedScenario);
            SelectedCluster = ClusterManager.GetCluster(ClusterId ?? ClusterList.FirstOrDefault()?.Id ?? 0);
            SelectedTasks = SelectedCluster.Tasks.Select(x => Mapper.Map<TaskModel, ClusterTaskModel>(x)).ToList();
            ClusterTaskPlanModels = ClusterManager.GetClusterTaskPlanWithExtraProperties(SelectedCluster.Id);

            IsSapaView = SelectedCluster.RiskObject?.AnalysisType == "SAPA";

            ClusterTree.Initialize(ClusterManager.GetTreeViewForClusters(SelectedScenario));
            SelectActiveNodeInTree();
        }

        private void SelectActiveNodeInTree()
        {
            var node = ClusterTree.GetClusterTreeNode(SelectedCluster.Id);

            if (node != null)
            {
                ClusterTree.SelectNode(node);
                UpdatePaginator();
                var firstTask = SelectedCluster.Tasks.FirstOrDefault();

                if (firstTask != null)
                    SelectClusterTask(firstTask);
            }
        }

        private void ChangeScenario()
        {
            PageNavigation.SetSelectedScenario(SelectedScenario);
            ClusterList = ClusterManager.GetClustersForScenario(SelectedScenario);
            ReloadTreeAndTabs();
        }

        private void SaveCluster(ClusterModel cluster)
        {
            ClusterManager.UpdateCluster(cluster);
        }

        private void SaveCluster()
        {
            var parentCluster = ClusterList.FirstOrDefault(x => x.Id == SelectedCluster.PartOf);

            if(SelectedCluster.PartOf == SelectedCluster.Id)
            {
                // Cannot set self as parent
                ErrorDialog(Localizer["CCCNoSelfPartOf"]);
            }
            else if(parentCluster == null)
            {
                // Invalid Parent
                ErrorDialog(Localizer["CCCInvalidParent"]);
            }
            else
            {
                SelectedCluster.PartOf = parentCluster.Id;
                SelectedCluster.PartOfCluster = parentCluster;
                BusyDialog("Saving cluster");
                SelectedCluster = ClusterManager.UpdateCluster(SelectedCluster);
                _logger.LogInformation($"Saving a {nameof(SelectedCluster)} succeeded. Name = {SelectedCluster.Name}, ID = {SelectedCluster.Id}.");
                DialogService.Close();
                ReloadTreeAndTabs();
                SelectActiveNodeInTree();
            }
        }

        #region cluster level

        private Dictionary<int, string> GetClusterLevelDropdown()
        {
            return new Dictionary<int, string> 
            { 
                { 1, "1" }, 
                { 2, "2" }, 
                { 3, "3" } 
            };
        }

        private void ChangeClustLevel()
        {
            SelectedCluster.PartOf = null;
            SelectedCluster.PartOfCluster = null;
        }

        private Dictionary<int?, string> GetClusterPartOfDropdown()
        {
            return ClusterList.Where(x => x.Level == SelectedCluster.Level).ToDictionary(x => (int?)x.Id, x => x.Name);
        }

        #endregion

        #region Tree related methods

        public void ClickTreeNode(TreeNodeGeneric<ClusterTreeObject> node)
        {
            if (node == null)
            {
                CurrentCluster = null;
                return;
            }   

            CurrentCluster = node.Source;

            if (!CurrentCluster.Id.HasValue) return;
            if (SelectedCluster.Id == CurrentCluster.Id) return;

            SelectedCluster = ClusterManager.GetCluster(CurrentCluster.Id.Value);
            ClusterTaskPlanModels = ClusterManager.GetClusterTaskPlanWithExtraProperties(SelectedCluster.Id);

            var firstTask = SelectedCluster.Tasks.FirstOrDefault();
            if (firstTask != null)
            {
                // Fix broken clusters
                if(SelectedCluster.ScenarioId == null)
                {
                    SelectedCluster.ScenarioId = firstTask.Risk.RiskObject.ScenarioId;
                    SaveCluster();
                }
                
                SelectClusterTask(firstTask);
            }

            PageNavigation.SavePageQueryString($"/cluster/{SelectedCluster.Id}");
            NavigationManager.NavigateTo($"/cluster/{SelectedCluster.Id}");

            UpdatePaginator();
        }
        
        private void DeleteClusterFromTree(object clusterTreeObject)
        {
            if (clusterTreeObject is ClusterTreeObject cluster)
            {
                if (!(cluster.Id > 0)) 
                    return;

                ClusterManager.DeleteCluster(cluster.Id.Value);
                
                if (cluster.Id == SelectedCluster.Id)
                    SelectNearestNode();
            }
            else
            {
                throw new ArgumentException($"Incorrect configuration, function should only receive object of Type {typeof(ClusterTreeObject)}");
            }
        }
        
        private void SelectNearestNode()
        {
            var location = ClusterTree.GetNodeLocation(ClusterTree.SelectedNode);
            var parentNode = location.Count > 1
                ? ClusterTree.GetNodeFromLocation(location.Take(location.Count - 1).ToList())
                : null;

            if (parentNode?.Nodes.Any() == true)
            {
                var siblingNode = parentNode.Nodes[Math.Max(0, location.Last() - 1)];
                ClickTreeNode(siblingNode);
            }
            else
            {
                var firstNode = ClusterTree.Node.Nodes.FirstOrDefault();
                ClickTreeNode(firstNode);
            }
        }

        #endregion

        #region Cluster tasks

        private void SelectClusterTask(TaskModel selectedClusterTask)
        {
            SelectedTask = selectedClusterTask;

            ClusterCostDetailGrid?.Grid.Reload();
            ClusterDataTabs?.Reload();
        }

        private void SaveClusterTask(ClusterTaskModel clusterTaskModel)
        {
            var clusterTask = Mapper.Map<ClusterTaskModel, TaskModel>(clusterTaskModel);

            // The Costs of a Task should be overriden by the CLusterCosts when saved from the Cluster Page
            clusterTask.Costs = clusterTask.ClusterCosts > 0
                ? clusterTask.ClusterCosts
                : clusterTask.EstCosts;

            clusterTask = RiskAnalysisManager.UpdateTask(clusterTask);
            TaskConceptGrid?.Grid.CancelEditRow(clusterTaskModel);

            //If clusterId is changed we need to make sure the task isn't show anymore.
            if (clusterTask.ClusterId != CurrentCluster?.Id)
            {
                //SelectedTasks = SelectedTasks.Where(x => x.ClusterId == CurrentCluster.Id).ToList();
                ClusterDataTabs?.Reload();
            }
        }

        #endregion

        #region Cluster costs

        private void SaveClusterCost(ClusterCostModel clusterCost)
        {
            ClusterManager.UpdateClusterCost(clusterCost);
            ClusterManager.SyncClusterCosts(SelectedCluster.Id);
            SelectedCluster = ClusterManager.GetCluster(SelectedCluster.Id);
        }

        private void DeleteClusterCost(ClusterCostModel clusterCost)
        {
            ClusterManager.DeleteClusterCost(clusterCost.Id);
            SelectedCluster = ClusterManager.SyncClusterCosts(SelectedCluster.Id);
            SelectedCluster = ClusterManager.GetCluster(SelectedCluster.Id);
        }

        private void OpenClusterCostPopup(int id)
        {
            DialogService.Open<CostEdit>(Localizer["CvmClusterCostDetailsTxt"],
                new Dictionary<string, object>
                {
                    {"CostId", id},
                    {"TaskId", SelectedTask?.Id},
                    {"ClusterId", SelectedCluster?.Id},
                    {"Callback", EventCallback.Factory.Create<ClusterModel>(this, AddOrEditClusterCostCallBack)}
                },
                new DialogOptions {Width = "800px", Resizable = false, Draggable = true });
        }

        private void AddOrEditClusterCostCallBack(ClusterModel model)
        {
            SelectedCluster = ClusterManager.GetCluster(model.Id);
            ClusterTabs?.Reload();
            ClusterDataTabs?.Reload();
            DetailsTab?.Reload();
        }

        #endregion

        #region command center methods

        private void AddNewCluster()
        {
            DialogService.Open<NewClusterWidget>
            ("New Cluster",
                new Dictionary<string, object>
                {
                    {"PartOfCluster", SelectedCluster?.Id},
                    {nameof(NewClusterWidget.CallBack), LoadNewCluster}
                });
        }

        private void LoadNewCluster(ClusterModel newCluster)
        {
            ClusterTree.Initialize(ClusterManager.GetTreeViewForClusters(SelectedScenario));
            TreeTab?.Reload();

            ClusterTree.ExpandTreeTillNode(newCluster.Id);

            SelectedCluster = newCluster;
            SelectedTasks = SelectedCluster.Tasks.Select(x => Mapper.Map<TaskModel, ClusterTaskModel>(x)).ToList();
            ClusterTaskPlanModels = ClusterManager.GetClusterTaskPlanWithExtraProperties(SelectedCluster.Id);

            if (SelectedCluster != null)
            {
                ReloadTreeAndTabs();
            }
        }

        private void OnClusterConceptClick(RadzenSplitButtonItem item)
        {
            if (item == null) return;
            var dialogOptions = new DialogOptions { Width = "500px", Resizable = false, Draggable = true };

            switch (item.Value)
            {
                // Update Cluster by Scenario
                case "1":
                    DialogService.Open<ClusterCommandCenterWidget>(Localizer["ClustUpdateClustConceptByScenarioBtn"],
                        new Dictionary<string, object>
                        {
                            {"CallbackInt", EventCallback.Factory.Create<int>(this, UpdateClusterConceptByScenario)},
                            {"ExtraConfirmation", false},
                            {"ConfirmationText", Localizer["CvmUpdateClustConceptScenTxt"].Value},
                            {"Options", ScenarioDict }
                        },
                        dialogOptions);
                    break;

                // Update Cluster by RiskObject
                case "2":
                    DialogService.Open<ClusterCommandCenterWidget>(Localizer["ClustUpdateClustConceptByRiskObjectBtn"],
                        new Dictionary<string, object>
                        {
                            {"CallbackInt", EventCallback.Factory.Create<int>(this, UpdateClusterConceptByRiskObject)},
                            {"ExtraConfirmation", false},
                            {"ConfirmationText", Localizer["CvmUpdateClustConceptTxt"].Value},
                            {"Options", RiskObjectDict }
                        },
                        dialogOptions);
                    break;

                // Regenerate Cluster by Scenario
                case "3":
                    DialogService.Open<ClusterCommandCenterWidget>(Localizer["ClustRegenerateClustConceptByScenarioBtn"],
                        new Dictionary<string, object>
                        {
                            {"CallbackInt", EventCallback.Factory.Create<int>(this, RegenerateClusterConceptByScenario)},
                            {"ExtraConfirmation", true},
                            {"ConfirmationText", Localizer["CvmRegenerateClusterTxt"].Value},
                            {"Options", ScenarioDict }
                        },
                        dialogOptions);
                    break;

                // Regenerate Cluster by RiskObject
                case "4":
                    DialogService.Open<ClusterCommandCenterWidget>(Localizer["ClustRegenerateClustConceptByRiskObjectBtn"],
                        new Dictionary<string, object>
                        {
                            {"CallbackInt", EventCallback.Factory.Create<int>(this, RegenerateClusterConceptByRiskObject)},
                            {"ExtraConfirmation", true},
                            {"ConfirmationText", Localizer["CvmRegenerateClusterTxt"].Value},
                            {"Options", RiskObjectDict }
                        },
                        dialogOptions);
                    break;
            }
        }

        private void OnTaskPlanClick()
        {
            DialogService.Open<ClusterCommandCenterWidget>(Localizer["ClustRegenerateTaskPlanBtn"],
                new Dictionary<string, object>
                {
                            {"Callback", EventCallback.Factory.Create(this, RegenerateTaskPlan)},
                            {"ConfirmationText", Localizer["CvmRegenerateTaskPlanTxt"].Value}
                },
                new DialogOptions { Width = "400px", Resizable = false, Draggable = true });

            ReloadTreeAndTabs();
        }

        private void ProcessDownTime()
        {
            ClusterManager.ProcessClusterDownTime(SelectedCluster);
            SaveCluster();
        }

        private void ProcessDownDuration()
        {
            ClusterManager.ProcessClusterDuration(SelectedCluster);
            SaveCluster();
        }

        private async Task UpdateClusterConceptByScenario(int scenarioId)
        {
            DialogService.Close();
            BusyDialog(Localizer["CvmRefreshingClustConceptUpdateTxt"].Value);
            await Task.Run(() => ClusterManager.UpdateClusterConceptByScenarioId(scenarioId, regenerate: false));
            DialogService.Close();

            ReloadTreeAndTabs();
        }

        private async Task UpdateClusterConceptByRiskObject(int scenarioId)
        {
            DialogService.Close();
            BusyDialog(Localizer["CvmRefreshingClustConceptUpdateTxt"].Value);
            await Task.Run(() => ClusterManager.UpdateClusterConceptByRiskObjectId(scenarioId, regenerate: false));
            DialogService.Close();

            ReloadTreeAndTabs();
        }

        private async Task RegenerateClusterConceptByScenario(int scenarioId)
        {
            DialogService.Close();
            BusyDialog(Localizer["CvmRefreshingClustConceptTxt"].Value);
            await Task.Run(() => ClusterManager.UpdateClusterConceptByScenarioId(scenarioId, regenerate: true));
            DialogService.Close();

            ReloadTreeAndTabs();
        }

        private async Task RegenerateClusterConceptByRiskObject(int riskObjectId)
        {
            DialogService.Close();
            BusyDialog(Localizer["CvmRefreshingClustConceptTxt"].Value);
            await Task.Run(() => ClusterManager.UpdateClusterConceptByRiskObjectId(riskObjectId, regenerate: true));
            DialogService.Close();

            ReloadTreeAndTabs();
        }

        private async Task RegenerateTaskPlan()
        {
            DialogService.Close();
            BusyDialog(Localizer["CvmRefreshClustPlanTxt"].Value);
            await Task.Run(() => ClusterManager.RegenerateTaskPlan());
            DialogService.Close();

            ReloadTreeAndTabs();
        }
        private void BusyDialog(string message)
        {
            DialogService.Open<BusyDialog>(Localizer["CvmLoadingTxt"].Value, new Dictionary<string, object> {{"Text", message}},
                new DialogOptions {Width = "400px", Resizable = false, Draggable = true });
        }

        private void ErrorDialog(string message)
        {
            DialogService.Open<InformationDialog>("Error",
                new Dictionary<string, object>
                {
                    {"DialogContent", message}
                });
        }

        private void ReloadTreeAndTabs()
        {
            SelectedCluster = ClusterManager.GetCluster(SelectedCluster.Id);
            SelectedTasks = SelectedCluster.Tasks.Select(x => Mapper.Map<TaskModel, ClusterTaskModel>(x)).ToList();
            ClusterTaskPlanModels = ClusterManager.GetClusterTaskPlanWithExtraProperties(SelectedCluster.Id);
            ClusterList = ClusterManager.GetClustersForScenario(SelectedScenario);

            ClusterTree.Initialize(ClusterManager.GetTreeViewForClusters(SelectedScenario));
            SelectActiveNodeInTree();
            TreeTab?.Reload();

            ClusterTabs?.Reload();
            ClusterDataTabs?.Reload();
            DetailsTab?.Reload();
            UpdatePaginator();
        }

        #endregion

        #region paginator

        public int GetInitialPaginatorValue()
        {
            var node = ClusterTree.GetClusterTreeNode(SelectedCluster?.Id ?? ClusterId ?? 0);
            var nodes = ClusterTree.GetFlattenedNodes();

            return nodes.IndexOf(node);
        }

        public int GetClusterCount() => ClusterTree.GetFlattenedNodes().Count;

        public void PaginatorCallback(int clusterIndex)
        {
            var clusterId = ClusterTree.GetFlattenedNodes().Skip(clusterIndex).FirstOrDefault()?.Source?.Id;

            if (clusterId != null)
            {
                var node = ClusterTree.GetFlattenedNodes().Skip(clusterIndex).FirstOrDefault();

                if (node != null)
                    ClickTreeNode(node);

                SelectActiveNodeInTree();
            }
        }

        private void UpdatePaginator()
        {
            if (Paginator != null)
            {
                var value = GetInitialPaginatorValue();
                Paginator.SetCurrentExternally(value);
            }
        }

        #endregion
    }
}
