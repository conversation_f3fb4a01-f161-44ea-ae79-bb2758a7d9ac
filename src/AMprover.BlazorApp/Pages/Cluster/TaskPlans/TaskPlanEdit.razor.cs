using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System.Threading.Tasks;
using AMprover.BlazorApp.Enums;

namespace AMprover.BlazorApp.Pages.Cluster.TaskPlans;

public partial class TaskPlanEdit
{
    [Inject] protected ILogger<TaskPlanEdit> Logger { get; set; } = default!;
    [Inject] protected TooltipService TooltipService { get; set; } = default!;
    [Inject] protected IGlobalDataService GlobalDataService { get; set; } = default!;
    [Inject] protected DialogService DialogService { get; set; } = default!;
    [Inject] protected IClusterManager ClusterManager { get; set; } = default!;
    [Inject] protected ILookupManager LookupManager { get; set; } = default!;
    [Inject] protected IDropdownManager DropdownManager { get; set; } = default!;

    [Parameter] public int TaskPlanId { get; set; }
    [Parameter] public int? ClusterId { get; set; }
    [Parameter] public EventCallback<ClusterTaskPlanModel> Callback { get; set; }

    public ClusterTaskPlanModel TaskPlan { get; set; }
    public string ErrorText { get; set; }
    public bool ShowError { get; set; }
    private Dictionary<int, string> Clusters { get; set; } = new();
    private Dictionary<int, string> Assets { get; set; } = new();
    private Dictionary<int, string> Risks { get; set; } = new();
    private Dictionary<int, string> Tasks { get; set; } = new();
    private Dictionary<int, string> CommonTasks { get; set; } = new();
    private Dictionary<int, string> Objects { get; set; } = new();
    private Dictionary<int, string> IntervalUnits { get; set; } = new();
    private Dictionary<int, string> ExecuteStatus { get; set; } = new();

    private EntityEditorMode EditorMode
    {
        get
        {
            return TaskPlanId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    protected override void OnInitialized()
    {
        Clusters = DropdownManager.GetClusterDict();
        Tasks = DropdownManager.GetTasksDict();
        Risks = DropdownManager.GetRisksDict();
        CommonTasks = DropdownManager.GetCommonTasksDict();
        Objects = DropdownManager.GetObjectsDict();
        Assets = DropdownManager.GetAssetDict();
        IntervalUnits = DropdownManager.GetIntervalUnitDict();
        ExecuteStatus = DropdownManager.GetExecuteStatusDict();

        switch (EditorMode)
        {
            case EntityEditorMode.Create:
                TaskPlan = new ClusterTaskPlanModel { Id = TaskPlanId, ClusterId = ClusterId };
                break;
            default:
                TaskPlan = ClusterManager.GetClusterTaskPlan(TaskPlanId);
                break;
        }

        base.OnInitialized();
    }

    private async Task ValidTaskSubmitted(ClusterTaskPlanModel taskPlan)
    {
        await CreateOrUpdateObjectAndNavigate(taskPlan);
    }

    private async Task CreateOrUpdateObjectAndNavigate(ClusterTaskPlanModel taskPlan)
    {
        TaskPlan = ClusterManager.UpdateClusterTaskPlan(taskPlan);
        Logger.LogInformation($"Creating a {nameof(ClusterTaskPlanModel)} succeeded. Name = {taskPlan.Risk?.Name}, ID = {taskPlan.Id}.");
        await Callback.InvokeAsync(TaskPlan);
        DialogService.Close();
    }

    private void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ErrorText = "Update NOT executed, invalid form submitted";
        Logger.LogWarning($"Invalid {EditorMode} form submitted for {nameof(ClusterTaskPlanModel)} with Id {TaskPlanId}.");
        Logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }
}
