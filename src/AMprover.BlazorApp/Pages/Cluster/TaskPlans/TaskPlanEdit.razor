﻿@page "/cluster/taskplan/{TaskPlanId:int}"

@if (TaskPlan != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem="ClusterTaskPlanModel" Data=@TaskPlan Submit=@ValidTaskSubmitted OnInvalidSubmit=@InvalidTaskSubmitted>
        <DataAnnotationsValidator/>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput ReadOnly="true" Label="Id" @bind-Value="@TaskPlan.Id"/>
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Cluster" Name="Cluster" AllowFiltering="true" class="form-control"
                                Data=@Clusters.ToNullableDictionary()
                                @bind-Value=@TaskPlan.ClusterId />
                <RadzenRequiredValidator DefaultValue=0 Component="Cluster"/>
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Risk" Name="Risk" AllowFiltering="true" class="form-control"
                                Data=@Risks.ToNullableDictionary()
                                @bind-Value=@TaskPlan.RiskId />
                <RadzenRequiredValidator DefaultValue=0 Component="Risk"/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Task" Name="Task" AllowFiltering="true" class="form-control"
                                Data=@Tasks.ToNullableDictionary()
                                @bind-Value=@TaskPlan.TaskId />
                <RadzenRequiredValidator DefaultValue=0 Component="Task"/>
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Object" Name="Object" AllowFiltering="true" class="form-control"
                                Data=@Objects.ToNullableDictionary()
                                @bind-Value=@TaskPlan.ObjectId />
                <RadzenRequiredValidator DefaultValue=0 Component="Object"/>
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown Label="Common task" AllowFiltering="true" class="form-control"
                                Data=@CommonTasks.ToNullableDictionary()
                                @bind-Value=@TaskPlan.CommonTaskId />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F0" Label="Quality score" @bind-Value=@TaskPlan.QualityScore/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="Cluster cost per unit" @bind-Value=@TaskPlan.ClusterCostPerUnit/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="Units" @bind-Value=@TaskPlan.SiUnits/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput  Format="c2" Label="Cluster costs" @bind-Value=@TaskPlan.ClusterCosts/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="DownTime" @bind-Value=@TaskPlan.DownTime/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="Duration" @bind-Value=@TaskPlan.Duration/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput  Format="c2" Label="Tool costs" @bind-Value=@TaskPlan.ToolCosts/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Label="Priority" @bind-Value=@TaskPlan.Priority/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Format="F2" Label="Sequence" @bind-Value=@TaskPlan.Sequence/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput  Label="Slack" @bind-Value=@TaskPlan.Slack/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Label="Shift start date" @bind-Value=@TaskPlan.ShiftStartDate/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput  Label="Shift end date" @bind-Value=@TaskPlan.ShiftEndDate/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <label>Asset:</label>
                <AMproverDictionaryDropdown Name="Task" AllowFiltering="true" class="form-control"
                                Data=@Assets.ToNullableDictionary()
                                @bind-Value=@TaskPlan.AssetId />
                <RadzenRequiredValidator DefaultValue=0 Component="Task"/>
            </div>
            <div class="col-sm-4">
                <label>Execute status:</label>
                <AMproverDictionaryDropdown Name="Object" AllowFiltering="true" class="form-control"
                                Data=@ExecuteStatus.ToNullableDictionary()
                                @bind-Value=@TaskPlan.ExecuteStatus />
                <RadzenRequiredValidator DefaultValue=0 Component="Object"/>
            </div>
            <div class="col-sm-4">
                <label>Slack interval type:</label>
                <AMproverDictionaryDropdown Name="Object" AllowFiltering="true" class="form-control"
                                Data=@IntervalUnits.ToNullableDictionary()
                                @bind-Value=@TaskPlan.SlackIntervalType />
                <RadzenRequiredValidator DefaultValue=0 Component="Object"/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <div class="form-group">
                    <label>Date generated:</label>
                    <RadzenDatePicker TValue="DateTime?" ShowTime="true" ShowTimeOkButton="true" Disabled="true" @bind-Value=@TaskPlan.DateGenerated  />
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label>Date executed:</label>
                    <RadzenDatePicker TValue="DateTime?" ShowTime="true" ShowTimeOkButton="true" @bind-Value=@TaskPlan.DateExecuted />
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label>Execution date:</label>
                    <RadzenDatePicker TValue="DateTime?" ShowTime="true" ShowTimeOkButton="true" @bind-Value=@TaskPlan.ExecutionDate  />
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Reference Id:</label>
                    <AMproverTextBox Name="ReferenceId" @bind-Value=TaskPlan.ReferenceId class="form-control"/>
                </div>
            </div>
            <div class="col-sm-5">
                <div class="row">
                    <div class="col-sm-7">
                        <label>Interruptable:</label>
                    </div>
                    <div class="col-sm-5">
                        <AMproverCheckbox class="form-control" @bind-Value="@TaskPlan.Interruptable" CssClass="e-primary" TValue="bool?"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-7">
                        <label>Use last date executed:</label>
                    </div>
                    <div class="col-sm-5">
                        <AMproverCheckbox class="form-control" @bind-Value="@TaskPlan.UseLastDateExecuted" CssClass="e-primary" TValue="bool?"/>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <label>Remarks</label>
                    <AMproverTextArea Name="Remarks" @bind-Value=@TaskPlan.Remarks class="form-control"/>
                </div>
            </div>
        </div>
        <br/>
        <RadzenButton type="submit" class="btn btn-primary" Text="Save" Disabled=!GlobalDataService.CanEdit/>
    </Radzen.Blazor.RadzenTemplateForm>
}