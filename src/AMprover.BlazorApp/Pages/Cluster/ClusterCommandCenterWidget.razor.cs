using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.Cluster;

public partial class ClusterCommandCenterWidget
{
    [Inject] private ILogger<ClusterCommandCenterWidget> Logger { get; set; }
    [Inject] private IStringLocalizer<ClusterPage> Localizer { get; set; }

    [Parameter]
    public EventCallback Callback { get; set; }

    [Parameter]
    public EventCallback<int> CallbackInt { get; set; }

    [Parameter]
    public bool ExtraConfirmation { get; set; }

    [Parameter]
    public string ButtonText { get; set; } = "Start";

    [Parameter]
    public string ValidationText { get; set; } = "I AGREE";

    [Parameter]
    public string ConfirmationText { get; set; } = "Are you sure?";

    [Parameter]
    public Dictionary<int, string> Options { get; set; }

    private string ConfirmText { get; set; } = string.Empty;

    private string ErrorMessage { get; set; }

    private int SelectedId { get; set; }

    private async Task ConfirmProcessing()
    {
        if (ExtraConfirmation && ConfirmText.Trim() != ValidationText)
        {
            ErrorMessage = "The confirmation is incorrect.";
        }
        else
        {
            if (Callback.HasDelegate)
            {
                await Callback.InvokeAsync();
            }
            else if (CallbackInt.HasDelegate)
            {
                await CallbackInt.InvokeAsync(SelectedId);
            }

            ConfirmText = string.Empty;
        }
    }
}