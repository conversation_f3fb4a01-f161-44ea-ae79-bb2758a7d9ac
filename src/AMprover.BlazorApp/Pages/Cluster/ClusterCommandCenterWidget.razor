<div>
    <div class="form-group my-3">
        @ConfirmationText
    </div>

    @if (Options?.Any() == true)
    {
        <AMDropdown 
            @bind-Value=@SelectedId
            Data=Options
            Label="Options"
            Required=true 
            AllowFiltering=true />
    }

    @if (ExtraConfirmation)
    {
        <div class="form-group my-3">
            <label>@Localizer["CCCWidgetTxtEnter"] <span class="select-on-click"><b>@ValidationText</b></span> @Localizer["CCCWidgetTxtWarning"]</label>
            <RadzenTextBox @bind-Value=@ConfirmText Name="Name" class="form-control"/>
        </div>
    }

    @if (!string.IsNullOrWhiteSpace(ErrorMessage))
    {
        <div class="alert alert-danger" role="alert">
            @ErrorMessage
        </div>
    }

    <RadzenButton ButtonStyle=ButtonStyle.Danger Text="@ButtonText" Click=@ConfirmProcessing Disabled=@(Options?.Any() == true && SelectedId == 0) />

</div>
