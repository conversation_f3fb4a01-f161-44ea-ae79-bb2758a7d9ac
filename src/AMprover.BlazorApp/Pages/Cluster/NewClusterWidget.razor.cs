using System;
using System.Collections.Generic;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using AutoMapper;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;

namespace AMprover.BlazorApp.Pages.Cluster;

public partial class NewClusterWidget
{
    [Inject] private ILogger<NewClusterWidget> Logger { get; set; }
    [Inject] private IMapper Mapper { get; set; }
    [Inject] private IClusterManager ClusterManager { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IStringLocalizer<NewClusterWidget> Localizer { get; set; }

    [Parameter] public int? PartOfCluster { get; set; }

    [Parameter] public Action<ClusterModel> CallBack { get; set; }

    private string Name { get; set; }

    private Dictionary<int, string> ClusterDict { get; set; } = new();

    private bool IsBusy { get; set; }

    private ClusterModel NewCluster { get; set; } = new();

    protected override void OnInitialized()
    {
        ClusterDict = DropdownManager.GetClusterDict();
    }

    private void CreateNewCluster()
    {
        if (string.IsNullOrWhiteSpace(Name)) return;

        IsBusy = true;

        try
        {
            //reset if done multiple times in a row
            NewCluster = new ClusterModel();

            if (PartOfCluster.HasValue)
            {
                //map all data
                var parentCluster = ClusterManager.GetCluster(PartOfCluster.Value);
                Mapper.Map(parentCluster, NewCluster);

                NewCluster.PartOf = PartOfCluster;
                NewCluster.Level = parentCluster.Level + 1;
                NewCluster.ScenarioId = parentCluster.ScenarioId;
                NewCluster.RiskObjectId = parentCluster.RiskObjectId;
            }

            //Set parameters to create new cluster.
            NewCluster.Id = 0;
            NewCluster.Name = Name;

            NewCluster = ClusterManager.UpdateCluster(NewCluster);

            CallBack?.Invoke(NewCluster);
            DialogService.Close();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating new cluster");
        }
        finally
        {
            IsBusy = false;
        }
    }
}