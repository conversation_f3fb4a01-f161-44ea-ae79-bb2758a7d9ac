using System.Collections.Generic;
using AMprover.BlazorApp.Enums;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;

namespace AMprover.BlazorApp.Pages.Cluster.Costs;

public partial class CostEdit
{
    [Inject] private ILogger<CostEdit> Logger { get; set; }
    [Inject] private IClusterManager ClusterManager { get; set; }
    [Inject] private GlobalDataService GlobalDataService { get; set; }
    [Inject] private IPortfolioSetupManager PortfolioSetupManager { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IStringLocalizer<CostEdit> Localizer { get; set; }

    [Parameter] public int CostId { get; set; }
    [Parameter] public int? TaskId { get; set; }
    [Parameter] public int? ClusterId { get; set; }
    [Parameter] public EventCallback<ClusterModel> Callback { get; set; }

    private ClusterCostModel Cost { get; set; }
    private CommonCostModel SelectedCommonCost { get; set; }
    public string ErrorText { get; set; }
    public bool ShowError { get; set; }
    private Dictionary<int, string> Clusters { get; set; } = new();
    private Dictionary<int, string> Tasks { get; set; } = new();
    private Dictionary<int, string> CommonCosts { get; set; } = new();
    private string SelectedMrbName { get; set; } = "Please link task first";
    private bool HideUnits { get; set; }
    private EntityEditorMode EditorMode { get; set; }

    protected override void OnInitialized()
    {
        Clusters = DropdownManager.GetClusterDict();
        Tasks = ClusterId.HasValue
            ? DropdownManager.GetTasksForClusterDict(ClusterId.Value)
            : DropdownManager.GetTasksDict();
        CommonCosts = DropdownManager.GetCommonCostDict();

        EditorMode = CostId == 0 ? EntityEditorMode.Create : EntityEditorMode.Update;

        switch (EditorMode)
        {
            case EntityEditorMode.Create:
                Cost = new ClusterCostModel
                    {Id = CostId, TaskId = TaskId, ClusterId = ClusterId, IsCommonTaskCost = true};
                HideUnits = true;
                if (TaskId.HasValue) OnTaskChange(TaskId);
                break;
            default:
                Cost = ClusterManager.GetClusterCost(CostId);
                HideUnits = Cost.HideUnits();
                if (Cost.TaskId.HasValue) OnTaskChange(Cost.TaskId.Value);
                break;
        }
    }

    private void OnTaskChange(object value)
    {
        if (value is int i)
        {
            SelectedMrbName = ClusterManager.GetRiskNameByTaskId(i);
        }
    }

    private void OnCommonCostChange(object value)
    {
        if (value is not int i) return;
        SelectedCommonCost = PortfolioSetupManager.GetCommonCost(i);

        if (SelectedCommonCost == null) return;
        if (SelectedCommonCost.Price.HasValue)
            Cost.Price = SelectedCommonCost.Price.Value;
        Cost.Quantity = SelectedCommonCost.Number;
        Cost.Units = SelectedCommonCost.Units;
        Cost.CalculationType = SelectedCommonCost.CalculationType;
        Cost.Type = SelectedCommonCost.Type;

        //Hide units field if the common cost has p*q instead of p*q*u
        HideUnits = SelectedCommonCost.HideUnits();

        RecalculateCost();
    }

    private void RecalculateCost()
    {
        Cost.CalculateCosts();
    }

    private void ValidTaskSubmitted(ClusterCostModel cost)
    {
        if (SelectedCommonCost != null)
        {
            cost.Type = SelectedCommonCost.Type;
        }

        CreateOrUpdateObjectAndNavigate(cost);
    }

    private void InvalidTaskSubmitted()
    {
        // Handle invalid submission if needed
    }

    private void CreateOrUpdateObjectAndNavigate(ClusterCostModel cost)
    {
        Cost = ClusterManager.UpdateClusterCost(cost);
        var cluster = new ClusterModel();

        if (cost.ClusterId.HasValue)
            cluster = ClusterManager.SyncClusterCosts(cost.ClusterId.Value);

        Logger.LogInformation($"Creating a {nameof(ClusterCostModel)} succeeded. Name = {cost.Task?.Risk?.Name}, ID = {cost.Id}.");
        Callback.InvokeAsync(cluster);
        DialogService.Close();
    }
}