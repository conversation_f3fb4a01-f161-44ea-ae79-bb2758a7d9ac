﻿@page "/cluster/costs/{CostId:int}"

@if (Cost != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem="ClusterCostModel" Data=@Cost Submit=@ValidTaskSubmitted InvalidSubmit=@InvalidTaskSubmitted>
        <div class="row">
            <div class="col-sm-12">
                <AMDropdown Label=@Localizer["CeWCommonCostLbl"]
                            Data=@CommonCosts
                            @bind-Value=@Cost.CommonCostId
                            Change=@(args => OnCommonCostChange(args))
                            Required=true AllowClear="true" AllowFiltering="true" />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverNumberInput TValue="decimal?" Format="F2" Label=@Localizer["CeWQuantityLbl"] Change=@RecalculateCost @bind-Value=@Cost.Quantity/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput TValue="decimal" Format="c2" Label=@Localizer["CeWPriceLbl"] Change=@RecalculateCost @bind-Value=@Cost.Price/>
            </div>
            <div class="col-sm-4">
                <AMproverNumberInput ReadOnly="true" Format="F2" Label=@Localizer["CeWCostsLbl"] @bind-Value=@Cost.Cost/>
            </div>
        </div>
        <div class="row">
            @if (!HideUnits)
            {
                <div class="col-sm-4">
                    <AMproverNumberInput Format="F2" Label=@Localizer["CeWUnitsLbl"] @bind-Value=@Cost.Units/>
                </div>
            }
            <div class="col-sm-4">
                <AMproverTextBox ReadOnly=true Label="Calculation type" class="form-control" @bind-Value=@Cost.CalculationType />
            </div>
            <div class="col-sm-4">
                <label>@Localizer["CeWCcDivisionLbl"]:</label>
                <AMproverCheckbox @bind-Value=@Cost.IsCommonTaskCost TValue=bool?/>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <div class="form-group">
                    <label>@Localizer["CeWActionLbl"]:</label>
                    <AMDropdown AllowClear="true" AllowFiltering="true" 
                                Data=@Tasks.ToNullableDictionary()
                                @bind-Value="@Cost.TaskId" 
                                Change=@(args => OnTaskChange(args))/>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label>@Localizer["CeWMrbNameLbl"]:</label>
                    <p class="form-control readonly">@SelectedMrbName</p>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <AMDropdown AllowClear="false" AllowFiltering="true" Required=true
                                Label=@Localizer["CeWClustNameLbl"]
                                Data=@Clusters.ToNullableDictionary()
                                @bind-Value="@Cost.ClusterId"/>
                </div>
            </div>
        </div>
        <br/>
        <RadzenButton type="submit" class="btn btn-primary" Text=@Localizer["CeWSaveBtn"] Disabled=!GlobalDataService.CanEdit/>
    </Radzen.Blazor.RadzenTemplateForm>
}
