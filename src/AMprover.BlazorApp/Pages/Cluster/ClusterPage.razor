﻿@page "/cluster/{ClusterId:int?}"

<h2>@Localizer["ClustHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
                <NavLink class="breadcrumb-item" aria-current="page" Match="NavLinkMatch.All">
                    Cluster
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-2 text-center">
        <Paginator
            Initial=GetInitialPaginatorValue()
            Count=GetClusterCount()
            CallBack=PaginatorCallback
            @ref=Paginator />
    </div>
    <div class="col 5 text-right">
        <RadzenSplitButton Disabled=!GlobalDataService.CanEdit Text=@Localizer["ClustClusterConceptTab"] Click=@(args => OnClusterConceptClick(args)) ButtonStyle=ButtonStyle.Secondary class="float-right my-2 mx-1">
            <ChildContent>

                <RadzenSplitButtonItem Text=@Localizer["ClustUpdateClustConceptByScenarioBtn"] Value="1" />
                <RadzenSplitButtonItem Text=@Localizer["ClustUpdateClustConceptByRiskObjectBtn"] Value="2" />

                <RadzenSplitButtonItem Text=@Localizer["ClustRegenerateClustConceptByScenarioBtn"] Value="3" />
                <RadzenSplitButtonItem Text=@Localizer["ClustRegenerateClustConceptByRiskObjectBtn"] Value="4" />

            </ChildContent>
        </RadzenSplitButton>
        <RadzenButton Disabled=!GlobalDataService.CanEdit Text=@Localizer["ClustRegenerateTaskPlanBtn"] class="float-right my-2 mx-1" Click=@OnTaskPlanClick ButtonStyle=ButtonStyle.Secondary />
        <RadzenButton Disabled=!GlobalDataService.CanEdit Icon="add_circle_outline" Text=@Localizer["ClustNewBtn"] class="float-right my-2 mx-1" Click=@AddNewCluster ButtonStyle=ButtonStyle.Secondary />
        <RadzenButton Disabled=!GlobalDataService.CanEdit Icon="save" Text=@Localizer["ClustSaveBtn"] Click=@SaveCluster class="float-right my-2 mx-1" ButtonStyle=ButtonStyle.Secondary />
    </div>
    <Information class="float-right my-2 ml-1 mr-2" DialogTitle=@Localizer["ClustMenuTitle"] DialogContent=@Localizer["ClustMenuTxt"] />
</div>

<div class="row">
    <div class="col-sm-3">
        <RadzenTabs @ref=@TreeTab TabPosition=TabPosition.Right class="hide-tabs-nav">
            <Tabs>
                <RadzenTabsItem>

                    <AMDropdown
                        Label="Scenario"
                        Data=@ScenarioDict
                        @bind-Value=@SelectedScenario
                        Change=@(() => ChangeScenario())/>

                    <TreeComponent
                        TItem=ClusterTreeObject
                        Treeview=ClusterTree
                        NodeClickCallback=ClickTreeNode
                        DeleteCallback=@(args => DeleteClusterFromTree(args)) />

                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
    <div class="col-sm-9">
        <RadzenTabs @ref=@DetailsTab TabPosition=TabPosition.Right class="hide-tabs-nav">
            <Tabs>
                <RadzenTabsItem>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group neg-margin-small">
                                        <label>Id:</label>
                                        <span class="form-control readonly neg-margin-small">@SelectedCluster.Id</span>
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <AMproverTextBox Disabled=!GlobalDataService.CanEdit MaxLength="50" @bind-Value=@SelectedCluster.Name Label=@Localizer["ClustHeaderTxt"] />
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4">
                                </div>
                                <div class="col-sm-2">
                                    <AMproverNumberInput Disabled=!GlobalDataService.CanEdit Label=@Localizer["ClustIntervalLbl"] @bind-Value=@SelectedCluster.Interval />
                                </div>
                                <div class="col-sm-6">
                                    <AMDropdown Disabled=!GlobalDataService.CanEdit AllowFiltering="true"
                                                Data=@IntervalDict.ToNullableDictionary()
                                                @bind-Value="@SelectedCluster.IntervalUnit"
                                                Label=@Localizer["ClustIntervalUnitLbl"] />
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4">
                                    <AMproverDictionaryDropdown Data=@GetClusterLevelDropdown()
                                                                Label=@Localizer["ClustPartLevelLbl"]
                                                                @bind-Value=@SelectedCluster.Level
                                                                Change=@ChangeClustLevel />
                                </div>
                                <div class="col-sm-8">
                                    <AMDropdown Disabled=!GlobalDataService.CanEdit AllowFiltering="true"
                                                Data=@GetClusterPartOfDropdown()
                                                @bind-Value="@SelectedCluster.PartOf"
                                                Label=@Localizer["ClustPartNameLbl"] />
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="overview-frame neg-margin-Xlarge">
                                <div class="row">
                                    <div class="col-6">
                                        <AMproverTextBox ReadOnly="true" @bind-Value=@SelectedCluster.ShortKey MaxLength="50" Label=@Localizer["ClustShortKeyLbl"] />
                                    </div>
                                    @if (IsSapaView)
                                    {
                                    <div class="col-6">
                                        <AMproverNumberInput Disabled=!GlobalDataService.CanEdit Label=@Localizer["ClustStartFrom"] class="mr-4" @bind-Value=@SelectedCluster.ShiftStartDate />
                                    </div>
                                    }
                                </div>
                                <div class="row">
                                    @if (IsSapaView)
                                    {
                                    <div class="col-6">
                                    </div>
                                    <div class="col-6">
                                        <AMproverNumberInput Style="background-color: red" Format="c2" Label=@Localizer["ClustBudgetLbl"] @bind-Value=@SelectedCluster.Budget />
                                    </div>
                                    }
                                    else
                                    {
                                    <div class="col-sm-6">
                                        <div class="row">
                                            <div class="col-10">
                                                <AMproverNumberInput Disabled=!GlobalDataService.CanEdit Label=@Localizer["ClustDownTimeLbl"] class="mr-4" @bind-Value=@SelectedCluster.DownTime />
                                            </div>
                                            <div class="col-2">
                                                <div class="form-group neg-margin">
                                                    <br/>
                                                    <RadzenButton Disabled=!GlobalDataService.CanEdit Icon="get_app" class="mt-2 float-right" Click=@ProcessDownTime />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="row">
                                            <div class="col-10">
                                                <AMproverNumberInput Disabled=!GlobalDataService.CanEdit Label=@Localizer["ClustDurationLbl"] @bind-Value=@SelectedCluster.Duration />
                                            </div>
                                            <div class="col-2">
                                                <div class="form-group neg-margin">
                                                    <br/>
                                                    <RadzenButton Disabled=!GlobalDataService.CanEdit Icon="get_app" class="mt-2 float-right" Click=@ProcessDownDuration />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    }
                                </div>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <AMDropdown Disabled=!GlobalDataService.CanEdit AllowClear="true" AllowFiltering="true"
                                                    Data=@StatusDict.ToNullableDictionary()
                                                    @bind-Value="@SelectedCluster.Status"
                                                    Label=@Localizer["ClustClusterStatusLbl"] />
                                    </div>
                                    <div class="col-sm-6">
                                        <AMproverNumberInput ReadOnly="true" Format="c2" Label=@Localizer["ClustTotalCostUnitLbl"] @bind-Value=@SelectedCluster.TotalCmnCost />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <RadzenTabs @ref=@ClusterTabs>
                                <Tabs>
                                    <RadzenTabsItem Text=@Localizer["ClustPropTab"]>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <AMproverTextArea Disabled=!GlobalDataService.CanEdit @bind-Value=@SelectedCluster.Description Cols="30" Rows="3" Label=@Localizer["ClustDescriptionLbl"] />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-3">
                                                <AMDropdown Disabled=!GlobalDataService.CanEdit AllowFiltering="true"
                                                            Data=@InitiatorDict.ToNullableDictionary()
                                                            @bind-Value=@SelectedCluster.Initiator
                                                            Label=@Localizer["ClustInitiatorLbl"] />
                                            </div>
                                            <div class="col-sm-3">
                                                <AMDropdown Disabled=!GlobalDataService.CanEdit AllowFiltering="true"
                                                            Data=@ExecutorDict.ToNullableDictionary()
                                                            @bind-Value=@SelectedCluster.Executor Label=@Localizer["ClustExecutorLbl"] />
                                            </div>
                                            <div class="col-sm-6">
                                                <AMproverTextBox Disabled=!GlobalDataService.CanEdit @bind-Value=@SelectedCluster.Responsible Label=@Localizer["ClustResponsibleLbl"] />
                                            </div>
                                        </div>
                                    </RadzenTabsItem>
                                    <RadzenTabsItem Text=@Localizer["ClustExtraTab"]>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <AMproverTextBox Disabled=!GlobalDataService.CanEdit MaxLength="12" Label="Site ID" @bind-Value=@SelectedCluster.SiteId />
                                            </div>
                                            <div class="col-sm-3">
                                                <AMproverTextBox Disabled=!GlobalDataService.CanEdit MaxLength="50" Label=@Localizer["ClustLocationIdLbl"] @bind-Value=@SelectedCluster.Location />
                                            </div>
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Disabled=!GlobalDataService.CanEdit Label=@Localizer["ClustShiftStartDateLbl"] @bind-Value=@SelectedCluster.ShiftStartDate />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <AMproverTextBox Disabled=!GlobalDataService.CanEdit MaxLength="12" Label=@Localizer["ClustOrganizationIdLbl"] @bind-Value=@SelectedCluster.OrgId />
                                            </div>
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Disabled=!GlobalDataService.CanEdit Label=@Localizer["ClustSequenceLbl"] @bind-Value=@SelectedCluster.Sequence />
                                            </div>
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Disabled=!GlobalDataService.CanEdit Label=@Localizer["ClustShiftEndDateLbl"] @bind-Value=@SelectedCluster.ShiftEndDate />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <AMproverTextBox Disabled=!GlobalDataService.CanEdit MaxLength="30" Label=@Localizer["ClustTemplateTypeLbl"] @bind-Value=@SelectedCluster.TemplateType />
                                            </div>
                                            <div class="col-sm-3">
                                                <AMproverNumberInput Disabled=!GlobalDataService.CanEdit Label=@Localizer["ClustPriorityLbl"] @bind-Value=@SelectedCluster.Priority />
                                            </div>
                                            <div class="col-sm-3 mt-4">
                                                <Radzen.Blazor.RadzenCheckBox Disabled=!GlobalDataService.CanEdit  @bind-Value="@SelectedCluster.Interruptable" TValue=bool? /> @Localizer["ClustInterruptableLbl"]
                                            </div>
                                        </div>
                                    </RadzenTabsItem>
                                    <RadzenTabsItem Text=@Localizer["ClustConceptCostsTab"]>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <AMproverNumberInput ReadOnly="true" Format="c2"  Label=@Localizer["ClustDiscUnitLbl"] @bind-Value=@SelectedCluster.DisciplineCosts />
                                            </div>
                                            <div class="col-sm-6">
                                                <AMproverNumberInput ReadOnly="true" Format="c2"  Label=@Localizer["ClustSharedCostUnitLbl"] @bind-Value=@SelectedCluster.SharedCosts />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <AMproverNumberInput ReadOnly="true" Format="c2"  Label=@Localizer["ClustEnergyUnitLbl"] @bind-Value=@SelectedCluster.EnergyCosts />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <AMproverNumberInput ReadOnly="true" Format="c2"  Label=@Localizer["ClustMaterialUnitLbl"] @bind-Value=@SelectedCluster.MaterialCosts />
                                            </div>
                                            <div class="col-sm-6">
                                                <AMproverNumberInput ReadOnly="true" Format="c2"  Label=@Localizer["ClustTotEstCostLbl"] @bind-Value=@SelectedCluster.EstTaskCosts />
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <AMproverNumberInput ReadOnly="true" Format="c2"  Label=@Localizer["ClustToolUnitLbl"] @bind-Value=@SelectedCluster.ToolCosts />
                                            </div>
                                            <div class="col-sm-6">
                                                <AMproverNumberInput ReadOnly="true" Format="c2"  Label=@Localizer["ClustTotalCostUnitLbl"] @bind-Value=@SelectedCluster.TotalCmnCost />
                                            </div>
                                        </div>
                                    </RadzenTabsItem>
                                    <RadzenTabsItem Text=@Localizer["ClustRemarksTab"]>
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <AMproverTextArea Disabled=!GlobalDataService.CanEdit @bind-Value=@SelectedCluster.Remark Cols="30" Rows="3" Label=@Localizer["ClustRemarksTab"] />
                                            </div>
                                        </div>
                                    </RadzenTabsItem>
                                    <RadzenTabsItem Text=@Localizer["ClustUsedByTab"]>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label>@Localizer["ClustCreatedByLbl"]:</label>
                                                    <label>@SelectedCluster.InitiatedBy</label>
                                                </div>
                                            </div>
                                            @if (SelectedCluster.DateInitiated != null)
                                            {
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label>@Localizer["ClustOnLbl"]</label>
                                                    <label>@SelectedCluster.DateInitiated.Value.ToString("dd-MM-yyyy hh:mm")</label>
                                                </div>
                                            </div>
                                            }
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label>@Localizer["ClustModifiedByLbl"]:</label>
                                                    <label>@SelectedCluster.ModifiedBy</label>
                                                </div>
                                            </div>
                                            @if (SelectedCluster.DateModified != null)
                                            {
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label>@Localizer["ClustOnLbl"]:</label>
                                                    <label>@SelectedCluster.DateModified.Value.ToString("dd-MM-yyyy hh:mm")</label>
                                                </div>
                                            </div>
                                            }
                                        </div>
                                    </RadzenTabsItem>
                                </Tabs>
                            </RadzenTabs>
                        </div>
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
</div>
<div class="row">
    <div class="col-sm-12">
        <RadzenTabs @ref=@ClusterDataTabs style="min-width: 98%">
            <Tabs>
                <RadzenTabsItem Text=@($"{Localizer["ClustTaskConceptTab"]} ({SelectedCluster?.Tasks?.Count})")>
                    <div class="row">
                        <div class="col-sm-12">
                            <UtilityGrid TItem=ClusterTaskModel
                                         @ref=TaskConceptGrid
                                         Data=SelectedTasks
                                         FileName=@GridNames.Cluster.PreventiveActions
                                         Interactive=true
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         RowSelectCallBack=@(args => SelectClusterTask((TaskModel) args))
                                         SaveCallback=@(args => SaveClusterTask(args))
                                         DropDownOverrides=@TaskDropdownOverrides
                                         MaxRows="25" />
                        </div>
                    </div>
                    <br/>
                    <div class="row">
                        <div class="col-sm-12">
                            <UtilityGrid TItem=ClusterCostModel
                                         @ref=ClusterCostDetailGrid
                                         Data=@(SelectedCluster.Costs.Where(x => x.TaskId.HasValue && SelectedTask != null
                                         && x.TaskId.Value == SelectedTask.Id).ToList())
                                         FileName=@GridNames.Cluster.Costs
                                         Interactive=true
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         AddNewButton=@(SelectedTask !=null)
                                         AddRowCallBack=@(_ => OpenClusterCostPopup(0))
                                         OpenPopup=@(args => OpenClusterCostPopup(args.Id))
                                         SaveCallback=@(args => SaveClusterCost(args))
                                         DeleteCallback=@(args => DeleteClusterCost(args))
                                         DropDownOverrides=@ClusterCostDropdownOverrides
                                         MaxRows="25" />
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@($"{Localizer["ClustClusterCostTab"]} ({SelectedCluster?.Costs?.Count})")>
                    <div class="row">
                        <div class="col-sm-12">
                            <UtilityGrid TItem=ClusterCostModel
                                         @ref=ClusterCostGrid
                                         Data=SelectedCluster.Costs
                                         FileName=@GridNames.Cluster.Costs
                                         Interactive=true
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         AddRowCallBack=@(_ => OpenClusterCostPopup(0))
                                         OpenPopup=@(args => OpenClusterCostPopup(args.Id))
                                         SaveCallback=@(args => SaveClusterCost(args))
                                         DeleteCallback=@(args => DeleteClusterCost(args))
                                         DropDownOverrides=@ClusterCostDropdownOverrides
                                         MaxRows="25" />
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@($"{Localizer["ClustTaskPlanTab"]} ({SelectedCluster?.TaskPlans?.Count})")>
                    <div class="row">
                        <div class="col-sm-12">
                            <UtilityGrid TItem=ClusterTaskPlanModelWithExtraTaskProperties
                                         @ref=TaskPlanGrid
                                         Data=ClusterTaskPlanModels
                                         FileName=@GridNames.Cluster.TaskPlan
                                         Interactive=false
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         DropDownOverrides=@ClusterTaskPlanDropdownOverrides
                                         MaxRows="25" />
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@($"{Localizer["ClustClustersTab"]} ({ClusterList?.Count})")>
                    <div class="row">
                        <div class="col-sm-12">
                            <UtilityGrid TItem=ClusterModel
                                         @ref=ClusterGrid
                                         Data=ClusterList
                                         FileName=@GridNames.Cluster.Clusters
                                         Interactive=true
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         SaveCallback=@(args => SaveCluster(args))
                                         MaxRows="25" />
                        </div>
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
</div>
