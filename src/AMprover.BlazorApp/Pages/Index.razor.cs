using AMprover.BlazorApp.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.JSInterop;
using System.Threading.Tasks;
using AMprover.BusinessLogic;

namespace AMprover.BlazorApp.Pages;

public partial class Index
{
    [Inject] private IConfiguration Configuration { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private CookieService CookieService { get; set; }
    [Inject] private TooltipHelper TooltipHelper { get; set; }
    [Inject] private IJSRuntime JsRuntime { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IPageNavigationManager PageNavigationManager { get; set; }

    // Element references for tooltips and navigation
    public ElementReference DataPreparation { get; set; }
    public ElementReference RiskMatrix { get; set; }
    public ElementReference ValueDriverAnalysis { get; set; }
    public ElementReference CriticalityRanking { get; set; }
    public ElementReference RcmFmecaStudy { get; set; }
    public ElementReference PmoStudy { get; set; }
    public ElementReference RamsStudy { get; set; }
    public ElementReference LccStudy { get; set; }
    public ElementReference SparePartStudy { get; set; }
    public ElementReference BuildWorkPackages { get; set; }
    public ElementReference LinkPmToAbs { get; set; }
    public ElementReference RiskOnAbs { get; set; }
    public ElementReference Summary { get; set; }
    public ElementReference Ltap { get; set; }
    public ElementReference ValueForecast { get; set; }
    public ElementReference EamUpload { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
            if (!firstRender) return;
            await CookieService.AddOrUpdateCookie(NavigationManager.Uri);
        }

    private void Navigate(string page)
    {
            var navigationItem = PageNavigationManager.GetPageUrl(page);
            NavigationManager.NavigateTo(!string.IsNullOrWhiteSpace(navigationItem) ? navigationItem : page);
        }

    public async Task OpenInNewTab(string url)
    {
            await JsRuntime.InvokeAsync<object>("open", url, "_blank");
        }

    public void ShowTooltip(ElementReference element, string text)
    {
            TooltipHelper.ShowTooltip(element, text);
        }

    public async Task HideTooltip(ElementReference element)
    {
            await TooltipHelper.HideTooltip(element);
        }
}