using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BlazorApp.Components.Pagination;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Pages.LCC;

public partial class LCCPage
{
    [Parameter] public int? LccId { get; set; }
    
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IScenarioManager ScenarioManager { get; set; }
    [Inject] private ILCCManager LccManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private IStringLocalizer<LCCPage> Localizer { get; set; }
    [Inject] private IPageNavigationManager PageNavigationManager { get; set; }
    [Inject] private IGlobalDataService GlobalDataService { get; set; }
    
    private ILogger Logger { get; set; }
    private bool Pmo { get; set; } = false;
    private string Currency => LookupManager.GetCurrency();

    private List<ScenarioModel> RiskScenarios { get; set; }
    public int SelectedScenario { get; set; }
    public TreeGeneric<LccTreeObject> LccTree { get; set; } = new();
    private LccTreeObject CurrentLcc { get; set; }
    public LccModel SelectedLcc { get; set; }
    public LccDetailModel SelectedLccDetail { get; set; }
    public string ErrorText { get; set; }
    public bool ShowError { get; set; }
    private bool LccExclude { get; set; }
    private bool ShowPmoCheckbox { get; set; }
    private bool ShowActualDates { get; set; }

    private RadzenTabs LccTabs { get; set; }
    private RadzenTabs TreeTab { get; set; }
    private RadzenTabs LccDetailsTab { get; set; }
    public RadzenChart OptimalLifeTimeGraph { get; set; }
    public RadzenChart RealExpenditureGraph { get; set; }
    private UtilityGrid<LccDetailModel> OptimalCostsGrid { get; set; }
    public UtilityGrid<LccDetailModel> OptimalCostsGridPmo { get; set; }
    private UtilityGrid<LccDetailModel> RealCostsGrid { get; set; }
    private UtilityGrid<LccEffectDetailModel> OptimalCostEffectDetailsGrid { get; set; }
    public UtilityGrid<LccEffectDetailModel> OptimalCostEffectDetailsGridPmo { get; set; }
    private UtilityGrid<LccEffectDetailModel> RealCostEffectDetailsGrid { get; set; }
    private UtilityGrid<TaskModel> TasksGrid { get; set; }
    public UtilityGrid<RiskModel> RisksGrid { get; set; }
    private Paginator Paginator { get; set; }

    private Dictionary<string, Dictionary<int, string>> TaskDropDownOverrides { get; set; }
    private Dictionary<string, Dictionary<int, string>> RiskDropDownOverrides { get; set; }

    protected override void OnInitialized()
    {
        Logger = LoggerFactory.CreateLogger<LCCPage>();
        
        RiskScenarios = ScenarioManager.GetAllScenariosSorted();

        TaskDropDownOverrides = DropdownManager.GetTaskModelDropDownOverrides();
        RiskDropDownOverrides = DropdownManager.GetRiskModelDropDownOverrides();

        if (LccId != null)
        {
            SelectedLcc = LccManager.GetLccDetailed(LccId.Value);
            SelectedScenario = SelectedLcc.ScenarioId
                ?? SelectedLcc.RiskObject?.ScenarioId
                ?? RiskScenarios.FirstOrDefault()?.Id
                ?? 0;

            LccTree.Initialize(LccManager.GetTreeViewForScenario(SelectedScenario));
            PageNavigationManager.SetSelectedScenario(SelectedScenario);

            var node = LccTree.GetLccTreeNode(LccId.Value);

            if (node != null)
                LccTree.SelectNode(node);
        }
        else
        {
            SelectedScenario = PageNavigationManager.GetSelectedScenario() ?? RiskScenarios.FirstOrDefault()?.Id ?? 0;
            SelectFirstLcc();
        }

        if (SelectedLcc != null)
        {
            SelectedLccDetail = SelectedLcc.Details.FirstOrDefault();
            ShowPmoCheckbox = SelectedLcc.RiskObject?.AnalysisType == "PMO";
        }

        ShowActualDates = true; //ToDo: link on LccStartDate>1100
    }

    private void ReloadGrids()
    {
        LccTabs?.Reload();
        TasksGrid?.Grid?.Reload();
        OptimalCostsGrid?.Grid?.Reload();
        RealCostsGrid?.Grid?.Reload();
        RealCostEffectDetailsGrid?.Grid?.Reload();
        OptimalCostEffectDetailsGrid?.Grid?.Reload();
    }

    private void SelectScenario(object args)
    {
        SelectedScenario = (int)args;
        PageNavigationManager.SetSelectedScenario(SelectedScenario);
        LccTree.Initialize(LccManager.GetTreeViewForScenario(SelectedScenario));
    }

    public void ClickTreeNode(TreeNodeGeneric<LccTreeObject> node)
    {
        CurrentLcc = node.Source;

        if (CurrentLcc.Id.HasValue)
        {
            if (SelectedLcc?.Id != CurrentLcc.Id)
            {
                SelectedLcc = LccManager.GetLccDetailed(CurrentLcc.Id.Value);
                PageNavigationManager.SavePageQueryString($"/lcc/{CurrentLcc.Id}", string.Empty);
                NavigationManager.NavigateTo($"/lcc/{CurrentLcc.Id}");
            }

            SelectedLccDetail = SelectedLcc?.Details.FirstOrDefault();
            LccTabs?.Reload();
            UpdatePaginator(CurrentLcc.Id);
        }
    }

    private void ValidLccSubmitted(EditContext editContext)
    {
        var task = (LccModel)editContext.Model;
        CreateOrUpdateObjectAndNavigate(task);
    }

    private void SaveAndReCalculateLcc()
    {
        CreateOrUpdateObjectAndNavigate(SelectedLcc);
    }

    private void CreateOrUpdateObjectAndNavigate(LccModel lcc)
    {
        SelectedLcc = LccManager.CalculateLcc(lcc);
        ReloadGrids();
        Logger.LogInformation($"Creating a {nameof(lcc)} succeeded. Name = {lcc.Name}, ID = {lcc.Id}.");
    }

    private void InvalidLccSubmitted(EditContext editContext)
    {
        ErrorText = "Update NOT executed, invalid form submitted";
        Logger.LogWarning($"Invalid form submitted for {nameof(SelectedLcc)} with Id {SelectedLcc.Id}.");
        Logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject((RiskObjectModel)editContext.Model));
    }

    private void OpenNewLccWidget()
    {
        DialogService.Open<NewLCCWidget>
        (Localizer["LccWHeaderTxt"],
            new Dictionary<string, object>
            {
                {nameof(NewLCCWidget.ScenarioId), SelectedScenario},
                {nameof(NewLCCWidget.CallBack), EventCallback.Factory.Create<int>(this, UpdateTree)}
            });
    }

    private void UpdateTree(int lccRiskObjectId)
    {
        var forceReload = SelectedLcc == null;

        SelectFirstLcc(lccRiskObjectId);
        SaveAndReCalculateLcc();

        if (forceReload)
            NavigationManager.NavigateTo(NavigationManager.Uri, true);
    }

    private void DeleteLccFromTree(LccTreeObject lccTreeObject)
    {
        if (!(lccTreeObject.Id > 0))
            return;

        LccManager.DeleteLcc(lccTreeObject.Id.Value);

        if (lccTreeObject.Id == SelectedLcc.Id)
            SelectFirstLcc();
    }

    public int GetInitialPaginatorValue()
    {
        var node = LccTree.GetLccTreeNode(LccId ?? 0);
        return GetLccTreeNodes().IndexOf(node);
    }

    public void PaginatorCallback(int lccIndex)
    {
        var treeNode = GetLccTreeNodes()?.Skip(lccIndex)?.FirstOrDefault();

        if (treeNode != null)
        {
            LccTree.SelectNode(treeNode);
            ClickTreeNode(treeNode);
        }
    }

    private void UpdatePaginator(int? lccId)
    {
        lccId ??= LccId;

        if (Paginator != null)
        {
            var node = GetLccTreeNodes().FirstOrDefault(x => x.Id == lccId);
            if (node != null)
                Paginator.SetCurrentExternally(GetLccTreeNodes().IndexOf(node));
        }
    }

    public int GetScenarioCount() => GetLccTreeNodes().Count;

    private List<TreeNodeGeneric<LccTreeObject>> GetLccTreeNodes() => LccTree.GetFlattenedNodes();

    private void SetSelectedLcc(LccDetailModel model)
    {
        SelectedLccDetail = model;
    }

    private void SelectFirstLcc(int? lccId = null)
    {
        LccTree.Initialize(LccManager.GetTreeViewForScenario(SelectedScenario));

        CurrentLcc = LccTree.Node?.Nodes?.FirstOrDefault()?.Source;

        SelectedLcc = CurrentLcc?.Id != null
            ? LccManager.GetLccDetailed(CurrentLcc.Id.Value)
            : null;

        if(SelectedLcc != null)
            SelectedLccDetail = SelectedLcc.Details.FirstOrDefault();

        var node = LccTree.GetFlattenedNodes().FirstOrDefault(x => x.Id == lccId);
        node ??= LccTree.Node?.Nodes?.FirstOrDefault();

        if (node != null)
            LccTree.SelectNode(node);

        LccTabs?.Reload();
        LccDetailsTab?.Reload();
        TreeTab?.Reload();
    }

    private string FormatAsSelectedCurrency(object value)
    {
        Refresh(); //op oneigenlijke plaats aangeroepen, maar wil graag refreshen voor PMO viewcheckbox
        return value == null ? string.Empty : ((double)value).ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
    }
    
    private string FormatDecimalAsSelectedCurrency(object value)
    {
        Refresh(); //op oneigenlijke plaats aangeroepen, maar wil graag refreshen voor PMO viewcheckbox
        return value == null ? string.Empty : ((decimal)value).ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
    }
    
    private string FormatAsNumber(object value)
    {
        Refresh(); //op oneigenlijke plaats aangeroepen, maar wil graag refreshen voor PMO viewcheckbox
        return value == null ? string.Empty : ((int)value).ToString("F0", CultureInfo.CreateSpecificCulture(Currency));
    }

    private void Refresh()
    {
        if (SelectedLcc.RiskObject?.AnalysisType == "PMO")
            ShowPmoCheckbox = true;
        else ShowPmoCheckbox = false;
    }
    
    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    private void OpenRisksPage(RiskModel model) => OpenRisksPage(model.RiskObjectId, model.Id);

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    private void OpenRisksPage(TaskModel model) => OpenRisksPage((model.Risk?.RiskObjectId) ?? 0, model.MrbId ?? 0);

    private void OpenRisksPage(int riskObjectId, int riskId) =>
        NavigationManager.NavigateTo($"/value-risk-analysis/{riskObjectId}/risks/{riskId}");

    private string GetOptLifeTime()
    {
       return ShowActualDates ? Localizer["LccOptReplacementLbl"] : Localizer["LccOptLifeTimeLbl"];
    }
}
