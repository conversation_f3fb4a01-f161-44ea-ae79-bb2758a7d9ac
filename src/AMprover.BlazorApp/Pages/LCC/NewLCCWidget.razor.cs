﻿using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.LCC;

public partial class NewLCCWidget
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; } = default!;
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; } = default!;
    [Inject] private ILookupManager LookupManager { get; set; } = default!;
    [Inject] protected IStringLocalizer<LCCPage> Localizer { get; set; } = default!;
    [Inject] private ILCCManager LccManager { get; set; } = default!;
    [Inject] private IDropdownManager DropdownManager { get; set; } = default!;
    [Inject] private DialogService DialogService { get; set; } = default!;

    [Parameter] public int ScenarioId { get; set; }
    [Parameter] public EventCallback<int> CallBack { get; set; }

    private Dictionary<int, string> RiskObjects { get; set; }
    private int RiskObjectId { get; set; }
    private int? LccRiskObjectId { get; set; }
    private NewLccModel NewLcc { get; set; } = new();
    private bool IsBusy { get; set; }

    protected override void OnInitialized()
    {
        NewLcc.ScenarioId = ScenarioId;
        RiskObjects = DropdownManager.GetRiskObjectDictByScenarioId(ScenarioId);
    }

    private async Task Confirm()
    {
        IsBusy = true;

        var tree = RiskAnalysisManager.GetRisksTreeForLcc(RiskObjectId);

        LccManager.DeleteLccByRiskObject(RiskObjectId);
        CreateLccCollection(tree, true);

        IsBusy = false;

        if (CallBack.HasDelegate)
            await CallBack.InvokeAsync(LccRiskObjectId ?? 0).ConfigureAwait(false);

        LccRiskObjectId = null;
        DialogService.Close();
    }

    private void CreateLccCollection(TreeNodeGeneric<RiskTreeObject> node, bool setReplacementValue)
    {
        var newLcc = LccManager.CreateLcc(new NewLccModel { SelectedNode = node, ScenarioId = ScenarioId }, setReplacementValue);

        if (node.Parent != null)
            LccRiskObjectId ??= newLcc;

        foreach (var item in node.Nodes)
        {
            item.Parent.Source.LccId = newLcc;
            CreateLccCollection(item, false);
        }
    }
}