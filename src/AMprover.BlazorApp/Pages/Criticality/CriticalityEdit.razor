﻿@page "/criticality/edit/{Id:int}"

@if (Model != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem=CriticalityRankingModelFlat Data=@Model
                                      Submit=@ValidTaskSubmitted OnInvalidSubmit=@InvalidTaskSubmitted>

        <!-- Crit Id, Crit Name-->
        <div class="row">
            <div class="col-7">
                <div class="form-group neg-margin-small">
                    <label>Asset Code:</label>
                    <input type="text" class="form-control neg-margin-small" value=@Model.AssetCode disabled="disabled" />
                </div>
            </div>
            <div class="col-2">
            </div>
            <div class="col-3">
                <div class="form-group">
                    <AMproverTextBox Label="ABC Score" Name="Category" @bind-Value="Model.Category" class="form-control" />
                </div>
            </div>
        </div>
        <!-- Description, FailureMode and mtbf -->
        <div class="row">
            <div class="col-9">
                <div class="form-group neg-margin-small">
                    <label>Asset Description:</label>
                    <input type="text" class="form-control neg-margin-small" value=@Model.AssetDescription disabled="disabled" />
                </div>
            </div>
            @if (Model.Asset != null)
            {
                <div class="col-3">
                    <div class="form-group neg-margin-small">
                        <label>ABS Cat.</label>
                        <input type="text" class="form-control neg-margin-small" value=@Model.AssetCategory disabled="disabled" />
                    </div>
                </div>
            }
        </div>
        <div class="row">
            <div class="col-9">
                <div class="form-group">
                    <AMproverTextBox Label="Failure Mechanism" MaxLength="50" Name="Failure Mechanism" @bind-Value="Model.FailureMechanism" class="form-control" />
                </div>
            </div>
            <div class="col-3">
                <div class="form-group">
                    <AMproverNumberInput Label="MTBF" @bind-Value=Model.Probability />
                </div>
            </div>
        </div>

        <RadzenTabs>
            <Tabs>
                <RadzenTabsItem Text="Criticality">
                    <!-- Fmeca1, Femca2 -->
                    <div class="form-group">
                        <div class="row">
                            @if (@CritCategoryCalculation.TextValue == "ValueCalculation" || CritCategoryCalculation.TextValue == "CriticalityHighCalculation")
                            {
                                <div class="col-2">
                                    <label>@GetHeaderText("Fmeca1Value")</label>
                                </div>
                                <div class="col-2">
                                    <AMproverNumberInput Name="Fmeca1Value" Format=C0 @bind-Value="Model.Fmeca1Value" class="form-control" />
                                </div>
                                <div class="col-2">
                                    <label>@GetHeaderText("Fmeca2Value")</label>
                                </div>
                                <div class="col-2">
                                    <AMproverNumberInput Name="Fmeca2Value" Format=C0 @bind-Value="Model.Fmeca2Value" class="form-control" />
                                </div>
                                <div class="col-2">
                                    <label>@GetHeaderText("Fmeca3Value")</label>
                                </div>
                                <div class="col-2">
                                    <AMproverNumberInput Name="Fmeca3Value" Format=C0 @bind-Value="Model.Fmeca3Value" class="form-control" />
                                </div>
                            }
                            else
                            {
                                <div class="col-2">
                                    <label>@GetHeaderText("Fmeca")</label>
                                </div>
                                <div class="col-2 neg-margin-small">
                                    <RadzenNumeric Name="Fmeca" @bind-Value="Model.Fmeca" class="form-control" />
                                </div>
                                <div class="col-2">
                                    <label>@GetHeaderText("Fmeca2")</label>
                                </div>
                                <div class="col-2  neg-margin-small">
                                    <RadzenNumeric Name="Fmeca2" @bind-Value="Model.Fmeca2" class="form-control" />
                                </div>
                                <div class="col-2">
                                    <label>@GetHeaderText("Fmeca3")</label>
                                </div>
                                <div class="col-2 neg-margin-small">
                                    <RadzenNumeric Name="Fmeca3" @bind-Value="Model.Fmeca3" class="form-control" />
                                </div>
                            }
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="row">
                            @if (@CritCategoryCalculation.TextValue == "ValueCalculation")
                            {
                                <div class="col-2 ">
                                    <label>@GetHeaderText("Fmeca4Value")</label>
                                </div>
                                <div class="col-2">
                                    <AMproverNumberInput Name="Fmeca4Value" Format=C0 @bind-Value="Model.Fmeca4Value" class="form-control" />
                                </div>
                                <div class="col-2 ">
                                    <label>@GetHeaderText("Fmeca5Value")</label>
                                </div>
                                <div class="col-2">
                                    <AMproverNumberInput Name="Fmeca5Value" Format=C0 @bind-Value="Model.Fmeca5Value" class="form-control" />
                                </div>
                                <div class="col-2 ">
                                    <label>@GetHeaderText("Fmeca6Value")</label>
                                </div>
                                <div class="col-2">
                                    <AMproverNumberInput Name="Fmeca6Value" Format=C0 @bind-Value="Model.Fmeca6Value" class="form-control" />
                                </div>
                            }
                            else
                            {
                                <div class="col-2 ">
                                    <label>@GetHeaderText("Fmeca4")</label>
                                </div>
                                <div class="col-2 neg-margin-small">
                                    <RadzenNumeric Name="Fmeca4" @bind-Value="Model.Fmeca4" class="form-control" />
                                </div>
                                <div class="col-2 ">
                                    <label>@GetHeaderText("Fmeca5")</label>
                                </div>
                                <div class="col-2 neg-margin-small">
                                    <RadzenNumeric Name="Fmeca5" @bind-Value="Model.Fmeca5" class="form-control" />
                                </div>
                                <div class="col-2 ">
                                    <label>@GetHeaderText("Fmeca6")</label>
                                </div>
                                <div class="col-2 neg-margin-small">
                                    <RadzenNumeric Name="Fmeca6" @bind-Value="Model.Fmeca6" class="form-control" />
                                </div>
                            }
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="row">
                            @if (@CritCategoryCalculation.TextValue == "ValueCalculation")
                            {
                                @if (GetHeaderText("Fmeca7Value") != "Fmeca7Value")
                                {
                                    <div class="col-2">
                                        <label>@GetHeaderText("Fmeca7Value")</label>
                                    </div>
                                    <div class="col-2 neg-margin-small">
                                        <AMproverNumberInput Name="Fmeca7Value" Format=C0 @bind-Value="Model.Fmeca7Value" class="form-control" />
                                    </div>

                                }
                                <div class="col-2 bold">
                                    <label>Total Value</label>
                                </div>
                                <div class="col-2 neg-margin-small bold">
                                    <AMproverNumberInput Name="TotalValue" Format=C0 @bind-Value="Model.TotalValue" class="form-control" />
                                </div>
                            }
                            else
                            {
                                @if (GetHeaderText("Fmeca7") != "Fmeca7")
                                {
                                    <div class="col-2">
                                        <label>@GetHeaderText("Fmeca7")</label>
                                    </div>
                                    <div class="col-2 neg-margin-small bold">
                                        <RadzenNumeric Name="Fmeca7" @bind-Value="Model.Fmeca7" class="form-control" />
                                    </div>
                                }
                                <div class="col-2 bold">
                                    <label>Total</label>
                                </div>
                                <div class="col-2 neg-margin-small bold">
                                    <RadzenNumeric Name="Total" @bind-Value="Model.Total" class="form-control" />
                                </div>
                            }
                        </div>
                    </div>

                </RadzenTabsItem>

                <RadzenTabsItem Text="Failure Consequences">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <AMproverTextArea Label="Failure Cause" Name="FailureCause" Rows="4" @bind-Value="Model.FailureCause" class="form-control" />
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <AMproverTextArea Label="Failure Consequences" Name="FailureConsequences" Rows="4" @bind-Value="Model.FailureConsequences" class="form-control" />
                            </div>
                        </div>
                    </div>
                    <!-- DownTimeAfter, MTTR -->
                    <div class="row">
                        <div class="col-3">
                            <div class="form-group">
                                <AMproverNumberInput Label="DownTimeAfter" @bind-Value=Model.DownTimeAfter />
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="form-group">
                                <AMproverNumberInput Label="MTTR" @bind-Value=Model.MTTR />
                            </div>
                        </div>

                        <!-- RE strategy -->
                        <div class="col-6">
                            <div class="form-group">
                                <AMproverTextBox Label="RE Strategy" Name="RE strategy" @bind-Value="Model.ReStrategy" class="form-control" />
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text="Status">
                    <!-- Status, Responsible -->
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <AMDropdown Label="Status"
                                            Data=@StatusDict.ToNullableDictionary()
                                            @bind-Value="Model.Status" />
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <AMproverTextBox Label="Responsible" MaxLength="150" Name="Responsible" @bind-Value="Model.Responsible" class="form-control" />
                            </div>
                        </div>
                    </div>

                    <!-- FmecaSelect -->
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <AMproverTextBox Label="Fmeca Select" Name="FmecaSelect" @bind-Value="Model.FmecaSelect" class="form-control" />
                            </div>
                        </div>

                        <!-- Redundant, Koon, Total, TotalValue -->
                        <div class="col-6">
                            <div class="form-group">
                                <AMproverTextBox Label="Redundant" Name="Redundant" @bind-Value="Model.Redundant" class="form-control" />
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text="Remarks">
                    <!-- Remarks -->
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <AMproverTextArea Label="Remarks" Name="Remarks" @bind-Value="Model.Remarks" class="form-control" />
                            </div>
                        </div>
                    </div>

                    <!-- InitiatedBy, DateInitiated -->
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <AMproverTextBox Label="Initiated By" Name="InitiatedBy" @bind-Value="Model.InitiatedBy" class="form-control" Disabled="true" />
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group neg-margin-small">
                                <label>Date Initiated</label>
                                <input type="text" value=@Model.DateInitiated.ToDateString(Language) disabled="disabled" class="form-control neg-margin-small" />
                            </div>
                        </div>
                    </div>

                    <!-- DateModified, ModifiedBy -->
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <AMproverTextBox Label="Modified By" Name="ModifiedBy" @bind-Value="Model.ModifiedBy" class="form-control" Disabled="true" />
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group neg-margin-small">
                                <label>Date Modified</label>
                                <input type="text" value=@Model.DateModified.ToDateString(Language) disabled="disabled" class="form-control neg-margin-small" />
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>

        <input type="submit" class="btn btn-primary" value="Save" />
    </Radzen.Blazor.RadzenTemplateForm>
}