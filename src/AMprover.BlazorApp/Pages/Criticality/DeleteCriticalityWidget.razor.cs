﻿using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.Criticality;

public partial class DeleteCriticalityWidget
{
    [Inject] private ILogger<DeleteCriticalityWidget> Logger { get; set; } = default!;
    [Inject] private ICriticalityRankingManager CriticalityManager { get; set; } = default!;
    [Inject] private DialogService DialogService { get; set; } = default!;
    [Inject] protected IStringLocalizer<DeleteCriticalityWidget> Localizer { get; set; } = default!;

    [Parameter] public EventCallback ConfirmCallback { get; set; }

    private async Task DeleteAllCriticalities()
    {
        CriticalityManager.DeleteAllCriticalities();
        DialogService.Close();
        await ConfirmCallback.InvokeAsync();
    }
}