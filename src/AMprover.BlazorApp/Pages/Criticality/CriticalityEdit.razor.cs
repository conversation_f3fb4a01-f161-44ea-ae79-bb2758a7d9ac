using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Criticality;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Models;
using System.Linq;
using AMprover.BusinessLogic.Enums;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.Criticality;

public partial class CriticalityEdit
{
    [Inject] protected ILogger<CriticalityEdit> Logger { get; set; } = default!;
    [Inject] protected DialogService DialogService { get; set; } = default!;
    [Inject] protected ICriticalityRankingManager CriticalityRankingManager { get; set; } = default!;
    [Inject] protected ILookupManager LookupManager { get; set; } = default!;
    [Inject] protected IDropdownManager DropdownManager { get; set; } = default!;
    [Inject] protected IStringLocalizer<CriticalityRanking> Localizer { get; set; } = default!;
    
    [Parameter] public CriticalityRankingModelFlat Model { get; set; }
    [Parameter] public EventCallback<CriticalityRankingModelFlat> CallBack { get; set; }
    [Parameter] public int Id { get; set; }

    private List<LookupSettingModel> LookupSettings { get; set; }
    public Dictionary<int, string> FailureModes { get; set; }
    public Dictionary<int, string> SiCategories { get; set; }
    private Dictionary<int, string> StatusDict { get; set; }
    private Dictionary<string, string> CriticalityGridColumnHeaders { get; set; }
    private LookupSettingModel CritCategoryType { get; set; }
    private LookupSettingModel CritCategoryCalculation { get; set; }
    private LookupSettingModel UpperBlevel { get; set; }
    private LookupSettingModel UpperClevel { get; set; }
    private LookupSettingModel UpperDlevel { get; set; }
    private LookupSettingModel UpperElevel { get; set; }
    private string Language => LookupManager.GetLanguage();

    protected override void OnInitialized()
    {
        LookupSettings = LookupManager.GetLookupSettings();
        FailureModes = DropdownManager.GetFailureModesDict();
        SiCategories = DropdownManager.GetSiCategoriesDict();
        StatusDict = DropdownManager.GetStatusDict();
        CriticalityGridColumnHeaders = LookupManager.GetColumnHeaders(GridNames.Criticality.Rankings);

        InitCriticalitySettings();
        
        base.OnInitialized();
    }

    private void InitCriticalitySettings()
    {
        CritCategoryType = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritCategoryType")) ?? new LookupSettingModel { Property = "CritCategoryType", TextValue = $"{CriticalityCategoryType.ABC}" };
        CritCategoryCalculation = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritCategoryCalculation")) ?? new LookupSettingModel { Property = "CritCategoryCalculation", TextValue = $"{CriticalityCategoryCalculation.CriticalityCalculation}" };
        UpperBlevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperBlevel")) ?? new LookupSettingModel { Property = "CritUpperBlevel", IntValue = 0 };
        UpperClevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperClevel")) ?? new LookupSettingModel { Property = "CritUpperClevel", IntValue = 0 };
        UpperDlevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperDlevel")) ?? new LookupSettingModel { Property = "CritUpperDlevel", IntValue = 0 };
        UpperElevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperElevel")) ?? new LookupSettingModel { Property = "CritUpperElevel", IntValue = 0 };
    }

    private string GetHeaderText(string fieldName)
    {
        return CriticalityGridColumnHeaders.TryGetValue(fieldName, out var header) ? header : fieldName;
    }

    private async Task ValidTaskSubmitted(CriticalityRankingModelFlat model)
    {
        if (CritCategoryCalculation.TextValue != "None") 
            model.Calculate((int)UpperBlevel.IntValue, (int)UpperClevel.IntValue, (int)UpperDlevel.IntValue, (int)UpperElevel.IntValue, CritCategoryCalculation.TextValue, CritCategoryType.TextValue);
        
        Model = CriticalityRankingManager.SaveCriticalityRanking(model);
        await CallBack.InvokeAsync(Model).ConfigureAwait(false);
        DialogService.Close();
    }

    public void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        Logger.LogError($"{nameof(CriticalityEdit)}.{nameof(InvalidTaskSubmitted)}() => {string.Join('|', args.Errors)}");
    }
}