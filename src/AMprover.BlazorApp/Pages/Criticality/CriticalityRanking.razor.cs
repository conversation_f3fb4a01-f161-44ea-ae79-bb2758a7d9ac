using Radzen.Blazor;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Criticality;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;
using AMprover.BusinessLogic.Enums;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.Criticality;

public partial class CriticalityRanking
{
    [Inject] protected ILogger<CriticalityRanking> Logger { get; set; } = default!;
    [Inject] protected ICriticalityRankingManager CriticalityRankingManager { get; set; } = default!;
    [Inject] protected IDropdownManager DropdownManager { get; set; } = default!;
    [Inject] protected NavigationManager NavigationManager { get; set; } = default!;
    [Inject] protected IStringLocalizer<CriticalityRanking> Localizer { get; set; } = default!;
    [Inject] protected ILookupManager LookupManager { get; set; } = default!;
    [Inject] protected DialogService DialogService { get; set; } = default!;
    [Inject] protected IGlobalDataService GlobalDataService { get; set; } = default!;

    private RadzenTabs Tabs { get; set; }
    private List<CriticalityRankingModelFlat> CriticalityRankings { get; set; }
    private UtilityGrid<CriticalityRankingModelFlat> UtilityGrid { get; set; }
    private Dictionary<string, int> CriticalityCategories { get; set; }
    public Dictionary<string, Dictionary<int, string>> DropdownOverrides { get; set; }
    private List<LookupSettingModel> LookupSettings { get; set; }
    public List<RiskMatrixTemplateModel> Templates { get; set; } = [];
    private List<string> PieChartColors { get; set; } = [];
    private CriticalityCategoryType CategoryType { get; set; }
    private CriticalityCategoryCalculation CategoryCalculation { get; set; }
    private bool Loading { get; set; }

    private Dictionary<CriticalityCategoryType, string> CategoryDropdownOptions { get; set; }
        = Enum.GetValues<CriticalityCategoryType>().ToDictionary(x => x, y => y.GetDescription());
    private Dictionary<CriticalityCategoryCalculation, string> CategoryCalcDropdownOptions { get; set; }
        = Enum.GetValues<CriticalityCategoryCalculation>().ToDictionary(x => x, y => y.GetDescription());

    private LookupSettingModel CritCategoryType { get; set; }
    private LookupSettingModel CritCategoryCalculation { get; set; }
    private LookupSettingModel UpperBlevel { get; set; }
    private LookupSettingModel UpperClevel { get; set; }
    private LookupSettingModel UpperDlevel { get; set; }
    private LookupSettingModel UpperElevel { get; set; }

    protected override void OnInitialized()
    {
        CriticalityRankings = CriticalityRankingManager.GetAllCriticalityRankings();
        LookupSettings = LookupManager.GetLookupSettings();
        
        InitCriticalitySettings();
        CategoryType = Enum.Parse<CriticalityCategoryType>(CritCategoryType.TextValue);
        CategoryCalculation = Enum.Parse<CriticalityCategoryCalculation>(CritCategoryCalculation.TextValue);

        SetCategoryPieChart();
        base.OnInitialized();
    }
 
    private void InitCriticalitySettings()
    {
        CritCategoryType = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritCategoryType")) ?? new LookupSettingModel { Property = "CritCategoryType", TextValue = $"{CriticalityCategoryType.ABC}" };
        CritCategoryCalculation = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritCategoryCalculation")) ?? new LookupSettingModel { Property = "CritCategoryCalculation", TextValue = $"{CriticalityCategoryCalculation.CriticalityCalculation}" };
        UpperBlevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperBlevel")) ?? new LookupSettingModel { Property = "CritUpperBlevel", IntValue = 0 };
        UpperClevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperClevel")) ?? new LookupSettingModel { Property = "CritUpperClevel", IntValue = 0 };
        UpperDlevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperDlevel")) ?? new LookupSettingModel { Property = "CritUpperDlevel", IntValue = 0 };
        UpperElevel = LookupSettings.FirstOrDefault(x => x.Property.Equals("CritUpperElevel")) ?? new LookupSettingModel { Property = "CritUpperElevel", IntValue = 0 };
    }

    private void ChangeCategoryType()
    {
        CritCategoryType.TextValue = $"{CategoryType}";
        SaveCriticalitySettings(CritCategoryType);
        NavigationManager.NavigateTo(NavigationManager.Uri, true);
    }

    private void ChangeCategoryCalculation()
    {
        CritCategoryCalculation.TextValue = $"{CategoryCalculation}";
        SaveCriticalitySettings(CritCategoryCalculation);
    }

    private void SaveCriticality(CriticalityRankingModelFlat model)
    {
        if (CritCategoryCalculation.TextValue != "None") model.Calculate((int)UpperBlevel.IntValue, (int)UpperClevel.IntValue, (int)UpperDlevel.IntValue, (int)UpperElevel.IntValue, CritCategoryCalculation.TextValue, CritCategoryType.TextValue);

        var orginalModel = CriticalityRankings.FirstOrDefault(x => x.Id == model.Id);
        var index = CriticalityRankings.IndexOf(orginalModel);
        var updatedModel = CriticalityRankingManager.SaveCriticalityRanking(model);
        CriticalityRankings[index] = updatedModel;
        SetCategoryPieChart();
    }

    private void SaveCriticalitySettings(LookupSettingModel setting)
    {
        LookupManager.SaveLookupSettings(setting);
    }

    private void OnFilter(DataGridColumnFilterEventArgs<CriticalityRankingModelFlat> _)
    {
        SetCategoryPieChart();
    }

    private void SetCategoryPieChart()
    {
        // Use Filtered Results from the UtilityGrid, Fallback to original collection for inital load
        CriticalityCategories = (UtilityGrid?.Grid.View.ToList() ?? CriticalityRankings)
            .GroupBy(x => x.Category?.ToUpper().Trim())
            .OrderBy(x => GenCategoryIndex(x.Key))
            .ToDictionary(g => g.Key ?? "None", g => g.Count());

        SetPieChartColors();
    }

    private int GenCategoryIndex(string input)
    {
        var result = GetCategorySequence(CategoryType).IndexOf(input);

        if (result == -1)
            return int.MaxValue;

        return result;
    }

    private static List<string> GetCategorySequence(CriticalityCategoryType categoryType)
    {
        return categoryType switch
        {
            CriticalityCategoryType.ABC => ["A", "B", "C"],
            CriticalityCategoryType.ABCD => ["A", "B", "C", "D"],
            CriticalityCategoryType.ABCDE => ["A", "B", "C", "D", "E"],
            CriticalityCategoryType.HML => ["H", "M", "L"],
            CriticalityCategoryType.ThreeToOne => ["3", "2", "1"],
            CriticalityCategoryType.FourToOne => ["4", "3", "2", "1"],
            CriticalityCategoryType.FiveToOne => ["5", "4", "3", "2", "1"],
            _ => throw new NotImplementedException(),
        };
    }

    private int GetCritLevel()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABC => 3,
            CriticalityCategoryType.ABCD => 4,
            CriticalityCategoryType.ABCDE => 5,
            CriticalityCategoryType.HML => 3,
            CriticalityCategoryType.ThreeToOne => 3,
            CriticalityCategoryType.FourToOne => 4,
            CriticalityCategoryType.FiveToOne => 5,
            _ => throw new NotImplementedException(),
        };
    }

    private string GetUpperBtxt()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABC => Localizer["CrUpperBlevelTxt"],
            CriticalityCategoryType.ABCD => Localizer["CrUpperBlevelTxt"],
            CriticalityCategoryType.ABCDE => Localizer["CrUpperBlevelTxt"],
            CriticalityCategoryType.HML => Localizer["CrUpperMlevelTxt"],
            CriticalityCategoryType.ThreeToOne => Localizer["CrUpper4levelTxt"],
            CriticalityCategoryType.FourToOne => Localizer["CrUpper4levelTxt"],
            CriticalityCategoryType.FiveToOne => Localizer["CrUpper4levelTxt"],
            _ => throw new NotImplementedException(),
        };
    }

    private string GetUpperCtxt()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABC => Localizer["CrUpperClevelTxt"],
            CriticalityCategoryType.ABCD => Localizer["CrUpperClevelTxt"],
            CriticalityCategoryType.ABCDE => Localizer["CrUpperClevelTxt"],
            CriticalityCategoryType.HML => Localizer["CrUpperLlevelTxt"],
            CriticalityCategoryType.ThreeToOne => Localizer["CrUpper3levelTxt"],
            CriticalityCategoryType.FourToOne => Localizer["CrUpper3levelTxt"],
            CriticalityCategoryType.FiveToOne => Localizer["CrUpper3levelTxt"],
            _ => throw new NotImplementedException(),
        };
    }

    private string GetUpperDtxt()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABCD => Localizer["CrUpperDlevelTxt"],
            CriticalityCategoryType.ABCDE => Localizer["CrUpperDlevelTxt"],
            CriticalityCategoryType.FourToOne => Localizer["CrUpper2levelTxt"],
            CriticalityCategoryType.FiveToOne => Localizer["CrUpper2levelTxt"],
            _ => throw new NotImplementedException(),
        };
    }
    
    private string GetUpperEtxt()
    {
        return CategoryType switch
        {
            CriticalityCategoryType.ABCDE => Localizer["CrUpperElevelTxt"],
            CriticalityCategoryType.FiveToOne => Localizer["CrUpper1levelTxt"],
            _ => throw new NotImplementedException(),
        };
    }

    private void SetPieChartColors()
    {
        PieChartColors.Clear();
        var unfilteredCriticalities = UtilityGrid?.Grid.View.ToList() ?? CriticalityRankings;
        var options = unfilteredCriticalities.Select(x => x.Category?.Trim()?.ToUpper() ?? "").Distinct().Count();

        var categories = GetCategorySequence(CategoryType);

        if (GetCritLevel() == 3)
        {
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[0], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#ff8080"); // Red
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[1], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#ffd280"); // Orange
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[2], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#80c080"); // Green
        }
        else if (GetCritLevel() == 4)
        {
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[0], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#ff8080"); // Red
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[1], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#ffd280"); // Orange
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[2], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#80c080"); // Green
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[3], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#80ff80"); // Light Green
        }
        else if (GetCritLevel() == 5)
        {
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[0], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#ff8080"); // Red
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[1], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#ffd280"); // Orange
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[2], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#ffff80"); // Yellow
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[3], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#80c080"); // Green
            if (unfilteredCriticalities.Any(x => (x.Category?.Trim() ?? "").Equals(categories[4], StringComparison.OrdinalIgnoreCase)))
                PieChartColors.Add("#009900"); // Dark Green
        }

        // Gray
        for (int i = 0; i < options; i++)
        {
            PieChartColors.Add("#dddddd");
        }
    }

    private void OpenDeleteWidget()
    {
        DialogService.Open<DeleteCriticalityWidget>
                    (Localizer["CrDeleteHeaderTxt"], new Dictionary<string, object>
                    {
                        {
                            nameof(DeleteCriticalityWidget.ConfirmCallback),
                            EventCallback.Factory.Create(this, WidgetCallBack)
                        }
                    });
    }

    private void WidgetCallBack()
    {
        OnInitialized();
        Tabs.Reload();
    }

    private void OpenRiskOnAbs(int assetId)
    {
        NavigationManager.NavigateTo($"/value-risks-on-abs?assetid={assetId}&tabid=3");
    }

    private void ProcessUpload(FileUpload fileUpload)
    {
        if (string.IsNullOrWhiteSpace(fileUpload.Base64Content))
            return;

        ShowLoadingDialog();

        var uploadedItems = fileUpload.Base64Content
            .GetExcelData<CriticalityRankingModelFlat>(headerIndex: 1)
            .Where(x => !string.IsNullOrWhiteSpace(x.Value.AssetCode))
            .Select(x => x.Value).ToList();
        
        if (CriticalityRankingManager.ImportFileUpload(uploadedItems, out var result))
        {
            DialogService.Close();
            ShowDialog("Success", true, result.StatusMessage);
        }
        else
        {
            DialogService.Close();
            ShowDialog("Error!", true, new[] { "Failed to process uploaded file" }.Concat(result.ErrorMessages).ToArray());
        }
    }

    private void ShowLoadingDialog()
    {
        DialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    private void ShowDialog(string title, bool reloadOnClose, params string[] texts)
    {
        DialogService.Open<NotificationDialog>(title,
            new Dictionary<string, object>
            {
                {nameof(NotificationDialog.Texts), texts.ToList()},
                {nameof(NotificationDialog.ReloadPageOnClose), reloadOnClose}
            },
            new DialogOptions
            {
                ShowClose = false,
                Resizable = false,
                Draggable = true
            });
    }

    private void ImportAssets()
    {
        var newAssets = CriticalityRankingManager.ImportFromAssets();
        ShowDialog("Success!", true, $"Imported {newAssets.Count} new assets");
    }

    private void EditRow(CriticalityRankingModelFlat row)
    {
        DialogService.Open<CriticalityEdit>("Edit Criticality",
            new Dictionary<string, object>
            {
                { "Model", row },
                { "CallBack", EventCallback.Factory.Create<CriticalityRankingModelFlat>(this, EditCallBack) },
            },
            new DialogOptions { Width = "700px", Resizable = false, Draggable = true });
    }

    private void EditCallBack(CriticalityRankingModelFlat model)
    {
        var originalModel = CriticalityRankings.FirstOrDefault(x => x.Id == model.Id);
        var index = CriticalityRankings.IndexOf(originalModel);
        CriticalityRankings[index] = model;
        SetCategoryPieChart();
    }

    private Dictionary<string, Dictionary<string, string>> GetCriticalityOptions()
    {
        return new Dictionary<string, Dictionary<string, string>>
        {
            {
                nameof(CriticalityRankingModelFlat.Category),
                GetCategorySequence(CategoryType).ToDictionary(x => x, y => y)
            }
        };
    }

    //Recalculate All Criticalities
    private async Task RecalculateAllCriticalities()
    {
        Loading = true;
        await Task.Delay(1);

        for (int i = 0; i < CriticalityRankings.Count; i++)
        {
            CriticalityRankingModelFlat model;

            model = CriticalityRankings[i];
            model.Calculate((int)UpperBlevel.IntValue, (int)UpperClevel.IntValue, (int)UpperDlevel.IntValue, (int)UpperElevel.IntValue, CritCategoryCalculation.TextValue, CritCategoryType.TextValue);
        }

        CriticalityRankingManager.UpdateCriticalities(CriticalityRankings);
        NavigationManager.NavigateTo(NavigationManager.Uri, true);

        Loading = false;
    }
}
