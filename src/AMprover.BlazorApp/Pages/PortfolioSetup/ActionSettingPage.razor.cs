using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Sapa;
using Ra<PERSON>zen;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;
using AMprover.Data.Models;
using Microsoft.AspNetCore.Components;
using PolicyModel = AMprover.BusinessLogic.Models.RiskAnalysis.PolicyModel;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public partial class ActionSettingPage
{
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IIntervalUnitManager IntervalUnitManager { get; set; }
    [Inject] private IAttachmentManager AttachmentManager { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }

    private List<InitiatorModel> Initiators { get; set; }
    private List<ExecutorModel> Executors { get; set; }
    private List<IntervalUnitModel> IntervalUnits { get; set; }
    private UtilityGrid<IntervalUnitModel> IntervalCrudGrid { get; set; }
    private List<WorkPackageModel> WorkPackages { get; set; }
    private List<SapaWorkpackageModel> SapaWorkpackages { get; set; }
    private List<PolicyModel> Policies { get; set; }
    private List<AttachmentCategoryModel> AttachmentCategories { get; set; }
    private List<AttachmentModel> Attachments { get; set; }
    public List<AdditionalDataModel> AdditionalData { get; set; }

    private Dictionary<string, Dictionary<int, string>> AttachmentDropdownOverrides { get; set; }

    private UtilityGrid<InitiatorModel> InitiatorCrudGrid { get; set; }
    private UtilityGrid<ExecutorModel> ExecutorCrudGrid { get; set; }
    private UtilityGrid<WorkPackageModel> WorkPackageCrudGrid { get; set; }
    private UtilityGrid<SapaWorkpackageModel> SapaWorkpackageCrudGrid { get; set; }
    private UtilityGrid<PolicyModel> PolicyCrudGrid { get; set; }
    private UtilityGrid<AttachmentModel> AttachmentCrudGrid { get; set; }
    public UtilityGrid<AttachmentCategoryModel> AttachmentCategoryCrudGrid { get; set; }
    public UtilityGrid<AdditionalDataModel> AdditionalDataGrid { get; set; }

    protected override void OnInitialized()
    {
        AdditionalData = LookupManager.GetAdditionalData();
        LookupManager.GetLookupSettings();
        Initiators = LookupManager.GetInitiators();
        Executors = LookupManager.GetExecutors();
        IntervalUnits = IntervalUnitManager.GetIntervalUnits();
        WorkPackages = LookupManager.GetWorkPackages();
        SapaWorkpackages = LookupManager.GetSapaWorkPackages();
        Policies = LookupManager.GetPolicies();
        AttachmentCategories = AttachmentManager.GetAttachmentCategories();
        Attachments = AttachmentManager.GetAttachments();
        AttachmentDropdownOverrides = DropdownManager.GetAttachmentDropdownOverrides();

        base.OnInitialized();
    }

    public void UpdateSettings(LookupSettingModel setting)
    {
        LookupManager.SaveLookupSettings(setting);
    }

    // Interval Unit
    private void SaveIntervalUnit(IntervalUnitModel model)
    {
        model.UnitsPerYear ??= 0;
        IntervalUnitManager.SaveIntervalUnit(model, out var result);

        if (model.Id != null)
        {
            if (result.Success)
            {
                IntervalUnits[IntervalUnits.IndexOf(model)] = result.Item;
                IntervalCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                IntervalCrudGrid.EditItem(model);
            }
        }
        else
        {
            IntervalUnits.Add(result.Item);
            IntervalCrudGrid.Reload();
        }
    }

    private void DeleteIntervalUnit(IntervalUnitModel model)
    {
        if (model.Id == null) return;
        if (IntervalUnitManager.DeleteIntervalUnit(model.Id.Value, out var result))
        {
            IntervalUnits.Remove(model);
            IntervalCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            IntervalCrudGrid.EditItem(model);
        }
    }

    // Initiator
    private void SaveInitiator(InitiatorModel model)
    {
        LookupManager.SaveInitiator(model, out var result);

        if (model.Id != null)
        {
            if (result.Success)
            {
                Initiators[Initiators.IndexOf(model)] = result.Item;
                InitiatorCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                InitiatorCrudGrid.EditItem(model);
            }
        }
        else
        {
            Initiators.Add(result.Item);
            InitiatorCrudGrid.Reload();
        }
    }

    private void DeleteInitiator(InitiatorModel model)
    {
        if (model.Id == null) return;
        if (LookupManager.DeleteInitiator(model.Id.Value, out var result))
        {
            Initiators.Remove(model);
            InitiatorCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            InitiatorCrudGrid.EditItem(model);
        }
    }

    // Policy
    private void SavePolicy(PolicyModel model)
    {
        LookupManager.SavePolicy(model, out var result);

        if (model.Id != 0)
        {
            if (result.Success)
            {
                Policies[Policies.IndexOf(model)] = result.Item;
                PolicyCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                PolicyCrudGrid.EditItem(model);
            }
        }
        else
        {
            Policies.Add(result.Item);
            PolicyCrudGrid.Reload();
        }
    }

    private void DeletePolicy(PolicyModel model)
    {
        if (model.Id == 0) return;
        if (LookupManager.DeletePolicy(model.Id, out var result))
        {
            Policies.Remove(model);
            PolicyCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            PolicyCrudGrid.EditItem(model);
        }
    }

    // Executor
    private void SaveExecutor(ExecutorModel model)
    {
        LookupManager.SaveExecutor(model, out var result);

        if (model.Id != null && Executors.IndexOf(model) >= 0)
        {
            if (result.Success)
            {
                Executors[Executors.IndexOf(model)] = result.Item;
                ExecutorCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                ExecutorCrudGrid.EditItem(model);
            }
        }
        else
        {
            Executors.Add(result.Item);
            ExecutorCrudGrid.Reload();
        }
    }

    private void DeleteExecutor(ExecutorModel model)
    {
        if (model.Id == null) return;
        if (LookupManager.DeleteExecutor(model.Id.Value, out var result))
        {
            Executors.Remove(model);
            ExecutorCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            ExecutorCrudGrid.EditItem(model);
        }
    }

    // WorkPackage
    private void SaveWorkPackage(WorkPackageModel model)
    {
        LookupManager.SaveWorkPackage(model, out var result);

        if (model.Id != null)
        {
            if (result.Success)
            {
                WorkPackages[WorkPackages.IndexOf(model)] = result.Item;
                WorkPackageCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                WorkPackageCrudGrid.EditItem(model);
            }
        }
        else
        {
            WorkPackages.Add(result.Item);
            WorkPackageCrudGrid.Reload();
        }
    }

    private void DeleteWorkPackage(WorkPackageModel model)
    {
        if (model.Id == null) return;
        if (LookupManager.DeleteWorkPackage(model.Id.Value, out var result))
        {
            WorkPackages.Remove(model);
            WorkPackageCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            WorkPackageCrudGrid.EditItem(model);
        }
    }

    // SapaWorkPackage
    private void SaveSapaWorkPackage(SapaWorkpackageModel model)
    {
        LookupManager.SaveSapaWorkPackage(model, out var result);

        if (model.Id != 0)
        {
            if (result.Success)
            {
                SapaWorkpackages[SapaWorkpackages.IndexOf(model)] = result.Item;
                SapaWorkpackageCrudGrid.Reload();
            }
            else
            {
                ShowDbErrorMessage(result);
                SapaWorkpackageCrudGrid.EditItem(model);
            }
        }
        else
        {
            SapaWorkpackages.Add(result.Item);
            SapaWorkpackageCrudGrid.Reload();
        }
    }

    private void DeleteSapaWorkPackage(SapaWorkpackageModel model)
    {
        if (model.Id == 0) return;
        if (LookupManager.DeleteSapaWorkPackage(model.Id, out var result))
        {
            SapaWorkpackages.Remove(model);
            SapaWorkpackageCrudGrid.Reload();
        }
        else
        {
            ShowDbErrorMessage(result);
            SapaWorkpackageCrudGrid.EditItem(model);
        }
    }

    // Attachment
    private void SaveAttachment(AttachmentModel model)
    {
        var index = Attachments.IndexOf(model);
        model = AttachmentManager.SaveAttachment(model);

        if (index == -1)
        {
            Attachments.Add(model);
        }
        else
        {
            Attachments[index] = model;
        }

        AttachmentCrudGrid.Reload();
    }

    private void DeleteAttachment(AttachmentModel model)
    {
        if (model.Id == 0) return;
        if (AttachmentManager.DeleteAttachment(model.Id, out var result))
        {
            AttachmentCategories = AttachmentManager.GetAttachmentCategories();
            Attachments.Remove(model);
            AttachmentCrudGrid.Reload();
        }
        else
        {
            ShowErrorMessage(result);
        }
    }

    // Attachment Categories
    private void SaveAttachmentCategory(AttachmentCategoryModel model)
    {
        AttachmentManager.SaveAttachmentCategory(model);
        AttachmentCategories = AttachmentManager.GetAttachmentCategories();
        AttachmentDropdownOverrides = DropdownManager.GetAttachmentDropdownOverrides();
    }

    private async Task DeleteAttachmentCategory(AttachmentCategoryModel model)
    {
        if (model.Id == 0) return;
        if (AttachmentManager.DeleteAttachmentCategory(model.Id, out var result))
        {
            AttachmentCategories = AttachmentManager.GetAttachmentCategories();
            AttachmentDropdownOverrides = DropdownManager.GetAttachmentDropdownOverrides();
        }
        else
        {
            DialogService.Close();
            await Task.Delay(1);
            ShowErrorMessage(result);
        }
    }

    // Additional Data

    private Task SaveAdditionalData(AdditionalDataModel model)
    {
        LookupManager.SaveAdditionalData(model, out var result);

        if (model.Id != null)
        {
            if (result.Success)
            {
                AdditionalData[AdditionalData.IndexOf(model)] = result.Item;
                return AdditionalDataGrid.Reload();
            }

            ShowDbErrorMessage(result);
            AdditionalDataGrid.EditItem(model);
        }
        else
        {
            AdditionalData.Add(result.Item);
            return AdditionalDataGrid.Reload();
        }

        return Task.CompletedTask;
    }

    private Task DeleteAdditionalData(AdditionalDataModel model)
    {
        if (model.Id == null) return Task.CompletedTask;
        if (LookupManager.DeleteAdditionalData(model.Id.Value, out var result))
        {
            AdditionalData.Remove(model);
            return AdditionalDataGrid.Reload();
        }

        ShowDbErrorMessage(result);
        AdditionalDataGrid.EditItem(model);

        return Task.CompletedTask;
    }

    // Dropdown overrides
    private Dictionary<string, Dictionary<int, string>> GetWorkPackageDropDowns()
    {
        var result = new Dictionary<string, Dictionary<int, string>>
            {
                { nameof(WorkPackageModel.IntervalUnit), IntervalUnits.ToDictionary(x => x.Id.Value, y => y.Name) },
                { nameof(WorkPackageModel.Executor), Executors.ToDictionary(x => x.Id.Value, y => y.Name) }
            };
        return result;
    }

    private void ShowDbErrorMessage(DbOperationResult dbResult)
    {
        DialogService.Open<NotificationDialog>("Error",
            new Dictionary<string, object>
            {
                    {nameof(NotificationDialog.Texts), dbResult.ErrorMessages}
            });
    }

    private void ShowErrorMessage(string errorMessage)
    {
        DialogService.Open<NotificationDialog>("Error",
            new Dictionary<string, object>
            {
                    {nameof(NotificationDialog.Texts), new List<string> {errorMessage}}
            });
    }
}