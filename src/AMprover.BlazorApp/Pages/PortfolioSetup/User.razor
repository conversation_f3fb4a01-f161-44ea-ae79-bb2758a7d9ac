﻿@page "/users/{UserId}"
@using AMprover.Data.Constants

@code {
    private static string AdminRoles => $"{RoleConstants.Administrators},{RoleConstants.PortfolioAdministrators}";
}

<AuthorizeView Roles="@AdminRoles">
    <Authorized Context="authContext">
        @if (UserAccount != null)
        {
            <h4>Update useraccount for: @UserAccount.Name</h4>

            <EditForm Model="@UserAccount"
                      OnValidSubmit=@UpdateUserAccountValidFormSubmitted
                      OnInvalidSubmit=@UpdateUserAccountInvalidFormSubmitted>
                <DataAnnotationsValidator/>

                <div class="form-group">
                    <RadzenTextBox class="form-control" Placeholder="Userid" @bind-Value=UserAccount.Id Disabled="true"/>
                </div>

                <div class="form-group">
                    <RadzenTextBox class="form-control" Placeholder="Full name" @bind-Value=UserAccount.Name Disabled="true"/>
                </div>

                <div class="form-group">
                    <RadzenTextBox class="form-control" Placeholder="Username" @bind-Value=UserAccount.UserName type="email" Disabled="true"/>
                </div>

                <div class="form-group">
                    <RadzenTextBox class="form-control" Placeholder="Company" @bind-Value=UserAccount.Company Disabled="true"/>
                </div>

                <div class="form-group">
                    <label>Departments</label>
                    <RadzenCheckBoxList @bind-Value="@SelectedDepartmentIds"
                                        Data="@Departments"
                                        TextProperty="Description"
                                        ValueProperty="Id"
                                        Orientation="Orientation.Vertical"/>
                </div>

                <div class="form-group">
                    <label>Select a role</label>
                    <AMDropdown Data="@AvailableRoles" Required="true" @bind-Value="CurrentRole" AllowFiltering="true" ShowValidations="ShowValidations"/>
                </div>

                <div class="form-group">
                    <RadzenButton ButtonType="ButtonType.Submit" Text="Save"/>
                    <RadzenButton Text="@Localizer["Cancel"]" ButtonStyle="ButtonStyle.Light" Click="@(_ => NavigationManager.NavigateTo("/users"))"/>
                    <span style="color:red">@ValidationError</span>
                </div>
            </EditForm>
        }
    </Authorized>
</AuthorizeView>