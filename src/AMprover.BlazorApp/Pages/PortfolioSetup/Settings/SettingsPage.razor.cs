using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Models;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.Settings;

public partial class SettingsPage
{
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IConfiguration Configuration { get; set; }

    private List<LookupSettingModel> LookupSettings { get; set; }
    private LookupSettingModel LanguageSetting { get; set; }
    private Dictionary<string, string> Languages => Configuration.GetSection("Languages").GetChildren().OrderBy(x => x.Value)
        .ToDictionary(x => x.Key, x => x.Value);
    private LookupSettingModel CurrencySetting { get; set; }
    private Dictionary<string, string> Currencies => Configuration.GetSection("Currencies").GetChildren().OrderBy(x => x.Value)
        .ToDictionary(x => x.Key, x => x.Value);
    private Dictionary<string, LookupSettingModel> CalculationSettings { get; } = new();

    private LookupSettingModel RamsAllowedDeptSetting { get; set; }
    private LookupSettingModel RamsTotalAllowedBlocksSetting { get; set; }
    private LookupSettingModel EnableColumnsForTypes { get; set; }
    private LookupSettingModel ShowSubColumns { get; set; }
    private LookupSettingModel ShowRegenerateImages { get; set; }

    protected override void OnInitialized()
    {
        LookupSettings = LookupManager.GetLookupSettings();

        // Language
        LanguageSetting = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.Language, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.Language };

        // Currency
        CurrencySetting = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.Currency, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.Currency };

        // Calculation settings
        InitCalculationSettings();

        // Configuration settings
        InitConfigurationSettings();

        base.OnInitialized();
    }

    private void InitCalculationSettings()
    {
        var calculationSettings = LookupSettings.Where(x => x.Property.StartsWith("Calc", StringComparison.OrdinalIgnoreCase)).ToList();

        foreach (var setting in calculationSettings)
        {
            var displayName = setting.Property.Replace("Calc", string.Empty);
            CalculationSettings.Add(displayName, setting);
        }
    }

    private void InitConfigurationSettings()
    {
        RamsAllowedDeptSetting = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.RamsAllowedDept, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.RamsAllowedDept };
        RamsTotalAllowedBlocksSetting = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.RamsTotalAllowedBlocks, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.RamsTotalAllowedBlocks };
        EnableColumnsForTypes = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.EnableColumnsForTypes, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.EnableColumnsForTypes };
        ShowSubColumns = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.ShowSubColumns, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.ShowSubColumns };
        ShowRegenerateImages = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.ShowRegenerateImages, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.ShowRegenerateImages };
    }

    private void UpdateSettings(LookupSettingModel setting)
    {
        LookupManager.SaveLookupSettings(setting);
    }

    private void OnCheckboxChange(bool? value, LookupSettingModel setting)
    {
        setting.IntValue = value == true ? 1 : 0;
        UpdateSettings(setting);
    }
}