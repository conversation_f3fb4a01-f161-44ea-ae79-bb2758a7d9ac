using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BlazorApp.Pages.RiskOrganize.Import;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.Import;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public partial class CommonActionsPage
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IPortfolioSetupManager PortfolioSetupManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private ICommonActionImportManager CommonActionImportManager { get; set; }

    private RadzenTabs Tabs { get; set; }
    private Dictionary<int, string> ExecutorDict { get; set; }
    private Dictionary<int, string> PolicyDict { get; set; }
    private Dictionary<int, string> IntervalDict { get; set; }
    private Dictionary<int, string> InitiatorDict { get; set; }
    private Dictionary<int, string> WorkPackageDict { get; set; }
    private Dictionary<int, string> CommonTasksDict { get; set; }
    private Dictionary<int, string> UnitTypeDict { get; set; }
    private Dictionary<string, string> TaskTypeDict { get; set; } = [];

    private UtilityGrid<CommonTaskModel> CommonActionsGrid { get; set; }
    private List<CommonTaskModel> CommonActions { get; set; }
    private List<CommonActionImportModel> CommonActionsImport { get; set; }

    protected override void OnInitialized()
    {
            ExecutorDict = DropdownManager.GetExecutorDict();
            PolicyDict = DropdownManager.GetPolicyDict();
            IntervalDict = DropdownManager.GetIntervalUnitDict();
            InitiatorDict = DropdownManager.GetInitiatorDict();
            WorkPackageDict = DropdownManager.GetWorkpackageDict();
            CommonTasksDict = DropdownManager.GetCommonTasksDict();
            UnitTypeDict = DropdownManager.GetLookupUserDefinedByFilterDict("UnitTypes");
            TaskTypeDict = LookupManager.GetLookupByFilterDict("MeasureType", true);

            CommonActions = PortfolioSetupManager.GetCommonActions();
            CommonActionsImport = PortfolioSetupManager.GetCommonActionImportModels();

            base.OnInitialized();
        }

    private void OpenCommonActionPopUp(int id)
    {
            DialogService.Open<CommonActionEdit>(_localizer["CaEditPopupHeader"],
                new Dictionary<string, object> {
                    { "CommonTaskId", id },
                    { "Callback", EventCallback.Factory.Create<CommonTaskModel>(this, AddOrUpdateCommonActionGrid)}
                },
                new DialogOptions { Width = "740px", Resizable = false, Draggable = true });
        }

    // Common Actions
    private async Task DeleteCommonAction(CommonTaskModel commonTask)
    {
            var taskIdsUsingCommonAction = PortfolioSetupManager.GetTaskIdsUsingCommonAction(commonTask.Id);

            if (taskIdsUsingCommonAction.Count > 0)
            {
                DialogService.Close();

                // Required to Replace exiting Dialog with new one
                await Task.Delay(1);

                // Extra confirm since common action is in use
                DialogService.Open<AreYouSureDialog<CommonTaskModel>>(_localizer["CaCommonActionInUse"],
                    new Dictionary<string, object>
                    {
                        {
                            nameof(AreYouSureDialog<CommonTaskModel>.Item),
                            commonTask
                        },
                        {
                            nameof(AreYouSureDialog<CommonTaskModel>.YesCallback),
                            EventCallback.Factory.Create<CommonTaskModel>(this, ConfirmDeleteCommonAction)
                        },
                        {
                            nameof(AreYouSureDialog<CommonTaskModel>.Text),
                            string.Format(_localizer["CaCommonActionInUseDetails"], taskIdsUsingCommonAction.Count)
                        }
                    },
                new DialogOptions { Width = "600px", Resizable = false, Draggable = true });

                return;
            }

            ConfirmDeleteCommonAction(commonTask);
        }

    private void ConfirmDeleteCommonAction(CommonTaskModel commonTask)
    {
            if (CommonActions.Contains(commonTask))
            {
                CommonActions.Remove(commonTask);
                CommonActions = [.. CommonActions];
            }

            PortfolioSetupManager.DeleteCommonAction(commonTask);
            CommonActionsImport = PortfolioSetupManager.GetCommonActionImportModels();
            Tabs?.Reload();
        }

    private async Task UpdateCommonAction(CommonTaskModel commonTask)
    {
            commonTask = PortfolioSetupManager.UpdateCommonAction(commonTask);
            CommonActionsImport = PortfolioSetupManager.GetCommonActionImportModels();
            await AddOrUpdateCommonActionGrid(commonTask);
        }

    private async Task AddOrUpdateCommonActionGrid(CommonTaskModel model)
    {
            var rowIndex = CommonActions.FindIndex(x => x.Id == model.Id);
            if (rowIndex >= 0)
            {
                CommonActions[rowIndex] = model;
            }
            else
            {
                CommonActions.Add(model);
            }

            await CommonActionsGrid.Grid.Reload();
        }

    private void PasteCommonTaskCallback(BaseModel model)
    {
            var copiedTask = model.CopyAsNew<CommonTaskModel>();
            CommonActions.Add(PortfolioSetupManager.UpdateCommonAction(copiedTask));
            CommonActions = [.. CommonActions];
            CommonActionsImport = PortfolioSetupManager.GetCommonActionImportModels();
        }

    // Import
    private void OpenImportWidget()
    {
            DialogService.Open<ImportCommonActionWidget>
                        ("Import Common Actions",
                        new Dictionary<string, object>
                        {
                            {
                                nameof(ImportCommonActionWidget.RefreshCommonActions),
                                EventCallback.Factory.Create(this, RefreshItems)
                            }
                        },
                        new DialogOptions() { Draggable = true });
        }

    private void RefreshItems()
    {
            CommonActions = PortfolioSetupManager.GetCommonActions();
            CommonActionsImport = PortfolioSetupManager.GetCommonActionImportModels();
            Tabs?.Reload();
        }

    // Dropdown overrides
    private Dictionary<string, Dictionary<int, string>> GetCommonActionDropdownOverrides()
    {
            var result = new Dictionary<string, Dictionary<int, string>>
            {
                { nameof(CommonTaskModel.IntervalUnitId), IntervalDict},
                { nameof(CommonTaskModel.ExecutorId), ExecutorDict },
                { nameof(CommonTaskModel.InitiatorId), InitiatorDict },
                { nameof(CommonTaskModel.WorkPackageId), WorkPackageDict },
                { nameof(CommonTaskModel.MxPolicyId), PolicyDict },
                { nameof(CommonTaskModel.UnitType), UnitTypeDict },
            };
            return result;
        }

    private Dictionary<string, Dictionary<string, string>> GetCommonActionOptionOverrides()
    {
            var result = new Dictionary<string, Dictionary<string, string>>
            {
                { nameof(CommonTaskModel.Type), TaskTypeDict },
            };
            return result;
        }
}