using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Failures;
using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.RiskAnalysis;

public partial class FailuresPage
{
    [Inject] private IFailureManager FailureManager { get; set; }

    public UtilityGrid<FailureModeModel> FailureModesGrid { get; set; }
    public UtilityGrid<FailureCategory> FirstCategoriesGrid { get; set; }
    public UtilityGrid<FailureCategory> SecondCategoriesGrid { get; set; }

    public List<FailureModeModel> FailureModes { get; set; } = [];
    public List<FailureCategory> FirstCategories { get; set; } = [];
    public List<FailureCategory> SecondCategories { get; set; } = [];

    public bool ShowError { get; set; }
    public string ErrorText { get; set; }

    protected override async Task OnInitializedAsync()
    {
            LoadData();
            await base.OnInitializedAsync();
        }

    private void LoadData()
    {
            FailureModes = FailureManager.GetAllFailureModes();
            var allFailureCategories = FailureManager.GetAllFailureCategories();
            FirstCategories = allFailureCategories.Primary;
            SecondCategories = allFailureCategories.Secondary;
        }

    public void SaveFailureMode(FailureModeModel model)
    {
            try
            {
                FailureManager.SaveFailureMode(model);
                LoadData();
                ShowError = false;
            }
            catch (Exception ex)
            {
                ShowError = true;
                ErrorText = ex.Message;
                StateHasChanged();
            }
        }

    public void DeleteFailureMode(FailureModeModel model)
    {
            try
            {
                FailureManager.DeleteFailureMode(model);
                LoadData();
                ShowError = false;
            }
            catch (Exception ex)
            {
                ShowError = true;
                ErrorText = ex.Message;
                StateHasChanged();
            }
        }

    public void SaveFailureCategoryOne(FailureCategory model)
    {

            SaveFailureCategory(model, 1);
        }

    public void SaveFailureCategoryTwo(FailureCategory model)
    {

            SaveFailureCategory(model, 2);
        }

    private void SaveFailureCategory(FailureCategory model, int categoryType)
    {
            try
            {
                 FailureManager.SaveFailureCategory(model, categoryType);
                 LoadData();
                ShowError = false;
            }
            catch (Exception ex)
            {
                ShowError = true;
                ErrorText = ex.Message;
                StateHasChanged();
            }
        }

    public void DeleteFailureCategory(FailureCategory model)
    {
            try
            {
                FailureManager.DeleteFailureCategory(model);
                LoadData();
                ShowError = false;
            }
            catch (Exception ex)
            {
                ShowError = true;
                ErrorText = ex.Message;
                StateHasChanged();
            }
        }

    public bool ValidateCanSave(FailureModeModel model)
    {
            return !string.IsNullOrWhiteSpace(model.Name);
        }

    public void ValidateCanSaveFailed(FailureModeModel model)
    {
            ShowError = true;
            ErrorText = "Failure mode name cannot be empty";
            StateHasChanged();
        }
}