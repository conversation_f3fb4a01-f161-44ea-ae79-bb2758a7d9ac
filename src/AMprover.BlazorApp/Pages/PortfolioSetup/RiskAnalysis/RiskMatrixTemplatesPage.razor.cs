﻿using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Radzen;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic.Models;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.RiskAnalysis;

public partial class RiskMatrixTemplatesPage
{
    [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; } = default!;
    [Inject] private NavigationManager NavigationManager { get; set; } = default!;
    [Inject] private DialogService DialogService { get; set; } = default!;
    [Inject] private IStringLocalizer<RiskMatrixTemplatePage> Localizer { get; set; } = default!;

    private List<RiskMatrixTemplateModel> RiskMatrixTemplates { get; set; }

    public UtilityGrid<RiskMatrixTemplateModel> RiskMatrixGrid { get; set; }

    protected override void OnInitialized()
    {
        RiskMatrixTemplates = RiskAnalysisSetupManager.GetAllTemplates();
    }

    private void AddNewRiskMatrix()
    {
        var defaultId = RiskMatrixTemplates.FirstOrDefault(x => x.IsDefault)?.Id;

        if (defaultId == null)
        {
            DialogService.Open<InformationDialog>(Localizer["RskMtrxNoDefaultTitle"].Value,
                new Dictionary<string, object>
                {
                    {"DialogContent", Localizer["RskMtrxNoDefaultTxt"].Value }
                });
        }
        else
        {
            var newTemplate = RiskAnalysisSetupManager.CopyTemplate(defaultId.Value);
            NavigationManager.NavigateTo($"/value-risk-matrix/{newTemplate}", true);
        }
    }

    private void EditRiskMatrixInPopup(BaseModel model)
    {
        NavigationManager.NavigateTo($"/value-risk-matrix/{model.Id}");
    }

    private void DeleteRiskMatrix(RiskMatrixTemplateModel model)
    {
        if (RiskMatrixTemplates.Contains(model))
            RiskMatrixTemplates.Remove(model);

        if (model.Id > 0)
            RiskAnalysisSetupManager.DeleteTemplate(model.Id);

        RiskMatrixTemplates = [..RiskMatrixTemplates];
    }

    private void PasteRiskMatrix(BaseModel model)
    {
        var copy = model.CopyAsNew<RiskMatrixTemplateModel>();
        RiskMatrixTemplates.Add(RiskAnalysisSetupManager.UpdateTemplateModel(copy));
        RiskMatrixTemplates = [..RiskMatrixTemplates];
    }
}