using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.RiskAnalysis;

public partial class RiskScenarios
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    private ILogger<RiskScenarios> _logger;

    private List<ScenarioModel> Scenarios { get; set; }
    public UtilityGrid<ScenarioModel> ScenarioCrudGrid { get; set; }
    private Dictionary<int, string> Statusses { get; set; }

    public string ErrorText { get; set; } = string.Empty;

    private string WarningText { get; set; } = string.Empty;

    public bool ShowError { get; set; }

    private bool ShowWarning { get; set; }

    protected override void OnInitialized()
    {
        _logger = LoggerFactory.CreateLogger<RiskScenarios>();

        try
        {
            Scenarios = ScenarioManager.GetAllScenarios();
            Statusses = DropdownManager.GetStatusDict();
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error initializing scenarios page");
            ShowError = true;
            ErrorText = ex.Message;
        }
    }

    private Dictionary<string, Dictionary<int, string>> GetDropdownOverrides()
    {
        return new Dictionary<string, Dictionary<int, string>>
        {
            {nameof(ScenarioModel.Status), Statusses}
        };
    }

    private void CloneScenario(ScenarioModel scenario)
    {
        try
        {
            var copy = scenario.CopyAsNew<ScenarioModel>();
            copy.Name += " (clone)";
            Scenarios.Add(ScenarioManager.SaveScenario(copy));
            Scenarios = [.. Scenarios];
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error cloning scenario");
            ShowError = true;
            ErrorText = $"Error cloning scenario: {ex.Message}";
        }
    }

    private void SaveScenario(ScenarioModel scenario)
    {
        try
        {
            // Check for duplicate short key but still allow save
            var isDuplicate = Scenarios.Any(s => s.ShortKey.Trim() == scenario.ShortKey && s.Id != scenario.Id);

            if (isDuplicate)
            {
                // Show warning but continue with save
                ShowWarning = true;
                WarningText = Localizer["ScWarningTxt", scenario.ShortKey];
            }
            else
            {
                // Clear any previous warnings if saving without duplicates
                ShowWarning = false;
            }

            if (scenario.Id > 0)
            {
                var result = ScenarioManager.SaveScenario(scenario);
                Scenarios[Scenarios.IndexOf(scenario)] = result;
                Scenarios = [.. Scenarios];
            }
            else
            {
                Scenarios = [.. Scenarios.Append(ScenarioManager.SaveScenario(scenario))];
            }
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error saving scenario");
            ShowError = true;
            ErrorText = $"Error saving scenario: {ex.Message}";
        }
    }

    private void DeleteRiskScenario(ScenarioModel riskScenario)
    {
        try
        {
            if (riskScenario.Id <= 0) return;

            if (!ScenarioManager.DeleteScenario(riskScenario))
                return;

            Scenarios.Remove(riskScenario);
            Scenarios = [.. Scenarios];
        }
        catch (System.Exception ex)
        {
            _logger.LogError(ex, "Error deleting scenario");
            ShowError = true;
            ErrorText = $"Error deleting scenario: {ex.Message}";
        }
    }
}