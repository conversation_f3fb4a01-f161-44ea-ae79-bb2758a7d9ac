﻿using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.RiskAnalysis;

public partial class RiskMatrixTemplatePage
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; } = default!;
    [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; } = default!;
    [Inject] private NavigationManager NavigationManager { get; set; } = default!;
    [Inject] private DialogService DialogService { get; set; } = default!;
    [Inject] private IStringLocalizer<RiskMatrixTemplatePage> Localizer { get; set; } = default!;
    [Inject] private IGlobalDataService GlobalDataService { get; set; } = default!;
    [Inject] private ILookupManager LookupManager { get; set; } = default!;
    [Inject] private ILogger<RiskMatrixTemplatePage> Logger { get; set; } = default!;

    [Parameter] public int MatrixId { get; set; }

    #region Matrix properties

    private List<string> Tabs { get; } =
    [
        GridType.Description.ToString(),
        GridType.ImpactValue.ToString(),
        GridType.CustomValue.ToString(),
        GridType.Points.ToString()
    ];

    private bool CanEdit { get; set; }
    public bool ShowError { get; set; }
    public string ErrorText { get; set; }
    public string Language => GlobalDataService.Language;
    public RiskMatrixTemplateModel RiskMatrixTemplate { get; set; }
    private List<int> ActivatedSubGrids { get; set; } = [];
    private RadzenTabs TemplateTabs { get; set; }

    #endregion

    protected override void OnInitialized()
    {
        CanEdit = GlobalDataService.CanEdit;
        
        if (MatrixId == 0)
        {
            RiskMatrixTemplate = new RiskMatrixTemplateModel
            {
                MainGrid = new RiskMatrixTemplateGrid
                {
                    TableColumns = Enumerable.Range(0, 5).Select(_ => new RiskMatrixTemplateColumn()).ToList(),
                    TableContent = Enumerable.Range(0, 5).Select(_ =>
                        new RiskMatrixTemplateRow([
                            ..Enumerable.Range(0, 5)
                                .Select(_ => new RiskMatrixTemplateCell())
                        ])).ToList()
                }
            };

            var id = RiskAnalysisSetupManager.UpdateTemplate(RiskMatrixTemplate);
            NavigationManager.NavigateTo($"/value-risk-matrix/{id}", true);
        }
        else
        {
            RiskMatrixTemplate = RiskAnalysisSetupManager.GetTemplate(MatrixId);
            TemplateTabs?.Reload();
        }
    }

    private void ValidFormSubmitted()
    {
        if (!CanEdit) return;
        RiskAnalysisSetupManager.UpdateTemplate(RiskMatrixTemplate);
        NavigationManager.NavigateTo("/value-risk-matrix");
    }

    private void AddRow(int currentRow)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var newRow = new RiskMatrixTemplateRow(grid.TableColumns
            .Select(_ => new RiskMatrixTemplateCell()).ToList());

        grid.TableContent.Insert(currentRow + 1, newRow);

        // Add corresponding rows to sub grids
        foreach (var subGrid in RiskMatrixTemplate.SubGrids)
        {
            // Ensure subgrid has enough rows
            if (subGrid.TableContent.Count >= grid.TableContent.Count) 
                continue;
            
            var newSubRow = new RiskMatrixTemplateRow(subGrid.TableColumns
                .Select(_ => new RiskMatrixTemplateCell()).ToList());
            subGrid.TableContent.Insert(currentRow + 1, newSubRow);
        }

        SaveTemplate();
    }

    private void RemoveRow(int currentRow)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        grid.TableContent.RemoveAt(currentRow);

        // Remove corresponding rows from sub grids
        foreach (var subGrid in RiskMatrixTemplate.SubGrids.Where(subGrid => subGrid.TableContent.Count > currentRow))
        {
            subGrid.TableContent.RemoveAt(currentRow);
        }

        SaveTemplate();
    }

    private void AddColumn(int currentColumn)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var newColumn = new RiskMatrixTemplateColumn();
        currentColumn += 1;
        grid.TableColumns.Insert(currentColumn, newColumn);

        grid.TableContent.FirstOrDefault()?.Cells
            .Insert(currentColumn, new RiskMatrixTemplateCell {Description = "<placeholder>"});

        foreach (var tableRow in grid.TableContent.Skip(1))
        {
            tableRow.Cells.Insert(currentColumn, new RiskMatrixTemplateCell());
        }

        SaveTemplate();
    }

    private void RemoveColumn(int currentColumn)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var column = grid.TableColumns[currentColumn];
        var hasSubColumns = column.HasSubColumns;
        grid.TableColumns.Remove(column);

        if (hasSubColumns)
        {
            var subGrid = RiskMatrixTemplate.SubGrids.FirstOrDefault(x => x.ColumnId == currentColumn);

            if (subGrid != null)
                if (subGrid.TableContent.Count >= grid.TableContent.Count)
                {
                    foreach (var tableRow in subGrid.TableContent.Where(tableRow => currentColumn >= 0 && currentColumn < tableRow.Cells.Count))
                    {
                        tableRow.Cells.RemoveAt(currentColumn);
                    }
                }
        }

        foreach (var tableRow in grid.TableContent.Where(tableRow => currentColumn >= 0 && currentColumn < tableRow.Cells.Count))
        {
            tableRow.Cells.RemoveAt(currentColumn);
        }

        SaveTemplate();
    }

    private void RemoveSubColumn(int currentColumn, int subColumnToDelete)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var column = grid.TableColumns[currentColumn];
        var hasSubColumns = column.HasSubColumns;

        if (hasSubColumns)
        {
            var subGrid = RiskMatrixTemplate.SubGrids.FirstOrDefault(x => x.ColumnId == currentColumn);

            if (subGrid?.TableColumns.Count <= 1)
            {
                RiskMatrixTemplate.SubGrids.Remove(subGrid);
            }
            else if (subGrid != null)
            {
                //remove column
                subGrid.TableColumns.RemoveAt(subColumnToDelete);

                //remove columns
                if (subGrid.TableContent.Count >= grid.TableContent.Count)
                {
                    foreach (var tableRow in subGrid.TableContent)
                    {
                        tableRow.Cells.RemoveAt(subColumnToDelete);
                    }
                }
            }

            SaveTemplate();
        }
    }

    private void AddSubColumn(int currentColumn, int order = 0)
    {
        if (!CanEdit) return;
        var grid = RiskMatrixTemplate.MainGrid;
        var columnCell = RiskMatrixTemplate.MainGrid.TableContent[0].Cells[currentColumn];
        var column = grid.TableColumns[currentColumn];
        column.HasSubColumns = true;

        var subGridsForColumn = RiskMatrixTemplate.SubGrids.Where(x => x.ColumnId == currentColumn).ToList();

        //Add column in existing subgrid
        if (subGridsForColumn.Any())
        {
            //If multiple sub grids available for same column remove all but 1
            if (subGridsForColumn.Count > 1)
            {
                RiskMatrixTemplate.SubGrids.Where(x => x.ColumnId == currentColumn).ToList()
                    .RemoveRange(1, subGridsForColumn.Count);
            }

            //Add column
            var subgrid = subGridsForColumn.First();

            subgrid.TableColumns.Add(new RiskMatrixTemplateColumn());

            for (var index = 0; index < subgrid.TableContent.Count; index++)
            {
                var row = subgrid.TableContent[index];
                row.Cells.Add(new RiskMatrixTemplateCell
                {
                    RowId = index,
                    ColumnId = order,
                    Description = index == 0 ? $"{columnCell.Description} {subgrid.TableColumns.Count}" : "",
                    ShortDescription = index == 0 ? $"{columnCell.Description} {subgrid.TableColumns.Count}" : ""
                });
            }
        }
        //Add new subgrid
        else
        {
            RiskMatrixTemplate.SubGrids.Add(new RiskMatrixTemplateGrid
            {
                ColumnId = RiskMatrixTemplate.MainGrid.TableColumns.Select(x => x.Id).ToList().IndexOf(column.Id),
                TableColumns = [new()],
                TableContent = Enumerable.Range(0, RiskMatrixTemplate.MainGrid.TableContent.Count).Select(x =>
                        new RiskMatrixTemplateRow([
                            new()
                            {
                                Description = x == 0 ? $"{columnCell.Description} 1" : string.Empty,
                                ShortDescription = x == 0 ? $"{columnCell.Description} 1" : string.Empty,
                                RowId = x,
                                ColumnId = 0
                            }
                        ]))
                    .ToList()
            });
        }

        SaveTemplate();
    }

    private void AddActiveSubgrid(int column)
    {
        if (!ActivatedSubGrids.Contains(column))
            ActivatedSubGrids.Add(column);
    }

    private void RemoveActiveSubgrid(int column)
    {
        if (ActivatedSubGrids.Contains(column))
            ActivatedSubGrids.Remove(column);
    }

    private void SaveTemplate()
    {
        if (!CanEdit) return;

        try
        {
            RiskAnalysisSetupManager.UpdateTemplate(RiskMatrixTemplate);
            RiskMatrixTemplate = RiskAnalysisSetupManager.GetTemplate(MatrixId);
        }
        catch (Exception)
        {
            //TODO write error to logging
        }
    }

    private void CopyTemplate()
    {
        if (!CanEdit) return;
        var newTemplate = RiskAnalysisSetupManager.CopyTemplate(MatrixId);
        NavigationManager.NavigateTo($"/value-risk-matrix/{newTemplate}", true);
    }
}
