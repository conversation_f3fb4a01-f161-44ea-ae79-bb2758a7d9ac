﻿@page "/failures"
@inject IStringLocalizer<FailuresPage> _localizer

<h2>@_localizer["FaHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Failures" />
    </div>
</div>

<RadzenTabs>
    <Tabs>
        <RadzenTabsItem Text=@_localizer["FaFailureModesTxt"]>
            <p>
                @((MarkupString)_localizer["FaSubHeaderTxt"].Value)
            </p>
            <div class="row p-2 mb-3">
                <div class="col-12">
                    <UtilityGrid TItem=BusinessLogic.Models.Failures.FailureModeModel
                                  @ref=FailureModesGrid
                                  Data=@FailureModes
                                  FileName=@GridNames.Portfolio.FailureModes
                                  Interactive=true
                                  AddNewButton=true
                                  AllowFiltering=true
                                  SaveCallback=@SaveFailureMode
                                  DeleteCallback=@DeleteFailureMode
                                  ExternalSaveValidation=@ValidateCanSave 
                                  ExternalSaveValidationFailed=@ValidateCanSaveFailed />
                </div>
            </div>
        </RadzenTabsItem>
        <RadzenTabsItem Text=@_localizer["FaFailureCategoriesTxt"]>
            <p>
                 @((MarkupString)_localizer["FaSubHeaderFCTxt"].Value)
            </p>
            <div class="row mb-3">
                <div class="col-6">
                    <h4>@_localizer["FaCategory1Txt"]</h4>
                    <UtilityGrid TItem=BusinessLogic.Models.Failures.FailureCategory
                              @ref=FirstCategoriesGrid
                              Data=@FirstCategories
                              FileName=@GridNames.Portfolio.FailureCategory1
                              Interactive=true
                              AddNewButton=true
                              SaveCallback=@SaveFailureCategoryOne
                              DeleteCallback=@DeleteFailureCategory
                              AllowChangeColumns=false />
                </div>
                <div class="col-6">
                    <h4>@_localizer["FaCategory2Txt"]</h4>
                    <UtilityGrid TItem=BusinessLogic.Models.Failures.FailureCategory
                              @ref=SecondCategoriesGrid
                              Data=@SecondCategories
                              FileName=@GridNames.Portfolio.FailureCategory2
                              Interactive=true
                              AddNewButton=true
                              SaveCallback=@SaveFailureCategoryTwo
                              DeleteCallback=@DeleteFailureCategory
                              AllowChangeColumns=false />
                </div>
            </div>
        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>

@if (ShowError)
{
    <div class="alert alert-danger">
        @ErrorText
    </div>
}
