﻿@page "/value-risk-matrix"
@using AMprover.BusinessLogic.Models.PortfolioSetup

<h2>@Localizer["RmtsHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Value Risk Matrix" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@Localizer["RmtsHeaderTxt"] DialogContent=@Localizer["RmtsMenuTxt"] />
    </div>
</div>

<p>@((MarkupString)Localizer["RmtsSubHeaderTxt"].Value)</p>

<UtilityGrid TItem=RiskMatrixTemplateModel
            @ref=@RiskMatrixGrid
            Data=@RiskMatrixTemplates
            FileName=@GridNames.Portfolio.RiskMatrixTemplates
            Interactive=true
            UseOpenTextInsteadOfEdit=true
            EditCallBackOverride=@(args => EditRiskMatrixInPopup((RiskMatrixTemplateModel) args))
            AddNewOverride=@AddNewRiskMatrix
            DeleteCallback=@DeleteRiskMatrix
            PasteCallBack=@(args => PasteRiskMatrix(args.Item1)) />
