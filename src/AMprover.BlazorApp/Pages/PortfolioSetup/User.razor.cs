﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public partial class User
{
    [Inject] private UserManager<UserAccount> UserManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; }
    [Inject] private ILogger<User> Logger { get; set; }
    [Inject] private IStringLocalizer<User> Localizer { get; set; }

    [Parameter] public string UserId { get; set; }

    private UserAccount UserAccount { get; set; }
    private List<DepartmentModel> Departments { get; set; } = [];
    private IEnumerable<int> SelectedDepartmentIds { get; set; } = new List<int>();

    private Dictionary<string, string> AvailableRoles { get; set; } =
        AMproverIdentityConstants.Roles.GetPortfolioRoles();

    private string CurrentRole { get; set; }
    private string ValidationError { get; set; }
    private bool ShowValidations { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadUserDataAsync();
    }

    private async Task LoadUserDataAsync()
    {
        try
        {
            UserAccount = await UserManager.FindByIdAsync(UserId);
            if (UserAccount == null)
            {
                ValidationError = "User not found";
                return;
            }

            // Load departments
            Departments = RiskAnalysisSetupManager.GetAllDepartments();

            // Get user departments
            var userDepartments = RiskAnalysisSetupManager.GetUserDepartments(UserId);
            SelectedDepartmentIds = userDepartments.Select(x => x.Id);

            // Get user role
            var roles = await UserManager.GetRolesAsync(UserAccount);
            CurrentRole = roles.FirstOrDefault(x => AvailableRoles.ContainsKey(x));
        }
        catch (Exception ex)
        {
            ValidationError = $"Error loading user data: {ex.Message}";
            Logger.LogError(ex, "Error loading user data");
        }
    }

    private async Task UpdateUserAccountValidFormSubmitted()
    {
        try
        {
            ShowValidations = false;

            // Update user role if changed
            var currentRoles = await UserManager.GetRolesAsync(UserAccount);
            var currentRole = currentRoles.FirstOrDefault(x => AvailableRoles.ContainsKey(x));

            if (currentRole != CurrentRole)
            {
                if (!string.IsNullOrEmpty(currentRole))
                {
                    await UserManager.RemoveFromRoleAsync(UserAccount, currentRole);
                }

                if (!string.IsNullOrEmpty(CurrentRole))
                {
                    await UserManager.AddToRoleAsync(UserAccount, CurrentRole);
                }
            }

            // Update user departments
            // First get current user departments
            var currentUserDepartments = RiskAnalysisSetupManager.GetUserDepartments(UserId);
            var currentDepartmentIds = currentUserDepartments.Select(x => x.Id).ToList();

            // Remove departments that are no longer selected
            foreach (var departmentId in currentDepartmentIds)
            {
                if (!SelectedDepartmentIds.Contains(departmentId))
                {
                    RiskAnalysisSetupManager.DeleteUserDepartments(UserId, departmentId);
                }
            }

            // Add newly selected departments
            foreach (var departmentId in SelectedDepartmentIds)
            {
                if (!currentDepartmentIds.Contains(departmentId))
                {
                    RiskAnalysisSetupManager.AddUserDepartments(UserId, departmentId);
                }
            }

            NavigationManager.NavigateTo("/users");
        }
        catch (Exception ex)
        {
            ValidationError = $"Error updating user: {ex.Message}";
            Logger.LogError(ex, "Error updating user");
        }
    }

    private void UpdateUserAccountInvalidFormSubmitted()
    {
        ShowValidations = true;
        ValidationError = "Please correct the validation errors";
    }
}