﻿@page "/data-preparation"
@inject IStringLocalizer<DataPreparationPage> _localizer

<h2>@_localizer["DpHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Data Preparation" />
    </div>
</div>

<div class="row card-row">
    <div class="col-sm-1"></div>
    <div class="tile col-sm-3" @onclick="@(() => Navigate("/scenarios"))">
        <h4> <img src="/images/Scenario.png" class="card-image" /><span class="card-title"> @_localizer["DpScenariosHdrTxt"]</span></h4>
        <hr />
            <p class="card-text">
                @((MarkupString)_localizer["DpScenariosTxt"].Value)
            </p>
    </div>
    <div class="tile col-sm-3" @onclick="@(() => Navigate("/failures"))">
        <h4> <img src="/images/FailureMode.png" class="card-image" /><span class="card-title"> @_localizer["DpFailuresHdrTxt"]</span></h4>
        <hr />
            <p class="card-text">
                @((MarkupString)_localizer["DpFailuresTxt"].Value)
            </p>
    </div>
    <div class="tile col-sm-3" @onclick="@(() => Navigate("/objects"))">
        <h4><img src="/images/GeneralHierarchy.png" class="card-image" /><span class="card-title"> @_localizer["DpHierarchyObjectsHdrTxt"]</span></h4>
        <hr />
            <p class="card-text">@((MarkupString)_localizer["DpHierarchyObjectsTxt"].Value)</p>
    </div>
</div>
<div class="row card-row">
    <div class="col-sm-1"></div>
    <div class="tile col-sm-3" @onclick="@(() => Navigate("/action-setting"))">
        <h4><img src="/images/ActionSettings.png" class="card-image" /><span class="card-title"> @_localizer["DpActionSettingsHdrTxt"]</span></h4>
        <hr />
        <p class="card-text">@((MarkupString)_localizer["DpActionSettingsTxt"].Value)</p>
    </div>
    <div class="tile col-sm-3" @onclick="@(() => Navigate("/common-actions"))">
        <h4><img src="/images/CommonAction.png" class="card-image" /><span class="card-title"> @_localizer["DpCommonActionsHdrTxt"]</span></h4>
        <hr />
            <p class="card-text">@((MarkupString)_localizer["DpCommonActionsTxt"].Value)</p>
    </div>
    <div class="tile col-sm-3" @onclick="@(() => Navigate("/cluster-cost-settings"))">
        <h4><img src="/images/ClusterCost.png" class="card-image" /><span class="card-title"> @_localizer["DpClusterCost"]</span></h4>
        <hr />
            <p class="card-text">@((MarkupString)_localizer["DpClusterCostTxt"].Value)</p>
    </div>
</div>
<div class="row card-row">
    <div class="col-sm-1"></div>
    <div class="tile col-sm-3" @onclick="@(() => Navigate("/settings"))">
        <h4><img src="/images/Instellingen.png" class="card-image" /><span class="card-title"> @_localizer["DpSettingsHdrTxt"]</span></h4>
        <hr />
        <p class="card-text">@((MarkupString)_localizer["DpSettingsTxt"].Value)</p>
    </div>
    <div class="tile col-sm-3" @onclick="@(() => Navigate("/asset-breakdown-structure"))">
        <h4><img src="/images/Abs.png" class="card-image" /><span class="card-title"> @_localizer["DpAbsHdrTxt"]</span></h4>
        <hr />
        <p class="card-text">@((MarkupString)_localizer["DpAbsTxt"].Value)</p>
    </div>
</div>
