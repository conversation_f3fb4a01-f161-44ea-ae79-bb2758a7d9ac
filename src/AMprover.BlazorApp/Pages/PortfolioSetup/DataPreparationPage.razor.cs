using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON><PERSON>;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public partial class DataPreparationPage
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }

    private void Navigate(string page)
    {
            NavigationManager.NavigateTo(page);
        }
}