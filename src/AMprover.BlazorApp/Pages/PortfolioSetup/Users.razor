﻿@page "/users"
@using AMprover.Data.Constants
@using AMprover.Data.Entities.Identity
@inject NavigationManager _navigationManager
@inject IStringLocalizer<Users> _localizer

@code {
    private static string AdminRoles => $"{RoleConstants.Administrators},{RoleConstants.PortfolioAdministrators}";
}

<AuthorizeView Roles="@AdminRoles">
    <Authorized>
        <h3>User management</h3>
        <p>Below you can manage all users of this portfolios.</p>

        <RadzenDataGrid TItem="UserAccount" Data="@ColUsers" FilterMode="FilterMode.Simple" AllowFiltering="true" AllowPaging="true" AllowSorting="true" PageSize="15">
            <Columns>
                <RadzenDataGridColumn TItem="UserAccount" Property="@nameof(UserAccount.Id)" Title="@nameof(UserAccount.Id)"/>
                <RadzenDataGridColumn TItem="UserAccount" Property="@nameof(UserAccount.Name)" Title="@nameof(UserAccount.Name)"/>
                <RadzenDataGridColumn TItem="UserAccount" Property="@nameof(UserAccount.UserName)" Title="@nameof(UserAccount.UserName)"/>
                <RadzenDataGridColumn TItem="UserAccount" Property="@nameof(UserAccount.Company)" Title="@nameof(UserAccount.Company)"/>
                <RadzenDataGridColumn TItem="UserAccount" Filterable="false" Sortable="false" TextAlign="TextAlign.Center" Width="10%">
                    <Template Context="useraccount">
                        @if (!IsAdministrator(useraccount))
                        {
                            <RadzenButton Icon="edit" Text="Edit" Size="ButtonSize.Small" Click="@(args => _navigationManager.NavigateTo($"/users/{useraccount.Id}"))"/>
                        }
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
        
        <div class="mt-5">
            <h3>Department management</h3>
            <p>Below you can manage all departments of this portfolios.</p>

            <RadzenDataGrid @ref="@DepartmentGrid" TItem="DepartmentModel" Data="@Departments" AllowPaging="true"
                            PageSize="10" AllowSorting="true">
                <Columns>
                    <RadzenDataGridColumn TItem="DepartmentModel" Property="ShortKey" Title="@_localizer["Code"]"/>
                    <RadzenDataGridColumn TItem="DepartmentModel" Property="Description" Title="@_localizer["Description"]"/>

                    <RadzenDataGridColumn TItem="DepartmentModel" Filterable="false" Sortable="false"
                                          TextAlign="TextAlign.Center" Width="10%">
                        <Template Context="department">
                            <RadzenButton Icon="edit" Text="@_localizer["Edit"]" Size="ButtonSize.Small"
                                          Click="@(_ => _navigationManager.NavigateTo($"/departments/{department.Id}"))"/>
                            <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger" Text="@_localizer["Delete"]" Size="ButtonSize.Small"
                                          Click="@(_ => DeleteDepartment(department.Id))"/>
                        </Template>
                    </RadzenDataGridColumn>
                </Columns>
            </RadzenDataGrid>
        </div>
        <div class="mt-3">
            <RadzenButton Icon="add" Text="@_localizer["AddDepartment"]" ButtonStyle="ButtonStyle.Primary"
                          Click="@(args => _navigationManager.NavigateTo("/departments/0"))"/>
        </div>
    </Authorized>
</AuthorizeView>
