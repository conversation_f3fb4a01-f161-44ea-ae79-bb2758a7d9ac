using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic.Enums;
using System.Collections.Generic;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using Microsoft.AspNetCore.Components;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public partial class ClusterCostPage
{
    [Inject] private IPortfolioSetupManager PortfolioSetupManager { get; set; }
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private ILogger<ClusterCostPage> Logger { get; set; }

    public Dictionary<CommonCostType, string> CostType { get; private set; } = new();
    private UtilityGrid<CommonCostModel> CommonCostsGrid { get; set; }
    private List<CommonCostModel> CommonCosts { get; set; }

    protected override void OnInitialized()
    {
            CommonCosts = PortfolioSetupManager.GetCommonCosts();
            CostType = PortfolioSetupManager.GetCostLevelTypes();

            base.OnInitialized();
        }

    // Common Costs
    private void DeleteCommonCost(CommonCostModel commonCost)
    {
            if (CommonCosts.Contains(commonCost))
            {
                CommonCosts.Remove(commonCost);
                CommonCosts = [.. CommonCosts];
            }

            PortfolioSetupManager.DeleteCommonCost(commonCost);
        }

    public string GetClusterCostType(CommonCostType clusterCostType)
    {
            if (CostType.TryGetValue(clusterCostType, out var type))
                return type;

            Logger.LogError($"{nameof(GetClusterCostType)} was called but {nameof(CostType)} has not been filled yet");
            return clusterCostType.ToString();
        }

    private void UpdateCommonCost(CommonCostModel commonCost)
    {
            commonCost = PortfolioSetupManager.UpdateCommonCost(commonCost);
            AddOrUpdateCommonCostsGrid(commonCost);
        }

    private void AddOrUpdateCommonCostsGrid(CommonCostModel model)
    {
            var rowIndex = CommonCosts.FindIndex(x => x.Id == model.Id);
            if (rowIndex >= 0)
            {
                CommonCosts[rowIndex] = model;
            }
            else
            {
                CommonCosts.Add(model);
            }

            CommonCostsGrid.Grid.Reload();
        }

    private void PasteCommonCostCallback(CommonCostModel model)
    {
            var copiedCost = model.CopyAsNew<CommonCostModel>();
            CommonCosts.Add(PortfolioSetupManager.UpdateCommonCost(copiedCost));
            CommonCosts = [..CommonCosts];
        }
}