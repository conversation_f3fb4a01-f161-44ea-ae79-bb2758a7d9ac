using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Constants;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public partial class Department
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; }

    [Parameter] public int DepartmentId { get; set; }

    private static string AdminRoles => $"{RoleConstants.Administrators},{RoleConstants.PortfolioAdministrators}";

    private DepartmentModel DepartmentModel { get; set; }

    protected override void OnInitialized()
    {
            if (DepartmentId != 0)
            {
                DepartmentModel = RiskAnalysisSetupManager.GetDepartment(DepartmentId);
            }
            else
            {
                DepartmentModel = new DepartmentModel
                {
                    Id = 0,
                    Description = string.Empty,
                    ShortKey = string.Empty
                };
            }

            base.OnInitialized();
        }

    private void HandleValidSubmit()
    {
            // Save department
            RiskAnalysisSetupManager.CreateOrUpdateDepartment(DepartmentModel);

            // Navigate back to list
            NavigationManager.NavigateTo("/users");
        }

    private void Cancel()
    {
            NavigationManager.NavigateTo("/users");
        }
}