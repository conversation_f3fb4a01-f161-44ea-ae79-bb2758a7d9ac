﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Infrastructure;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public partial class Users
{
    [Inject] private UserManager<UserAccount> UserManager { get; set; }
    [Inject] private IAssetManagementPortfolioResolver PortfolioResolver { get; set; }
    [Inject] private IRiskAnalysisSetupManager RiskAnalysisManager { get; set; }
    [Inject] private ILogger<Users> Logger { get; set; }

    private RadzenDataGrid<DepartmentModel> DepartmentGrid { get; set; }
    private List<DepartmentModel> Departments { get; set; } = [];
    private Dictionary<string, HashSet<string>> UserRoleCache { get; set; } = [];
    private List<UserAccount> ColUsers { get; set; } = [];
    private string StrError { get; set; } = string.Empty;
    private bool IsLoading { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            IsLoading = true;

            await LoadUsersAsync();
            LoadDepartments();

            // Cache user roles
            await CacheUserRolesAsync();
        }
        catch (Exception ex)
        {
            StrError = $"Error loading data: {ex.Message}";
            Logger.LogError(ex, "Error loading data");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadUsersAsync()
    {
        try
        {
            var currentPortfolio = PortfolioResolver.GetCurrentPortfolio();
            if (currentPortfolio == null)
            {
                StrError = "No active portfolio selected";
                return;
            }

            // Get users assigned to this portfolio
            var portfolioUsers = await UserManager.Users
                .Where(x => x.PortfolioAssignments.Any(y => y.PortfolioId == currentPortfolio.Id))
                .ToListAsync();

            var nonAdminUsers = new List<UserAccount>();
            foreach (var user in portfolioUsers)
            {
                if (!await UserManager.IsInRoleAsync(user, RoleConstants.Administrators))
                {
                    nonAdminUsers.Add(user);
                }
            }

            ColUsers = [.. nonAdminUsers
                .OrderBy(ua => GetDomainFromEmail(ua.UserName))
                .ThenBy(ua => ua.Name)];
        }
        catch (Exception ex)
        {
            StrError = $"Error loading users: {ex.Message}";
            Logger.LogError(ex, "Error loading users");
        }
    }

    private void LoadDepartments()
    {
        try
        {
            Departments = RiskAnalysisManager.GetAllDepartments();
        }
        catch (Exception ex)
        {
            StrError = $"Error loading departments: {ex.Message}";
            Logger.LogError(ex, "Error loading departments");
        }
    }

    private async Task CacheUserRolesAsync()
    {
        foreach (var user in ColUsers)
        {
            var roles = await UserManager.GetRolesAsync(user);
            UserRoleCache[user.Id] = [.. roles];
        }
    }

    public bool IsAdministrator(UserAccount user)
    {
        if (UserRoleCache.TryGetValue(user.Id, out var roles))
        {
            return roles.Contains(RoleConstants.Administrators);
        }
        return false;
    }

    public async Task DeleteDepartment(int departmentId)
    {
        RiskAnalysisManager.DeleteDepartment(departmentId);
        Departments.RemoveAll(x => x.Id == departmentId);
        await DepartmentGrid.Reload();
    }

    private static string GetDomainFromEmail(string email)
    {
        if (string.IsNullOrEmpty(email) || !email.Contains('@'))
            return string.Empty;

        return email.Split('@')[1];
    }
}