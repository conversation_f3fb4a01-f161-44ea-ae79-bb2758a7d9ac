﻿@page "/action-setting"
@using AMprover.BusinessLogic.Models.Sapa
@inject IStringLocalizer<ActionSettingPage> _localizer

<h2>@_localizer["AsHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Action Setting" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@_localizer["AsHeaderTxt"] DialogContent=@_localizer["AsMenuTxt"] />
    </div>
</div>

<p>@((MarkupString)_localizer["AsSubHeaderTxt"].Value)</p>

<RadzenTabs>
    <Tabs>

        <!-- Executor -->
        <RadzenTabsItem Text=@_localizer["AsExecutorTabTxt"]>
            <div class="row mb-3">
                <div class="col-md-8 col-6">
                    <UtilityGrid TItem=ExecutorModel
                              @ref=ExecutorCrudGrid
                              Data=@Executors
                              FileName=@GridNames.Portfolio.Executors
                              Interactive=true
                              AddNewButton=true
                              AllowFiltering=true
                              SaveCallback=@SaveExecutor
                              DeleteCallback=@DeleteExecutor />
                </div>
            </div>
        </RadzenTabsItem>

        <!-- Initiator -->
        <RadzenTabsItem Text=@_localizer["AsInitiatorTabTxt"]>
            <div class="row mb-3">
                <div class="col-md-8 col-6">
                    <UtilityGrid TItem=InitiatorModel
                              @ref=InitiatorCrudGrid
                              Data=@Initiators
                              FileName=@GridNames.Portfolio.Initiators
                              Interactive=true
                              AddNewButton=true
                              AllowFiltering=true
                              SaveCallback=@SaveInitiator
                              DeleteCallback=@DeleteInitiator />
                </div>
            </div>
        </RadzenTabsItem>

        <!-- IntervalUnits -->
        <RadzenTabsItem Text=@_localizer["AsIntervalUnitsTabTxt"]>
            <div class="row mb-3">
                <div class="col-12">
                    <UtilityGrid TItem=IntervalUnitModel
                              @ref=IntervalCrudGrid
                              Data=@IntervalUnits
                              FileName=@GridNames.Portfolio.IntervalUnits
                              Interactive=true
                              AddNewButton=true
                              AllowFiltering=true
                              SaveCallback=@SaveIntervalUnit
                              DeleteCallback=@DeleteIntervalUnit />
                </div>
            </div>
        </RadzenTabsItem>
        
        <!-- Task policy -->
        <RadzenTabsItem Text=@_localizer["AsTaskPolicyTabTxt"]>
            <div class="row mb-3">
                <div class="col-12">
                    <UtilityGrid TItem=PolicyModel
                              @ref=PolicyCrudGrid
                              Data=@Policies
                              FileName=@GridNames.Portfolio.TaskPolicies
                              Interactive=true
                              AddNewButton=true
                              AllowFiltering=true
                              SaveCallback=@SavePolicy
                              DeleteCallback=@DeletePolicy />
                </div>
            </div>
        </RadzenTabsItem>

        <!-- Workpackage -->
        <RadzenTabsItem Text=@_localizer["AsWorkpackageTabTxt"]>
            <div class="row mb-3">
                <div class="col-12">
                    <UtilityGrid TItem=WorkPackageModel
                              @ref=WorkPackageCrudGrid
                              Data=@WorkPackages
                              FileName=@GridNames.Portfolio.Workpackages
                              Interactive=true
                              AddNewButton=true
                              AllowFiltering=true
                              SaveCallback=@SaveWorkPackage
                              DeleteCallback=@DeleteWorkPackage
                              DropDownOverrides=@GetWorkPackageDropDowns()
                              NewItemTemplate=@(new WorkPackageModel { IntervalUnit = IntervalUnits.FirstOrDefault()?.Id ?? 0 }) />
                </div>
            </div>
        </RadzenTabsItem>

        <!-- SapaWorkpackage -->
        <RadzenTabsItem Text=@_localizer["AsSapaWorkpackageTabTxt"]>
            <div class="row mb-3">
                <div class="col-12">
                    <UtilityGrid TItem=SapaWorkpackageModel
                                 @ref=SapaWorkpackageCrudGrid
                                 Data=@SapaWorkpackages
                                 FileName=@GridNames.Portfolio.SapaWorkpackages
                                 Interactive=true
                                 AddNewButton=true
                                 AllowFiltering=true
                                 SaveCallback=@SaveSapaWorkPackage
                                 DeleteCallback=@DeleteSapaWorkPackage
                                 NewItemTemplate=@(new SapaWorkpackageModel()) />
                </div>
            </div>
        </RadzenTabsItem>

        <!-- AttachmentCategories -->
        <RadzenTabsItem Text=@_localizer["AsAtchCatHeaderTxt"]>
            <div class="row mb-3">
                <div class="col-12">
                    <UtilityGrid TItem=AttachmentCategoryModel
                                 @ref=AttachmentCategoryCrudGrid
                                 Data=@AttachmentCategories
                                 FileName=@GridNames.Portfolio.AttachmentCategories
                                 Interactive=true
                                 AddNewButton=true
                                 AllowFiltering=true
                                 SaveCallback=@SaveAttachmentCategory
                                 DeleteCallback=@DeleteAttachmentCategory
                                 NewItemTemplate=@(new AttachmentCategoryModel()) />
                </div>
            </div>
        </RadzenTabsItem>

        <!-- Attachments -->
        <RadzenTabsItem Text=@_localizer["AsAtchHeaderTxt"]>
            <div class="row mb-3">
                <div class="col-12">
                    <UtilityGrid TItem=AttachmentModel
                                 @ref=AttachmentCrudGrid
                                 Data=@Attachments
                                 FileName=@GridNames.Portfolio.Attachments
                                 Interactive=true
                                 AddNewButton=true
                                 AllowFiltering=true
                                 SaveCallback=@SaveAttachment
                                 DeleteCallback=@DeleteAttachment
                                 DropDownOverrides=@AttachmentDropdownOverrides
                                 NewItemTemplate=@(new AttachmentModel()) />
                </div>
            </div>
        </RadzenTabsItem>
        
        <!-- Additional data -->
        <RadzenTabsItem Text=@_localizer["AsAdditionalDataTabTxt"]>
            <div class="row mb-3">
                <div class="col-md-8 col-6">
                    <UtilityGrid TItem=AdditionalDataModel
                                 @ref=@AdditionalDataGrid
                                 Data=@AdditionalData
                                 FileName=@GridNames.Portfolio.AdditionalData
                                 Interactive=true
                                 AddNewButton=true
                                 AllowFiltering=true
                                 SaveCallback=@SaveAdditionalData
                                 DeleteCallback=@DeleteAdditionalData />
                </div>
            </div>
        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>
