using System.Collections.Generic;
using AMprover.BlazorApp.Enums;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Cluster;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BlazorApp.Pages.PortfolioSetup;

public partial class CommonActionEdit
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IPortfolioSetupManager PortfolioSetupManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private ILogger<CommonActionEdit> Logger { get; set; }

    // Dropdowns
    private Dictionary<int, string> ExecutorsDict { get; set; } = [];
    private Dictionary<int, string> InitiatorsDict { get; set; } = [];
    private Dictionary<int, string> WorkPackageDict { get; set; } = [];
    private Dictionary<int, string> IntervalUnitsDict { get; set; } = [];
    private Dictionary<int, string> PolicyDict { get; set; } = [];
    private Dictionary<int, string> UnitTypeDict { get; set; } = [];
    private Dictionary<string, string> TaskDict { get; set; } = [];

    [Parameter] public int CommonTaskId { get; set; }

    [Parameter] public EventCallback<CommonTaskModel> Callback { get; set; }

    public CommonTaskModel CommonTask { get; set; }

    public string ErrorText { get; set; }

    private EntityEditorMode EditorMode
    {
        get
        {
                return CommonTaskId switch
                {
                    0 => EntityEditorMode.Create,
                    _ => EntityEditorMode.Update,
                };
            }
    }

    protected override void OnInitialized()
    {
            ExecutorsDict = DropdownManager.GetExecutorDict();
            InitiatorsDict = DropdownManager.GetInitiatorDict();
            WorkPackageDict = DropdownManager.GetWorkpackageDict();
            IntervalUnitsDict = DropdownManager.GetIntervalUnitDict();
            PolicyDict = DropdownManager.GetPolicyDict();
            UnitTypeDict = DropdownManager.GetLookupUserDefinedByFilterDict("UnitTypes");
            TaskDict = LookupManager.GetLookupByFilterDict("MeasureType", true);

            CommonTask = EditorMode switch
            {
                EntityEditorMode.Create => new CommonTaskModel { Id = CommonTaskId },
                _ => PortfolioSetupManager.GetCommonAction(CommonTaskId)
            };

            base.OnInitialized();
        }

    private async Task ValidTaskSubmitted(CommonTaskModel commonAction)
    {
            await CreateOrUpdateObjectAndNavigate(commonAction);
        }

    private async Task CreateOrUpdateObjectAndNavigate(CommonTaskModel commonAction)
    {
            var updatedCommonAction = PortfolioSetupManager.UpdateCommonAction(commonAction);
            await Callback.InvokeAsync(updatedCommonAction);
            DialogService.Close();
        }

    private void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
            ErrorText = "Update NOT executed, invalid form submitted";
            Logger.LogWarning($"Invalid {EditorMode} form submitted for {nameof(CommonTaskModel)} with Id {CommonTaskId}.");
            Logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject(args));
        }
}