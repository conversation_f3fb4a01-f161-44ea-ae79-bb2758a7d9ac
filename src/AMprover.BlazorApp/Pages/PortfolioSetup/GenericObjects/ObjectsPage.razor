﻿@page "/objects"

<h2>@Localizer["ObHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Objects" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@Localizer["ObHeaderTxt"] DialogContent=@Localizer["ObMenuTxt"] />
    </div>
</div>

<p>@((MarkupString)Localizer["ObSubHeaderTxt"].Value)</p>

<RadzenTabs>
    <Tabs>

        @foreach (var type in Objects)
        {
            <RadzenTabsItem Text=@GetObjectLevel(type.Key)>
                <div class="row mb-3">
                    <div class="col-12">

                        <UtilityGrid TItem=ObjectModel
                                  @ref=ObjectGrid
                                  Data=@Objects[type.Key]
                                  FileName=@GridNames.Portfolio.Objects
                                  Interactive=true
                                  AddNewButton=true
                                  AllowFiltering=true
                                  CloneCallBack=@CloneObject
                                  SaveCallback=@SaveObject
                                  DeleteCallback=@DeleteObject
                                  NewItemTemplate=@(new ObjectModel { Level = (int)type.Key }) 
                                  ExternalSaveValidation=@ValidateCanSave 
                                  ExternalSaveValidationFailed=@ValidateCanSaveFailed 
                                  MaxRows=50 />

                    </div>
                </div>
            </RadzenTabsItem>
        }
        <RadzenTabsItem Text=@Localizer["ObLevelHeaderTxt"].Value>
            <div class="row">
                <div class="col-xl-6 col-lg-8 col-10">

                     <UtilityGrid TItem=GenericObjectLevel
                            @ref=LevelsCrudGrid
                            Data=@Levels
                            FileName=@GridNames.Portfolio.ObjectLevels
                            Interactive=true
                            SaveCallback=@SaveRow
                            AllowChangeColumns=false />

                </div>
            </div>
        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>
