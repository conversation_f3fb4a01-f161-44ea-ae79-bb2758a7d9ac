using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;

namespace AMprover.BlazorApp.Pages.PortfolioSetup.GenericObjects;

public partial class ObjectsPage
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; }
    [Inject] private IStringLocalizer<ObjectsPage> Localizer { get; set; }
    [Inject] private DialogService DialogService { get; set; }

    private Dictionary<ObjectLevel, List<ObjectModel>> Objects { get; set; } = new();
    public UtilityGrid<ObjectModel> ObjectGrid { get; set; }
    private List<GenericObjectLevel> Levels { get; set; }
    public UtilityGrid<GenericObjectLevel> LevelsCrudGrid { get; set; }
    private Dictionary<ObjectLevel, string> ObjectLevels { get; set; }

    protected override void OnInitialized()
    {
            Objects = ObjectManager.GetAllObjects();
            ObjectLevels = ObjectManager.GetObjectLevelNames();
            LoadLevels();
            base.OnInitialized();
        }

    private void LoadLevels()
    {
            Levels = ObjectManager.GetGenericObjectLevels();
        }

    private void SaveRow(GenericObjectLevel level)
    {
            Levels[level.Level] = ObjectManager.UpdateGenericObjectLevel(level);
            Levels = [..Levels];
        }

    private void CloneObject(ObjectModel model)
    {
            var copy = model.CopyAsNew();
            copy.Name += " (clone)";
            SaveObject(copy);
        }

    private void SaveObject(ObjectModel model)
    {
            var objects = Objects[model.GetObjectLevel()];
            var result = ObjectManager.SaveObject(model);

            if (model.Id is > 0 && objects.IndexOf(model) >= 0)
            {
                objects[objects.IndexOf(model)] = result;
                Objects[model.GetObjectLevel()] = [..objects];
            }
            else
            {
                Objects[model.GetObjectLevel()] = [..objects.Append(result)];
            }
        }

    private bool ValidateCanSave(ObjectModel model)
    {
            var objects = Objects[model.GetObjectLevel()];

            // Objects must have unique Name AND Shortkey, check ID to make sure your not comparing the object to itself
            return !objects.Any(x => x.Id != model.Id
                && (x.Name.Trim().Equals(model.Name.Trim(), StringComparison.OrdinalIgnoreCase)
                 || x.ShortKey.Trim().Equals(model.ShortKey.Trim(), StringComparison.OrdinalIgnoreCase)));
        }

    private void ValidateCanSaveFailed(ObjectModel model)
    {
            ShowErrorMessage(Localizer["ObNoDuplicateNamesOrShortkeyTxt"]);
        }

    private async Task DeleteObject(ObjectModel model)
    {
            if (model == null || model.Id == 0) return;

            if (ObjectManager.ObjectHasDependencies(model.Id))
            {
                DialogService.Close();

                // The Are You Sure? dialog is still open, we cannot open another dialog until it is closed
                // await Task.Delay() to fix async issue
                await Task.Delay(1);

                DialogService.Open<InformationDialog>(Localizer["ObCannotBeDeletedTitle"],
                    new Dictionary<string, object>
                    {
                        { "DialogContent", Localizer["ObCannotBeDeletedTxt"].Value}
                    });
            }
            else
            {
                ObjectManager.DeleteObject(model.Id);

                Objects[model.GetObjectLevel()].Remove(model);
                Objects[model.GetObjectLevel()] = [..Objects[model.GetObjectLevel()]];
            }
        }

    private string GetObjectLevel(ObjectLevel level)
    {
            return ObjectLevels.TryGetValue(level, out var value) ? value : level.ToString();
        }

    private void ShowErrorMessage(string errorMessage)
    {
            DialogService.Open<NotificationDialog>("Error",
                new Dictionary<string, object>
                {
                    {nameof(NotificationDialog.Texts), new List<string> {errorMessage}}
                });
        }
}