﻿@page "/commonactions/{CommonTaskId:int}"
@inject IStringLocalizer<CommonActionEdit> _localizer
@inject IGlobalDataService GlobalDataService;

@if (CommonTask != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem=CommonTaskModel Data=@CommonTask
                                      Submit=@ValidTaskSubmitted OnInvalidSubmit=@InvalidTaskSubmitted>
        <div class="row">
            <div class="col-sm-2">
                <div class="form-group neg-margin">
                    <label>Id:</label>
                    <label class="form-control">@CommonTask.Id</label>
                </div>
            </div>
            <div class="col-sm-6">
                <AMproverTextBox @bind-Value="CommonTask.Name" Label=@_localizer["CaeNameLbl"] Required=true MaxLength="60" />
            </div>
            <div class="col-sm-4">
                <AMproverTextBox @bind-Value="CommonTask.ReferenceId" Label=@_localizer["CaeReferenceIdLbl"] MaxLength="30" />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMproverDictionaryDropdown AllowFiltering="true" AllowClear="true" Required=true
                                Data=@PolicyDict.ToNullableDictionary()
                                @bind-Value=@CommonTask.MxPolicyId 
                                Label=@_localizer["CaePolicyLbl"] />
            </div>
            <div class="col-sm-4">
                <AMproverDictionaryDropdown AllowClear=true AllowFiltering=true Required=true
                                            Data=@TaskDict
                                            @bind-Value=@CommonTask.Type
                                            Label=@_localizer["CaeActionTypeLbl"] />
            </div>
            <div class="col-sm-2">
                <AMproverNumberInput Label="Priority" Format="0" TValue="int?" @bind-Value="CommonTask.PriorityCode" />
            </div>
            <div class="col-sm-2">
                <AMproverTextBox @bind-Value="CommonTask.FilterRef" Label=@_localizer["CaeFilterRefLbl"] MaxLength="20" />
            </div>
        </div>

        <RadzenTabs>
            <Tabs>
                <RadzenTabsItem Text=@_localizer["CaeDescriptionLbl"]>
                    <div class="col-sm-12">
                        <div class="row">
                            <div class="col-sm-5">
                                <AMproverTextArea @bind-Value=@CommonTask.Description Cols="30" Rows="3" Label=@_localizer["CaeDescriptionLbl"] />
                                <AMproverTextArea @bind-Value=@CommonTask.Remark Cols="30" Rows="3" Label=@_localizer["CaeRemarksLbl"] />
                            </div>
                            <div class="col-sm-7">
                                <label>@_localizer["CaeModifiableLbl"]:</label>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <Radzen.Blazor.RadzenCheckBox class="form-control mt-5 ml-3" @bind-Value="@CommonTask.CostModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>
                                    <div class="col-sm-4">
                                        <AMproverNumberInput Label=@_localizer["CaeCostLbl"] Format="c0" TValue="decimal?" @bind-Value="CommonTask.Costs" class="form-control" />
                                    </div>
                                    <div class="col-sm-5">
                                        <AMproverDictionaryDropdown Label="UnitType" AllowClear="true" AllowFiltering="true" class="form-control"
                                                        Data="@UnitTypeDict" @bind-Value="@CommonTask.UnitType" Required=true Min=1 />
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <AMproverCheckbox class="form-control mt-5 ml-3" @bind-Value="@CommonTask.InitiatorModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>
                                    <div class="col-sm-9">
                                        <AMproverDictionaryDropdown Label=@_localizer["CaeInitiatorLbl"] AllowClear="true" AllowFiltering="true" class="form-control"
                                                                    Data=@InitiatorsDict.ToNullableDictionary()
                                                                    @bind-Value=@CommonTask.InitiatorId Required=true />
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <Radzen.Blazor.RadzenCheckBox class="form-control mt-5 ml-3" @bind-Value="@CommonTask.ExecutorModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>

                                    <div class="col-sm-9">
                                        <AMproverDictionaryDropdown Label=@_localizer["CaeExecutorLbl"] AllowFiltering="true" AllowClear="true" class="form-control"
                                                                    Data=@ExecutorsDict.ToNullableDictionary()
                                                                    @bind-Value=@CommonTask.ExecutorId Required=true />
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <Radzen.Blazor.RadzenCheckBox class="form-control mt-5 ml-3" @bind-Value="@CommonTask.WorkPackageModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>

                                    <div class="col-sm-9">
                                        <AMproverDictionaryDropdown Label=@_localizer["CaeWorkPackageLbl"] AllowFiltering="true" AllowClear="true" class="form-control"
                                                                    Data=@WorkPackageDict.ToNullableDictionary()
                                                                    @bind-Value=@CommonTask.WorkPackageId Required=true/>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-2 neg-margin">
                                        <Radzen.Blazor.RadzenCheckBox class="form-control mt-5 ml-3" @bind-Value="@CommonTask.IntervalModifiable" CssClass="e-primary" TValue=bool? />
                                    </div>
                                    <div class="col-sm-4">
                                        <AMproverNumberInput Label=@_localizer["CaeIntervalLbl"] Format="0" TValue="decimal?" @bind-Value="CommonTask.Interval" class="form-control" />
                                    </div>
                                    <div class="col-sm-5">
                                        <AMproverDictionaryDropdown Label="IntervalUnit" AllowClear="true" AllowFiltering="true" class="form-control"
                                                                    Data=@IntervalUnitsDict.ToNullableDictionary()
                                                                    @bind-Value=@CommonTask.IntervalUnitId Required=true />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>

                <RadzenTabsItem Text=@_localizer["CaeGeneralItemsLbl"]>
                    <div class="row">
                        <div class="col-sm-12">
                            <AMproverTextArea @bind-Value=@CommonTask.GeneralDescription Cols="30" Rows="3" Label="General description" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <AMproverTextArea @bind-Value=@CommonTask.Permit Cols="30" Rows="3" Label="@_localizer["CaePermitLbl"]" />
                        </div>
                        <div class="col-sm-6">
                            <AMproverTextArea @bind-Value=@CommonTask.Responsible Cols="30" Rows="3" Label=@_localizer["CaeResponsibleLbl"] />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-3">
                            <AMproverNumberInput  Label=@_localizer["CaeDownTimeLbl"] Format="0" TValue="decimal?" @bind-Value="CommonTask.DownTime" class="form-control" />
                        </div>
                        <div class="col-sm-3">
                            <AMproverNumberInput  Label=@_localizer["CaeDurationLbl"] Format="0" TValue="decimal?" @bind-Value="CommonTask.Duration" class="form-control" />
                        </div>
                        <div class="col-sm-3">
                        </div>
                        <div class="col-sm-3">
                            <AMproverNumberInput Label=@_localizer["CaeSortOrderLbl"] Format="0" TValue="int?" @bind-Value="CommonTask.SortOrder" class="form-control" />
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@_localizer["CaePropertiesLbl"]>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>@_localizer["CaeInitiatedByLbl"]:</label>
                                <label>@CommonTask.InitiatedBy</label>
                            </div>
                        </div>
                        @if (CommonTask.DateInitiated != null)
                        {
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>@_localizer["CaeOnLbl"]:</label>
                                    <label>@CommonTask.DateInitiated.Value.ToString("dd-MM-yyyy hh:mm")</label>
                                </div>
                            </div>
                        }
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label>@_localizer["CaeInitiatedByLbl"]:</label>
                                <label>@CommonTask.ModifiedBy</label>
                            </div>
                        </div>
                        @if (CommonTask.DateModified != null)
                        {
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>@_localizer["CaeOnLbl"]:</label>
                                    <label>@CommonTask.DateModified.Value.ToString("dd-MM-yyyy hh:mm")</label>
                                </div>
                            </div>
                        }
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>

        <RadzenButton Disabled=!GlobalDataService.CanEdit type="submit" class="btn btn-primary" Text="Save" />
    </Radzen.Blazor.RadzenTemplateForm>
}