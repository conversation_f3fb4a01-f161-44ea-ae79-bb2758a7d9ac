﻿@page "/departments/{DepartmentId:int}"
@inject IStringLocalizer<Department> _localizer

<AuthorizeView Roles="@AdminRoles">
    <Authorized Context="authContext">
        @if (DepartmentModel != null)
        {
            <h4>Update department for: @DepartmentModel.Description</h4>

            <EditForm Model="@DepartmentModel" OnValidSubmit="@HandleValidSubmit">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="form-group">
                    <label for="shortkey">@_localizer["Code"]</label>
                    <InputText id="shortkey" @bind-Value="@DepartmentModel.ShortKey" class="form-control" required />
                </div>

                <div class="form-group">
                    <label for="description">@_localizer["Description"]</label>
                    <InputText id="description" @bind-Value="@DepartmentModel.Description" class="form-control" required />
                </div>

                <div class="form-group mt-3">
                    <RadzenButton ButtonType="ButtonType.Submit" Text="@_localizer["Save"]" ButtonStyle="ButtonStyle.Primary" />
                    <RadzenButton Text="@_localizer["Cancel"]" ButtonStyle="ButtonStyle.Light" Click="@(_ => Cancel())" />
                </div>
            </EditForm>
        }
    </Authorized>
</AuthorizeView>