using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BlazorApp.Helpers;
using AMprover.BlazorApp.Pages.RiskOrganize.Import;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Enums;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BlazorApp.Pages.RiskOrganize;

public partial class RiskOrganizer
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IRiskOrganizerManager RiskOrganizerManager { get; set; }
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private RiskOrganizerQueryParams QueryParams { get; set; }
    [Inject] private IPageNavigationManager PageNavigationManager { get; set; }
    [Inject] private IScenarioManager ScenarioManager { get; set; }
    [Inject] private IAdHocFmecaImageManager FmecaImageService { get; set; }
    [Inject] private IStringLocalizer<RiskOrganizer> Localizer { get; set; }
    [Inject] private AuthenticationStateProvider AuthenticationStateProvider { get; set; }
    [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; }
    [Inject] private UserManager<UserAccount> UserManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private IGlobalDataService GlobalDataService { get; set; }

    private ILogger Logger { get; set; }
    private string Currency => LookupManager.GetCurrency();
    private Dictionary<ObjectLevel, string> ObjectLevels { get; set; }

    // UI Elements
    private UtilityGrid<RiskObjectModel> RiskObjectGrid { get; set; }
    private RadzenTabs TabsComponent { get; set; }
    public RadzenTabs GridTabsComponent { get; set; }

    // Database Objects
    private List<ScenarioModel> Scenarios { get; set; } = [];
    private List<ScenarioModel> SelectedScenarios { get; set; } = [];
    private List<RiskObjectModel> RiskObjects { get; set; } = [];
    public List<RiskObjectModel> SelectedRiskObjects { get; private set; } = [];
    private List<ObjectModel> Objects { get; set; } = [];

    // Dropdown values
    private List<RiskMatrixTemplateModel> Templates { get; set; } = [];
    private List<string> AnalysisTypes { get; set; } = [];
    private Dictionary<int, string> StatusDict { get; set; } = new();
    private Dictionary<int, string> DepartmentDict { get; set; } = new();

    // State tracking
    private ScenarioModel _selectedScenario;

    private ScenarioModel SelectedScenario
    {
        get => _selectedScenario;
        set
        {
            _selectedScenario = value;
            QueryParams.Scenario = _selectedScenario?.Id;
        }
    }

    private RiskObjectModel _selectedRiskObject;

    private RiskObjectModel SelectedRiskObject
    {
        get => _selectedRiskObject;
        set
        {
            _selectedRiskObject = value;
            QueryParams.RiskObject = _selectedRiskObject?.Id;
        }
    }

    private List<DepartmentModel> UserDepartments { get; set; }
    public UtilityGrid<RiskModel> RisksGrid { get; set; }
    public UtilityGrid<TaskModel> PreventiveActionsGrid { get; set; }
    private List<ObjectModel> Collections { get; set; } = [];

    private Dictionary<string, Dictionary<int, string>> TaskDropDownOverrides { get; set; }
    private Dictionary<string, Dictionary<int, string>> RiskDropDownOverrides { get; set; }
    private Dictionary<string, Dictionary<int, string>> RiskObjectDropdownOverrides { get; set; }
    private string ValRiskTabText { get; set; }
    private string PrevActTabText { get; set; }
    private string PmoActTabText { get; set; }

    public bool ShowGauge { get; set; }
    private bool ShowRegenerateImagesBtn { get; set; }

    private IEnumerable<string> UserRoles { get; set; }
    private bool IsAdminUser { get; set; }
    private bool IsAssetManagementUser => UserRoles?.Contains(RoleConstants.AssetManagement) == true;

    private int SelectedTabIndex { get; set; }
    private int SelectedBottomTabIndex { get; set; }

    public List<int?> Categories
    {
        get => QueryParams.Categories;
        set
        {
            if (value == QueryParams.Categories)
                return;

            QueryParams.Categories = value;
            Task.Run(async () => await UpdateSelectedCollections(value, true));
        }
    }

    #region Chart Data

    public class DataItem
    {
        public string RiskType { get; set; }
        public double Cost { get; set; }
        public string CostString { get; init; }
    }

    private DataItem[] ChartDataActions { get; set; }
    private DataItem[] ChartDataDirectRiskCost { get; set; }
    private DataItem[] ChartDataValueRisk { get; set; }

    #endregion

    #region Initialization

    protected override async Task OnInitializedAsync()
    {
        Logger = LoggerFactory.CreateLogger<RiskOrganizer>();

        QueryParams.AnalysisType =
            !NavigationManager.Uri.CheckQueryStringValue("AnalysisType", "pmo") ? string.Empty : "pmo";
        ShowRegenerateImagesBtn = LookupManager.GetShowRegenerateImages();

        await LoadInitialData();
        await SetupUserAccess();
        SetupDropdowns();

        SelectedScenario = Scenarios.FirstOrDefault(x => x.Id == QueryParams.Scenario);
        await UpdateSelectedScenarios(QueryParams.Scenarios, false);

        // Make sure the correct GridRow shows as selected on opening the page
        await BottomTabChange(0);
    }

    private async Task LoadInitialData()
    {
        Scenarios = ScenarioManager.GetAllScenariosSorted();
        Templates = RiskAnalysisManager.GetAllTemplates();
        Objects = RiskAnalysisManager.GetAllObjects();
        AnalysisTypes = RiskOrganizerManager.GetAnalysisTypes();
        StatusDict = DropdownManager.GetStatusDict();
        DepartmentDict = DropdownManager.GetDepartmentDict();
        ObjectLevels = ObjectManager.GetObjectLevelNames();
        ShowGauge = LookupManager.GetRiskAnalysisShowGauge();

        ValRiskTabText = Localizer["RoValueRiskTabTxt"];
        PrevActTabText = Localizer["RoPrevActionTabTxt"];
        PmoActTabText = Localizer["RoPmoPrevActionTabTxt"];
    }

    private async Task SetupUserAccess()
    {
        UserRoles = (await GetCurrentUserRoles()).ToList();
        IsAdminUser = UserRoles?.Contains(RoleConstants.Administrators) == true ||
                      UserRoles?.Contains(RoleConstants.PortfolioAdministrators) == true;

        if (!IsAdminUser)
        {
            var user = await GetCurrentUserId();
            UserDepartments = RiskAnalysisSetupManager.GetUserDepartments(user).ToList();
        }
    }

    private void SetupDropdowns()
    {
        TaskDropDownOverrides = DropdownManager.GetTaskModelDropDownOverrides();
        RiskDropDownOverrides = DropdownManager.GetRiskModelDropDownOverrides();
        SetRiskDropdownOverride();
    }

    private async Task<IEnumerable<string>> GetCurrentUserRoles()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated == true)
        {
            var userAccount = await UserManager.FindByNameAsync(user.Identity.Name);
            if (userAccount != null)
            {
                return await UserManager.GetRolesAsync(userAccount);
            }
        }

        return [];
    }

    private async Task<string> GetCurrentUserId()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated == true)
        {
            var userAccount = await UserManager.FindByNameAsync(user.Identity.Name);
            return userAccount?.Id;
        }

        return null;
    }

    #endregion

    #region Data Methods

    public List<RiskModel> GetRisks() => SelectedRiskObject?.Risks?.ToList() ?? [];

    public List<TaskModel> GetTasks() =>
        SelectedRiskObject?.Risks?.SelectMany(x => x.Tasks).Where(x => !x.Pmo).ToList() ?? [];

    public List<TaskModel> GetPmoTasks() =>
        SelectedRiskObject?.Risks?.SelectMany(x => x.Tasks).Where(x => x.Pmo).ToList() ?? [];

    private void SetRiskDropdownOverride()
    {
        RiskObjectDropdownOverrides = DropdownManager.GetRiskObjectDropDownOverrides();
        RiskObjectDropdownOverrides.Add(nameof(RiskModel.RiskObject), RiskObjects.ToDictionary(x => x.Id, x => x.Name));
    }

    // Fill Collections ListBox based on SelectedRiskObjects
    private void SetCollectionsFromRiskObjects()
    {
        Collections = RiskObjects.Where(x => x.ParentObject != null)
            .Select(x => new ObjectModel
            {
                Id = x.ParentObject.Id,
                Name = x.ParentObject.Name.Trim()
            })
            .GroupBy(x => x.Id)
            .Select(g => g.First())
            .OrderBy(x => x.Name)
            .ToList();

        Collections.Insert(0, new ObjectModel {Id = 0, Name = "None"});
        QueryParams.Categories = Collections.Select(x => (int?) x.Id).ToList();
    }

    private void SetChartData()
    {
        decimal? prevCostPmo = SelectedRiskObject.PreventiveCostPmo ?? 0;
        decimal? prevCost = SelectedRiskObject.PreventiveCostAfter ?? 0;
        decimal? directCostPmo = SelectedRiskObject.DirectCorrectiveCostPmo ?? 0;
        decimal? directCost = SelectedRiskObject.DirectCorrectiveCostAfter ?? 0;
        decimal? riskPmo = (SelectedRiskObject.CorrectiveCostPmo ?? 0) - (decimal) directCostPmo;
        decimal? riskAfter = (SelectedRiskObject.CorrectiveCostAfter ?? 0) - (decimal) directCost;

        ChartDataActions =
        [
            new DataItem
                {RiskType = "Current", Cost = (double) prevCostPmo, CostString = FormatAsSelectedCurrency(prevCostPmo)},
            new DataItem
                {RiskType = "Analysed", Cost = (double) prevCost, CostString = FormatAsSelectedCurrency(prevCost)}
        ];

        ChartDataDirectRiskCost =
        [
            new DataItem
            {
                RiskType = "Current", Cost = (double) directCostPmo,
                CostString = FormatAsSelectedCurrency(directCostPmo)
            },
            new DataItem
                {RiskType = "Analysed", Cost = (double) directCost, CostString = FormatAsSelectedCurrency(directCost)}
        ];

        ChartDataValueRisk =
        [
            new DataItem
                {RiskType = "Current", Cost = (double) riskPmo, CostString = FormatAsSelectedCurrency(riskPmo)},
            new DataItem
                {RiskType = "Analysed", Cost = (double) riskAfter, CostString = FormatAsSelectedCurrency(riskAfter)}
        ];
    }

    #endregion

    #region Update Methods

    // Change Selected Scenarios
    private async Task UpdateSelectedScenarios(IEnumerable<int> selectedScenariosIds, bool setQueryString)
    {
        SelectedScenarios = Scenarios
            .Where(x => selectedScenariosIds.Contains(x.Id))
            .ToList();

        RiskObjects = RiskOrganizerManager.GetRiskObjectsFromScenariosV2(SelectedScenarios);

        if (!SelectedScenarios.Contains(SelectedScenario))
            SelectedScenario = SelectedScenarios.FirstOrDefault();

        SetCollectionsFromRiskObjects();
        await UpdateSelectedCollections(QueryParams.Categories ?? [], false);

        if (!string.IsNullOrWhiteSpace(QueryParams.AnalysisType))
        {
            var analysisType = AnalysisTypes.FirstOrDefault(x =>
                x.Contains(QueryParams.AnalysisType.ToLower(), StringComparison.InvariantCultureIgnoreCase));

            RiskObjects = RiskObjects.Where(x => x.AnalysisType == analysisType).ToList();
        }

        if (setQueryString)
            SetQueryString();
    }

    // Change Selected Collections
    private async Task UpdateSelectedCollections(List<int?> selectParentObjects, bool setQueryString)
    {
        if (selectParentObjects.FirstOrDefault(x => x == 0) != null)
            selectParentObjects.Add(null);

        SelectedRiskObjects = RiskObjects
            .Where(x => selectParentObjects.Contains(x.ParentObjectId)).ToList();

        SelectedRiskObject = SelectedRiskObjects.FirstOrDefault(x => x.Id == QueryParams.RiskObject);

        if (!SelectedRiskObjects.Contains(SelectedRiskObject))
            SelectedRiskObject = null;

        SelectedTabIndex = SelectedRiskObject == null ? 0 : 1;

        // Make sure the correct GridRow shows as selected on opening the page
        if (SelectedBottomTabIndex == 0)
            await BottomTabChange(0);

        if (setQueryString)
            SetQueryString();
    }

    /// <summary>
    /// Update selected RiskObject
    /// </summary>
    private async Task UpdateCurrentRiskObject(bool updateRisks = false)
    {
        if (SelectedRiskObject == null)
            return;

        SelectedRiskObject = RiskOrganizerManager.UpdateRiskObject(SelectedRiskObject);

        if (!QueryParams.Categories.Contains(SelectedRiskObject.ParentObjectId ?? 0))
            QueryParams.Categories.Add(SelectedRiskObject.ParentObjectId ?? 0);

        if (!QueryParams.Scenarios.Contains(SelectedRiskObject.ScenarioId))
            QueryParams.Scenarios.Add(SelectedRiskObject.ScenarioId);

        SetQueryString();

        if (updateRisks)
        {
            RiskOrganizerManager.UpdateRisksObjectStructure(
                SelectedRiskObject.Risks.ToList(),
                SelectedRiskObject.ParentObjectId,
                SelectedRiskObject.ObjectId);
        }

        await UpdateSelectedScenarios(QueryParams.Scenarios, true);
    }

    /// <summary>
    /// Update Selected Scenario
    /// </summary>
    private void UpdateCurrentScenario()
    {
        if (SelectedScenario != null)
            ScenarioManager.SaveScenario(SelectedScenario);
    }

    /// <summary>
    /// Update The asset linked to the current RiskObject
    /// </summary>
    private void UpdateCurrentAsset()
    {
        var asset = SelectedRiskObject.Object;
        SelectedRiskObject.Object = ObjectManager.SaveObject(asset);
    }

    private async Task UpdateScenarioOnRiskObject()
    {
        RiskOrganizerManager.UpdateScenarioOnLcc(SelectedRiskObject);
        await UpdateCurrentRiskObject();
    }

    private async Task UpdateCollectionOrIntallationOnRiskObject()
    {
        RiskOrganizerManager.UpdateCollectionAndInstallationOnLccs(SelectedRiskObject);
        await UpdateCurrentRiskObject(true);
    }

    private void UpdateRisk(RiskModel risk)
    {
        RiskOrganizerManager.UpdateRisk(risk);

        if (risk.RiskObjectId != SelectedRiskObject.Id)
        {
            var riskFromRiskObject = SelectedRiskObject.Risks.FirstOrDefault(x => x.Id == risk.Id);
            if (riskFromRiskObject != null)
            {
                var risks = SelectedRiskObject.Risks.ToList();
                risks.Remove(riskFromRiskObject);
                SelectedRiskObject.Risks = risks;
            }

            var newRiskObject = RiskObjects.FirstOrDefault(x => x.Id == risk.RiskObjectId);
            if (newRiskObject?.Risks != null)
            {
                newRiskObject.Risks = newRiskObject.Risks.ToList().Append(risk);
            }
        }
    }

    #endregion

    #region Navigation Methods

    /// <summary>
    /// Cycle through Selected Scenarios
    /// </summary>
    public void PreviousScenario()
    {
        var index = SelectedScenarios.IndexOf(SelectedScenario);
        SelectedScenario = index == 0 ? SelectedScenarios.Last() : SelectedScenarios.Skip(index - 1).First();
        SetQueryString();
    }

    /// <summary>
    /// Cycle through Selected Scenarios
    /// </summary>
    public void NextScenario()
    {
        var index = SelectedScenarios.IndexOf(SelectedScenario);
        SelectedScenario = index == SelectedScenarios.Count - 1
            ? SelectedScenarios.First()
            : SelectedScenarios.Skip(index + 1).First();
        SetQueryString();
    }

    // Callback on Manually changing the Top Tabs Component
    private void TopTabChange(int index) => SetQueryString();

    /// <summary>
    /// Changing bottom TabsComponent
    /// </summary>
    private async Task BottomTabChange(int index)
    {
        if (index == 0)
        {
            // When you switch tabs the selected row of the GridComponent is lost.
            // Wait a brief moment to allow tab change to complete
            await Task.Delay(10);

            if (SelectedRiskObject != null)
            {
                RiskObjectGrid.SelectRow(SelectedRiskObject);
            }
        }

        SetQueryString();
    }

    // Set Query string based on Selected Scenarios, RiskObject and Collections
    private void SetQueryString() =>
        SetQueryStringInternal(QueryParams.ToQueryString());

    private void SetQueryStringInternal(string queryString, bool forceReload = false)
    {
        var url = string.IsNullOrWhiteSpace(queryString)
            ? NavigationManager.Uri.Split('?')[0]
            : $"{NavigationManager.Uri.Split('?')[0]}{queryString}";

        if (NavigationManager.Uri == url) return;
        var uri = new Uri(url);

        PageNavigationManager.SavePageQueryString(uri.LocalPath, uri.RemoveQueryStringByKey("AnalysisType").Query);
        NavigationManager.NavigateTo(url, forceReload);
    }

    #endregion

    #region UI Interaction Methods

    /// <summary>
    /// Select a risk object, jump to risk object tab and show RiskObject Details
    /// </summary>
    private void ClickRiskObjectRow(RiskObjectModel riskObject)
    {
        if (riskObject == null)
            return;

        SelectedRiskObject = RiskOrganizerManager.GetRiskObjectWithRisks(riskObject.Id);
        SelectedTabIndex = 1;

        TabsComponent?.Reload();

        SetQueryString();

        if (GetPmoStatus()) SetChartData();
    }

    private void DeleteRiskObject(RiskObjectModel riskObject)
    {
        DialogService.Open<DeleteRiskObjectWidget>(
            Localizer["RoDeleteStatementTxt"],
            new Dictionary<string, object>
            {
                {"RiskObject", riskObject ?? SelectedRiskObject},
                {"Risks", riskObject?.Risks.Count() ?? SelectedRiskObject.Risks.Count()}
            }
        );
    }

    private async Task PasteRiskObject(RiskObjectModel model)
    {
        RiskOrganizerManager.CopyRiskObject(model);
        await UpdateSelectedScenarios(QueryParams.Scenarios, true);
    }

    private void OpenNewRiskObjectWidget()
    {
        DialogService.Open<NewRiskObjectWidget>(
            Localizer["RoNewBtnTxt"],
            new Dictionary<string, object>
            {
                {nameof(NewRiskObjectWidget.SelectedScenario), SelectedScenario?.Id},
                {
                    nameof(NewRiskObjectWidget.SaveCallBack),
                    EventCallback.Factory.Create<RiskObjectModel>(this, CreateNewRiskObjectCallback)
                }
            });
    }

    private void CreateNewRiskObjectCallback(RiskObjectModel ro)
    {
        if (!QueryParams.Scenarios.Contains(ro.ScenarioId))
            QueryParams.Scenarios.Add(ro.ScenarioId);

        if (ro.ParentObjectId != null && !QueryParams.Categories.Contains(ro.ParentObjectId))
            QueryParams.Categories.Add(ro.ScenarioId);

        RiskObjects.Add(ro);
        var scenarios = QueryParams.Scenarios.Append(ro.ScenarioId).Distinct().ToList();
        var categories = RiskObjects.Where(x => x.ParentObjectId != null)
            .Select(x => x.ParentObjectId)
            .Distinct()
            .ToList();

        if (ro.ParentObjectId == null && !categories.Contains(0))
            categories.Add(0);

        NavigationManager.NavigateTo(
            $"/value-risk-organizer?scenario={ro.ScenarioId}&riskobject={ro.Id}&scenarios={string.Join(',', scenarios)}&categories={string.Join(',', categories)}"
            , true);
    }

    #endregion

    #region Navigation and Page Actions

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    private void OpenRisksPage(RiskObjectModel model)
    {
        if (!ShouldShowOpenPage(model))
            return;

        var previousUrl = PageNavigationManager.GetRiskEditUrl(model.Id);
        if (previousUrl != null)
        {
            NavigationManager.NavigateTo(previousUrl);
        }
        else
        {
            OpenRisksPage(model.Id, RiskOrganizerManager.GetFirstRiskId(model));
        }
    }

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    private void OpenRisksPage(RiskModel model) => OpenRisksPage(model.RiskObjectId, model.Id);

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    private void OpenRisksPage(TaskModel model) => OpenRisksPage(model.Risk?.RiskObjectId ?? 0, model.MrbId ?? 0);

    /// <summary>
    /// Open RiskEdit Page
    /// </summary>
    private void OpenRisksPage(int riskObjectId, int riskId) =>
        NavigationManager.NavigateTo($"/value-risk-analysis/{riskObjectId}/risks/{riskId}", false);

    /// <summary>
    /// Open Lcc Page
    /// </summary>
    private void OpenLccPage(RiskObjectModel riskObject) =>
        NavigationManager.NavigateTo($"/lcc/{riskObject.Lcc?.Id}");

    private void ExportRiskObject() =>
        NavigationManager.NavigateTo("/value-risk-exports");

    #endregion

    #region UI Helper Methods

    /// <summary>
    /// Get Text for the Risks Tab label
    /// </summary>
    private string GetRiskTabText() => $"{ValRiskTabText} ({GetRisks().Count})";

    /// <summary>
    /// Get Text for the Tasks/Preventive actions Tab label
    /// </summary>
    private string GetPreventiveActionsTabText() => $"{PrevActTabText} ({GetTasks().Count})";

    private string GetPmoActionsTabText() => $"{PmoActTabText} ({GetPmoTasks().Count})";

    private bool GetPmoStatus() => SelectedRiskObject?.IsPmoType() ?? false;

    private bool ShouldShowOpenPage(RiskObjectModel rowData) =>
        rowData?.Department == null || IsAdminUser || UserDepartments.Any(x => x.Id == rowData.Department.Id);

    public bool ShouldShowOpenPage(RiskModel rowData) =>
        rowData?.RiskObject?.Department == null || IsAdminUser ||
        UserDepartments.Any(x => x.Id == rowData.RiskObject.Department.Id);

    public bool ShouldShowOpenPage(TaskModel rowData) =>
        rowData?.Risk?.RiskObject?.Department == null || IsAdminUser ||
        UserDepartments.Any(x => x.Id == rowData.Risk.RiskObject.Department.Id);

    private string FormatAsSelectedCurrency(decimal? value) =>
        value == null ? string.Empty : value.Value.ToString("C0", CultureInfo.CreateSpecificCulture(Currency));

    private static string FormatAsEur(object value) =>
        ((double) value).ToString("C0", CultureInfo.CreateSpecificCulture("nl-NL"));

    private string GetObjectLevel(ObjectLevel level) =>
        ObjectLevels.TryGetValue(level, out var name) ? name : level.ToString();

    #endregion

    #region Image Generation

    private void GenerateFmecaImagePopup()
    {
        if (SelectedRiskObject == null) return;

        DialogService.Open<AreYouSureDialog<RiskObjectModel>>(Localizer["RoRegenerateImagesLbl"],
            new Dictionary<string, object>
            {
                {
                    nameof(AreYouSureDialog<RiskObjectModel>.Text),
                    Localizer["RoRegenerateImagesTxt"].Value.Replace("[RISKOBJECT]", SelectedRiskObject.Name)
                },
                {nameof(AreYouSureDialog<RiskObjectModel>.Item), SelectedRiskObject},
                {
                    nameof(AreYouSureDialog<RiskObjectModel>.YesCallback),
                    EventCallback.Factory.Create<RiskObjectModel>(this, GenerateRiskObjectImages)
                }
            });
    }

    private void GenerateRiskObjectImages(RiskObjectModel riskObject) =>
        FmecaImageService.GenerateFmecaImages(riskObject.Id);

    #endregion

    #region Dialog Methods

    private void OnRiskObjectImportClick(RadzenSplitButtonItem item)
    {
        if (item == null) return;

        switch (item.Value)
        {
            case "1":
                DialogService.Open<ImportRisksWidget>(
                    Localizer["RoImportRisksBtn"],
                    new Dictionary<string, object>(),
                    new DialogOptions {Draggable = true});
                break;
            case "2":
                DialogService.Open<ImportTaskWidget>(
                    Localizer["RoImportTasksBtn"],
                    new Dictionary<string, object>(),
                    new DialogOptions {Draggable = true});
                break;
            case "3":
                DialogService.Open<ImportSpareWidget>(
                    Localizer["RoImportSpareBtn"],
                    new Dictionary<string, object>(),
                    new DialogOptions {Draggable = true});
                break;
        }
    }

    private void MultiLineInfoClick(RadzenSplitButtonItem item)
    {
        if (item == null) return;

        var dialogOptions = new DialogOptions {Width = "800px", Resizable = false, Draggable = true};

        switch (item.Value)
        {
            case "1":
                DialogService.Open<InformationDialog>(
                    Localizer["RoMenuTitle"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoMenuTxt"].ToString()}},
                    dialogOptions);
                break;
            case "2":
                DialogService.Open<InformationDialog>(
                    Localizer["RoExportBtnTxt"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoExportInfoTxt"].ToString()}},
                    dialogOptions);
                break;
            case "3":
                DialogService.Open<InformationDialog>(
                    Localizer["RoImportRiskBtn"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoImportRisksInfoTxt"].ToString()}},
                    dialogOptions);
                break;
            case "4":
                DialogService.Open<InformationDialog>(
                    Localizer["RoImportTaskBtn"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoImportTasksTxt"].ToString()}},
                    dialogOptions);
                break;
            case "5":
                DialogService.Open<InformationDialog>(
                    Localizer["RoImportSpareBtn"],
                    new Dictionary<string, object> {{"DialogContent", Localizer["RoImportSpareTxt"].ToString()}},
                    dialogOptions);
                break;
        }
    }

    #endregion
}