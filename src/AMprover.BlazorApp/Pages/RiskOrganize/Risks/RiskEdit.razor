﻿@page "/value-risk-analysis/{RiskObjectId:int}/risks/{RiskId:int}"

<div class="row">
    <div class="col-6">
        <h2>@PageTitle</h2>
    </div>
</div>

@if (RiskIsReadOnly())
{
    <div class="readonly-warning">@(GetRiskIsReadOnlyText())</div>
}

<div class="row header-navigation subheader-height">
    <div class="col-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
                <NavLink class="breadcrumb-item" href=@GetRiskOrganizerUrl() Match="NavLinkMatch.All">
                    Value Risk Organizer
                </NavLink>
                <NavLink class="breadcrumb-item" aria-current="page" href="@($"/value-risk-analysis/{RiskObjectId}/risks/{RiskId}")" Match="NavLinkMatch.All">
                    Value Risk Assessment
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-4 text-center">
        <Paginator Initial=GetInitialPaginatorValue()
                   Count=GetRiskCount()
                   CallBack=PaginatorCallback
                   @ref=Paginator />
    </div>
    <div class="col-4 text-right">
        <Information class="float-right btn-centered my-2 ml-1 mr-2" DialogTitle=@Localizer["RaMenuTitle"] DialogContent=@Localizer["RaMenuTxt"]/>
        @*@if (!IsRiskAnalysisView)
        {
            <RadzenFileInput class="my-2 mx-1" @bind-Value=@FileUpload Accept=".xls, .xlsx" TValue="string" ChooseText=@Localizer["RaBtnImpRisksorTasks"]
                             Change=@(args => ImportCurrentTaskPlan(args))/>
        }*@

        @* Text = @Localizer["RaScreenshotBtnTxt"] *@
        <RadzenButton Icon="camera_enhance" Click=DownloadImage Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary />
        <RadzenButton Icon="add_circle_outline" class="my-2 mx-0" Text=@Localizer["RaNewBtnTxt"] Click=@CreateNewRisk Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary Disabled=@(RiskIsReadOnly() || !GlobalDataService.CanEdit) />
        <RadzenButton Icon="delete" class="my-2 mx-1" Text=@Localizer["RaDeleteBtnTxt"] Click=DeleteSelectedRisk Size=ButtonSize.Medium ButtonStyle=ButtonStyle.Secondary Disabled=@(RiskIsReadOnly() || !GlobalDataService.CanEdit) />
    </div>
</div>

    <div class="row">
        <div class="col-sm-3 neg-margin">
            <h5>@GetPageTitle()</h5>

            <AMDropdown @bind-Value="@StatusFilter"
                        AllowClear=true
                        Data=@ItemStatusDict
                        Label=@Localizer["RaAssesmentStatusFilterLbl"]
                        ContainerClass=""
                        Change=@UpdateStatusFilter />

            <TreeComponent TItem=RiskTreeObject
                           Treeview=RiskTree
                           NodeClickCallback=ClickTreeNode
                           DeleteCallback=@(args => DeleteRiskFromTree(args))
                           PasteCallback=PasteInRiskTree
                           NewCallback=NewRiskInTree
                           ChangeOrderCallback=@(args => ChangeTreeNodeOrder(args))
                           CascadeDeleteParentsWithNoOtherChildren=true />
        </div>

        <div class="col-sm-7">
    
        @if (Risk != null)
        {
            bool disabled = RiskIsReadOnly();

        <RadzenTabs @bind-SelectedIndex="@QueryParams.TopTabs" Change=@OnChangeTopTabsComponent>
            <Tabs>

                <!-- Risk Tab -->
                <RadzenTabsItem Text=@Localizer["RaRiskTabTxt"]>
                    <div class="row riskassessment-top-tabs">
                        <div class="col-sm-12">
                            <div class="row">
                                <div class="col-sm-4">
                                    <AMDropdown AllowFiltering=true
                                                Data=@FailureModeDict
                                                @bind-value=@Risk.FailureModeId
                                                Change=@UpdateCurrentRisk
                                                Label=@Localizer["RaFailMechLbl"]
                                                Disabled=disabled />

                                    <div class="neg-margin">
                                        <AMDropdown AllowClear="true" AllowFiltering="true"
                                                    Data="@Objects.Where(x => x.Level == (int) ObjectLevel.System).OrderBy(x => x.Name).ToDictionary(x => x, x => x.Name, new ObjectModelComparer())"
                                                    @bind-value=@Risk.System
                                                    Change=@(args => ChangeObjectStructure(ObjectLevel.System, (ObjectModel) args))
                                                    Label=@GetObjectLevel(ObjectLevel.System) Disabled=disabled />
                                    </div>
                                    <div class="neg-margin">
                                        <AMDropdown AllowClear="true" AllowFiltering="true"
                                                    Data="@Objects.Where(x => x.Level == (int) ObjectLevel.Component).OrderBy(x => x.Name).ToDictionary(x => x, x => x.Name, new ObjectModelComparer())"
                                                    Value=@Risk.Component
                                                    Change=@(args => ChangeObjectStructure(ObjectLevel.Component, (ObjectModel) args))
                                                    Label=@GetObjectLevel(ObjectLevel.Component) Disabled=disabled />
                                    </div>
                                    <div class="neg-margin">
                                        <AMDropdown AllowClear="true" AllowFiltering="true"
                                                    Data="@Objects.Where(x => x.Level == (int) ObjectLevel.Assembly).OrderBy(x => x.Name).ToDictionary(x => x, x => x.Name, new ObjectModelComparer())"
                                                    Value=@Risk.Assembly
                                                    Change=@(args => ChangeObjectStructure(ObjectLevel.Assembly, (ObjectModel) args))
                                                    Label=@GetObjectLevel(ObjectLevel.Assembly) Disabled=disabled />
                                    </div>
                                </div>
                                <div class="col-sm-8">
                                    <AMproverTextBox @bind-Value=@Risk.Name MaxLength="100" Change=@(_ => UpdateRiskNameDirectly()) Label=@Localizer["RaFailModeLbl"] Disabled=disabled/>
                                    
                                    @if (IsSapaView)
                                    {
                                        <div class="neg-margin-small row">
                                            <div class="col-sm-6">
                                                <AMproverTextArea @bind-Value=@Risk.FailureCause
                                                        Change=@UpdateCurrentRisk Cols="30" Rows="2" Label=@Localizer["RaRulLbl"] Disabled=disabled />
                                            </div>
                                            <div class="col-sm-6">
                                                <AMproverTextArea @bind-Value=@Risk.Description
                                                        Change=@UpdateCurrentRisk Cols="30" Rows="2" Label=@Localizer["RaCbiLbl"] Disabled=disabled />
                                            </div>
                                        </div>
                                        <div class="row neg-margin-small">
                                            <div class="col-sm-12">
                                                <AMproverTextArea @bind-Value=@Risk.FailureConsequences
                                                        Change=@UpdateCurrentRisk Cols="30" Rows="3" Label=@Localizer["RaCriticalityTxt"] Disabled=disabled />
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="neg-margin-small row">
                                            <div class="col-sm-6">
                                                <AMproverTextArea @bind-Value=@Risk.FailureCause
                                                        Change=@UpdateCurrentRisk Cols="30" Rows="2" Label=@Localizer["RaCauseLbl"] Disabled=disabled />
                                                <div class="neg-margin-small row">
                                                    <div class="col-sm-12">
                                                        <AMproverTextArea @bind-Value=@Risk.FailureConsequences
                                                        Change=@UpdateCurrentRisk Cols="30" Rows="3" Label=@Localizer["RaConsequenceLbl"] Disabled=disabled />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <AMproverTextArea @bind-Value=@Risk.Description
                                                        Change=@UpdateCurrentRisk Cols="30" Rows="2" Label=@Localizer["RaEffectLbl"]/>
                                                <div class="neg-margin-small row">
                                                    <div class="col-sm-6">
                                                        <AMDropdown AllowFiltering="true" AllowClear=true 
                                                            Data="@FailureCategories1.ToDictionary(x => x.Name, x => x.Name)"
                                                            @bind-Value="@Risk.FailureCategory1"
                                                            Change=@UpdateCurrentRisk
                                                            Label=@Localizer["RaFailCat1Lbl"]
                                                            Disabled=disabled />
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <AMDropdown AllowFiltering="true" AllowClear=true
                                                            Data="@FailureCategories2.ToDictionary(x => x.Name, x => x.Name)"
                                                            @bind-Value="@Risk.FailureCategory2"
                                                            Change=@UpdateCurrentRisk
                                                            Label=@Localizer["RaFailCat2Lbl"]
                                                            Disabled=disabled />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                                <div class="col-sm-12 neg-margin-small">
                                    <AMproverTextArea @bind-Value=@Risk.Remarks Change=@UpdateCurrentRisk Cols="60" Rows="3" Label=@Localizer["RaRemarksLbl"] Disabled=disabled />
                                </div>
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>

                <!-- Optimize Tab -->
                <RadzenTabsItem Text=@Localizer["RaOptimizeTabTxt"]>
                    <div class="row riskassessment-top-tabs">
                        <div class="col-sm-6">
                            <img src="@Risk.OptimalImage" width="100%" alt="Optimize costs"/>
                        </div>
                        <div class="col-sm-6">
                            <AMproverNumberInput ReadOnly="true" Format="c0" Label=@Localizer["RaRiskOptimumPreventiveActionsAYearTxt"] Value=@Risk.OptimalCostsCalculated Disabled=disabled />
                        </div>
                    </div>
                </RadzenTabsItem>

                <!-- Document Tab -->
                <RadzenTabsItem Text=@Localizer["RaAttachmentTabTxt"]>
                    <div class="row riskassessment-top-tabs">
                        <div class="col-sm-8" style="max-height:400px;">
                            <AttachmentContainer ReadOnly="disabled" Data=@Risk.Attachments SaveCallback=@UpdateCurrentRisk ParentItem=@Risk />
                        </div>
                            <div class="col-sm-4 margin-top-25">
                                <AMproverTextArea @bind-Value=@Risk.Remarks1 Change=@UpdateCurrentRisk Cols="60" Rows="10" Label=@Localizer["RaDataLbl"] Disabled=disabled />
                        </div>
                    </div>
                </RadzenTabsItem>

                <!-- Misc Tab -->
                <RadzenTabsItem Text=@Localizer["RaMiscTabTxt"]>
                    <div class="row riskassessment-top-tabs">
                         <div class="col-sm-12">
                            <div class="row">
                                <div class="col-sm-6">
                                    <AMproverTextArea @bind-Value=@Risk.Function Change=@UpdateCurrentRisk Cols="30" Rows="4" Label=@Localizer["RaFunctionLbl"] Disabled=disabled />
                                </div>
                                <div class="col-sm-6">
                                    <AMproverTextArea @bind-Value=@Risk.Responsible Change=@UpdateCurrentRisk Cols="30" Rows="4" Label=@Localizer["RaResponsibleLbl"] Disabled=disabled />
                                </div>
                            </div>
                        
                            <div class="row">
                                <div class="col-sm-3">
                                    <AMproverNumberInput TValue=int? Label=@Localizer["RaRiskImportIdTxt"] @bind-Value=@Risk.ImportId Change=UpdateCurrentRisk Disabled=disabled />
                                </div>
                                <div class="col-sm-3">
                                    <AMDropdown @bind-Value="@Risk.ItemStatus"
                                                AllowClear=true
                                                Data=@ItemStatusDict
                                                Label=@Localizer["RaAssesmentStatusLbl"]
                                                Change=@(() => UpdateCurrentRisk())
                                                Readonly=IsSapaView />
                                </div>
                                <div class="col-sm-3">
                                    <AMproverNumberInput @bind-Value=@Risk.SortOrder
                                                         TValue=int
                                                         Label=@Localizer["RaRiskSortOrder"]
                                                             Change=@UpdateSortOrderProperty />
                                </div>
                                <div class="col-sm-3">
                                        @if (IsCsirView)
                                        {
                                            <AMproverTextBox TValue=int? Label=@Localizer["RaCriticalityTxt"] @bind-Value=@Risk.State Change=UpdateCurrentRisk Disabled=disabled />
                                        }
                                        else
                                        {
                                           <AMDropdown AllowClear=true
                                                    Data=@CriticalityTypes.ToDictionary(x => x, x => x)
                                                    @bind-Value=@Risk.State
                                                    Change=@(() => UpdateCurrentRisk())
                                                    Label=@(Localizer["RaCriticalityTxt"]) 
                                                    Disabled=disabled />
                                        }

                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label>@Localizer["RaInitiatedByLbl"]:</label>
                                        <label>@Risk.InitiatedBy</label>
                                    </div>
                                </div>
                                @if (Risk.DateInitiated != null)
                                {
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label>@Localizer["RaOnLbl"]:</label>
                                            <label>@Risk.DateInitiated.Value.ToString("dd-MM-yyyy hh:mm")</label>
                                        </div>
                                    </div>
                                }
                                <div class="col-sm-3">
                                    <AMDropdown @bind-Value="@Risk.AdditionalDataId"
                                                AllowClear=true
                                                Data=@AdditionalDataDict
                                                Label=@Localizer["RaAdditionalDataLbl"]
                                                Change=@(() => UpdateCurrentRisk()) />
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-3">
                                    <div class="form-group">
                                        <label>@Localizer["RaModifiedByLbl"]:</label>
                                        <label>@Risk.ModifiedBy</label>
                                    </div>
                                </div>
                                @if (Risk.DateModified != null)
                                {
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label>@Localizer["RaOnLbl"]:</label>
                                            <label>@Risk.DateModified.Value.ToString("dd-MM-yyyy hh:mm")</label>
                                        </div>
                                    </div>
                                }

                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>

                <!-- System Tab -->
                <RadzenTabsItem Text=@GetObjectLevel(ObjectLevel.System) Disabled=@(Risk.System == null)>
                    <ObjectDetails Model=@Risk.System/>
                </RadzenTabsItem>

                <!-- Component Tab -->
                <RadzenTabsItem Text=@GetObjectLevel(ObjectLevel.Component) Disabled=@(Risk.Component == null)>
                    <ObjectDetails Model=@Risk.Component/>
                </RadzenTabsItem>

                <!-- Assembly Tab -->
                <RadzenTabsItem Text=@GetObjectLevel(ObjectLevel.Assembly) Disabled=@(Risk.Assembly == null)>
                    <ObjectDetails Model=@Risk.Assembly/>
                </RadzenTabsItem>

            </Tabs>
        </RadzenTabs>
        }
    </div>

        <div class="col-sm-2">
        @if (Risk != null)
        {
            @if (IsRiskAnalysisView)
            {
                <RadzenTabs @ref=@TotalTabs>
                    <Tabs>
                        <RadzenTabsItem Text=@Localizer["RaValuePerYearTxt"]>
                            <div class="overview-frame neg-margin-large">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div>
                                            <text class="bold">@Localizer["RaWoActionsTxt"]:</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaVrTxt"]:</text><text class="currency-red">@FormatAsSelectedCurrency(Risk.RiskBefore)</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaActTxt"]:</text><text class="currency-green">-</text>
                                        </div>
                                        <div>
                                            <text class="bold"> @Localizer["RaTotTxt"]:</text><text class="currency-bold">@FormatAsSelectedCurrency(Risk.RiskBefore)</text>
                                        </div>

                                        <div>
                                            <text class="bold">@RaWithActionsTxt:</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaVrTxt"]:</text><text class="currency-red">@FormatAsSelectedCurrency(Risk.RiskAfter)</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaActTxt"]:</text><text class="currency-green">@FormatAsSelectedCurrency(Risk.PreventiveCosts)</text>
                                        </div>
                                        <div>
                                            <text class="bold"> @Localizer["RaTotTxt"]:</text><text class="currency-bold">@FormatAsSelectedCurrency((Risk.PreventiveCosts ?? 0) + (Risk.RiskAfter ?? 0))</text>
                                        </div>
                                    </div>
                                </div>
                                <div class="row neg-margin-large">
                                    <div class="col-sm-12">
                                        <RadzenRadialGauge class=@(ShowGauge ? "w-100 riskassessment-performance-gauge" : "hide-gauge w-100 riskassessment-performance-gauge")>
                                            <RadzenRadialGaugeScale Min="0" Max="100" TickPosition="GaugeTickPosition.None">
                                                <RadzenRadialGaugeScalePointer Value=@Risk.OptimalPercentage Length="0.8" ShowValue="true">
                                                    <Template Context="_">
                                                        <p class="row gauge-text">
                                                            @if (Risk.IsSubOptimalLeft)
                                                            {
                                                                @Localizer["RaSubOptimalLeftTxt"]
                                                            }
                                                            else if (Risk.IsSubOptimalRight)
                                                            {
                                                                @Localizer["RaSubOptimalRightTxt"]
                                                            }
                                                            else
                                                            {
                                                                @Localizer["RaOptimalTxt"]
                                                            }
                                                        </p>
                                                    </Template>
                                                </RadzenRadialGaugeScalePointer>
                                                <RadzenRadialGaugeScaleRange From="0" To="24.999999" Fill="red"/>
                                                <RadzenRadialGaugeScaleRange From="75" To="100" Fill="yellow"/>
                                                <RadzenRadialGaugeScaleRange From="25" To="74.999999" Fill="green"/>
                                            </RadzenRadialGaugeScale>
                                        </RadzenRadialGauge>
                                        <RadzenSwitch class="show-gauge-ra" @bind-Value=@ShowGauge Change=@(args => ToggleGauge(args))/>
                                    </div>
                                </div>
                            </div>
                        </RadzenTabsItem>
                    </Tabs>
                </RadzenTabs>
            }
            else if (IsSapaView)
            {
                <RadzenTabs @ref=@TotalTabs>
                    <Tabs>
                        <RadzenTabsItem Text=@Localizer["RaValuePerYearTxt"]>
                            <div class="overview-frame neg-margin-large">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div>
                                            <text class="bold">@Localizer["RaWoActionsTxt"]:</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaSapaTotTxt"]:</text><text class="currency-bold">@FormatAsSelectedCurrency(Risk.RiskBefore)</text>
                                        </div>
                                        <br>
                                        <div>
                                            <text class="bold">@RaWithActionsTxt:</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaActTxt"]:</text><text class="currency-bold">@FormatAsSelectedCurrency(Risk.PreventiveCosts)</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaSapaTotTxt"]:</text><text class="currency-bold">@FormatAsSelectedCurrency(Risk.RiskAfter ?? 0)</text>
                                        </div>
                                        @if (Risk.PreventiveCosts != 0 && Risk.PreventiveCosts != null)
                                        {
                                            <br>
                                            <div>
                                                <text class="large-bold"> @Localizer["RaSapaIndexTxt"]:</text><text class="large-bold-right">@FormatAsNumber((Risk.SapaIndex))</text>
                                            </div>
                                        }
                                    </div>
                                </div>
                                
                            </div>
                        </RadzenTabsItem>
                    </Tabs>
                </RadzenTabs>
            }
            else
            {
                <RadzenTabs @ref=@TotalTabs>
                    <Tabs>
                        <RadzenTabsItem Text=@Localizer["RaValuePerYearTxt"]>
                            <div class="overview-frame neg-margin-large">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div>
                                            <text class="bold">@Localizer["RaWithActionsPMOTabTxt"]:</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaVrTxt"]:</text><text class="currency-red">@FormatAsSelectedCurrency(Risk.RiskPmo)</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaActTxt"]:</text><text class="currency-green">@FormatAsSelectedCurrency(Risk.PreventiveCostsPmo)</text>
                                        </div>
                                        <div>
                                            <text class="bold"> @Localizer["RaTotTxt"]:</text><text class="currency-bold">@FormatAsSelectedCurrency((Risk.RiskPmo ?? 0) + (Risk.PreventiveCostsPmo ?? 0))</text>
                                        </div>

                                        <div>
                                            <text class="bold">@RaWithActionsTxt:</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaVrTxt"]:</text><text class="currency-red">@FormatAsSelectedCurrency(Risk.RiskAfter)</text>
                                        </div>
                                        <div>
                                            <text> @Localizer["RaActTxt"]:</text><text class="currency-green">@FormatAsSelectedCurrency(Risk.PreventiveCosts)</text>
                                        </div>
                                        <div>
                                            <text class="bold"> @Localizer["RaTotTxt"]:</text><text class="currency-bold">@FormatAsSelectedCurrency((Risk.RiskAfter ?? 0) + (Risk.PreventiveCosts ?? 0))</text>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row neg-margin-small">
                                             
                                <div class="container">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <RadzenChart class=@(ShowGauge ? "riskassessment-performance-gauge" : "hide-gauge riskassessment-performance-gauge" )>
                                                <RadzenStackedColumnSeries Data="@ChartDataActions" Fill="green" CategoryProperty="RiskType" ValueProperty="Cost">
                                                    <TooltipTemplate Context="data">
                                                        <div>
                                                            <span>@Localizer["RaActionCosts"]</span> = <strong>@data.CostString</strong>
                                                        </div>
                                                    </TooltipTemplate>
                                                </RadzenStackedColumnSeries>
                                                <RadzenStackedColumnSeries Data="@ChartDataDirectRiskCost" Fill="firebrick" CategoryProperty="RiskType" ValueProperty="Cost">
                                                    <TooltipTemplate Context="data">
                                                        <div>
                                                            <span>@Localizer["RaDirectRiskCosts"]</span> = <strong>@data.CostString</strong>
                                                        </div>
                                                    </TooltipTemplate>
                                                </RadzenStackedColumnSeries><RadzenStackedColumnSeries Data="@ChartDataValueRisk" Fill="red" CategoryProperty="RiskType" ValueProperty="Cost">
                                                    <TooltipTemplate Context="data">
                                                        <div>
                                                            <span>@Localizer["RaValueRiskCosts"]</span> = <strong>@data.CostString</strong>
                                                        </div>
                                                    </TooltipTemplate>
                                                </RadzenStackedColumnSeries>
                                                <RadzenColumnOptions Radius="5" Width="30" />
                                                <RadzenValueAxis Visible="false" />
                                                <RadzenLegend Visible="false" />
                                            </RadzenChart>
                                            <RadzenSwitch class="show-gauge neg-margin-large" @bind-Value=@ShowGauge Change=@(args => ToggleGauge(args)) />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </RadzenTabsItem>
                        @if (IsCsirView)
                        {
                            <RadzenTabsItem Text="CSIR">
                                <div class="overview-frame neg-margin-large">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div>
                                                <text class="bold">@Localizer["RaWoActionsTxt"]:</text>
                                            </div>
                                            <div>
                                                <text> @Localizer["RaActTxt"]:</text><text class="currency-green">@FormatAsSelectedCurrency(Risk.PreventiveCostsPmo)</text>
                                            </div>
                                            <div>
                                                <text class="bold"> @Localizer["RaCSIRrTxt"]:</text><text class="currency-bold">@FormatAsPoints(Risk.PointsPmo)</text>
                                            </div>

                                            <div>
                                                <text class="bold">@RaWithActionsTxt:</text>
                                            </div>
                                            <div>
                                                <text> @Localizer["RaActTxt"]:</text><text class="currency-green">@FormatAsSelectedCurrency(Risk.PreventiveCosts)</text>
                                            </div>
                                            <div>
                                                <text class="bold"> @Localizer["RaCSIRrTxt"]:</text><text class="currency-bold">@FormatAsPoints(Risk.PointsEffectAfter)</text>
                                            </div>
                                            <br>
                                            <div>
                                                <text class="large-bold"> @Localizer["RaCSIRcsTxt"]</text>
                                            </div>
                                            <div>
                                                <text class="large-bold"> @Localizer["RaCSIRwTxt"]:</text><text class="large-bold-right">@FormatAsResistanceLevel(Risk.PointsEffectAfter)</text>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </RadzenTabsItem>
                        }
                    </Tabs>
                </RadzenTabs>
            }
        }
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12 neg-margin-large">
        @if (Risk != null)
        {
            var readOnly = RiskIsReadOnly();

            <RadzenTabs @ref=@RiskTabs Change=OnChangeBottomTabsComponent @bind-SelectedIndex=@QueryParams.BottomTabs>
                <Tabs>
                    @if (IsRiskAnalysisView || IsSapaView)
                    {
                        <!-- Tab Risk assessment before -->
                        <RadzenTabsItem Text=@TabBeforeTitle>
                            <RiskMatrixContainer 
                                Risk=@Risk 
                                Language=@Language
                                Currency=@Currency
                                MatrixType="MatrixTypes.Before" 
                                OnChangeCallback=@EditRiskMatrixCallBack
                                MtbfLabel=@Localizer["RaMtbfLbl"] 
                                YearLabel=@Localizer["RaYearLbl"] 
                                ShowPointsLabel=@Localizer["RaShowPointsLbl"]
                                IsRiskAnalysis=@IsRiskAnalysisView 
                                IsSapaView=@IsSapaView 
                                IsCsirView=@IsCsirView 
                                TypesSetToColumn=@TypesSetToColumn 
                                ShowValuesLabel=@Localizer["RaShowValuesLbl"] 
                                ShowCustomValuesLabel=@Localizer["RaShowCustValuesLbl"] 
                                IsReadOnly=@RiskIsReadOnly() />
                        </RadzenTabsItem>
                    }

                    @if (!IsRiskAnalysisView && !IsSapaView)
                    {
                        <!-- Tab Risk assessment spares PMO -->
                        <RadzenTabsItem Text=@($"{TabSparesPmoTitle} ({Risk.Spares.Count(x => x.Pmo)})")>
                            <UtilityGrid TItem=SpareModel
                            @ref=SparesGrid
                            Data=@Risk?.Spares.Where(x => x.Pmo).ToList()
                            FileName=@GridNames.RiskEdit.Spares
                            Interactive=!readOnly
                            AllowXlsExport=true
                            AllowFiltering=true
                            DeleteCallback=@DeleteSpare
                            SaveCallback=@UpdateSpare
                            AddRowCallBack=@(_ => OpenSparePopup(0, true))
                            OpenPopup=@(args => OpenSparePopup(args.Id, true))
                            PasteCallBack=@(args => PasteSpareCallback(args.Item1, args.Item2))
                            CopyToPmoRowCallBack=@DeriveSpare/>
                        </RadzenTabsItem>

                        <!-- Tab Risk assessment actions -->
                        <RadzenTabsItem Text=@($"{TabTasksPmoTitle} ({Risk.Tasks.Count(x => x.Pmo)})")>
                            <UtilityGrid TItem=TaskModel
                            @ref=PreventiveActionsGrid
                            Data=@Risk?.Tasks.Where(x => x.Pmo).ToList()
                            FileName=@GridNames.RiskEdit.PreventiveActions
                            UseOpenTextInsteadOfEdit=true
                            Interactive=!readOnly
                            AllowXlsExport=true
                            AllowFiltering=true
                            DeleteCallback=@DeleteTask
                            SaveCallback=@SaveTask
                            AddRowCallBack=@(_ => OpenPreventiveActionPopup(0, true))
                            EditCallBackOverride=@(args => OpenPreventiveActionPopup(args.Id, false))
                            PasteCallBack=@(args => PasteTaskCallback(args.Item1, args.Item2))
                            DropDownOverrides=@TaskModelDropDownOverrides
                            CopyToPmoRowCallBack=@DeriveTask/>
                        </RadzenTabsItem>

                        <RadzenTabsItem Text=@TabPmoTitle>
                            <RiskMatrixContainer Risk=@Risk Language=@Language Currency=@Currency MatrixType="MatrixTypes.Pmo" OnChangeCallback=@EditRiskMatrixCallBack
                                         MtbfLabel=@Localizer["RaMtbfLbl"] YearLabel=@Localizer["RaYearLbl"] ShowPointsLabel=@Localizer["RaShowPointsLbl"]
                                         IsRiskAnalysis=@IsRiskAnalysisView IsSapaView=@IsSapaView IsCsirView=@IsCsirView TypesSetToColumn=@TypesSetToColumn 
                                         ShowValuesLabel=@Localizer["RaShowValuesLbl"] ShowCustomValuesLabel=@Localizer["RaShowCustValuesLbl"] IsReadOnly=@RiskIsReadOnly() />
                        </RadzenTabsItem>
                    }

                    @if (!IsSapaView)
                    {
                        <!-- Tab Risk assessment spares -->
                        <RadzenTabsItem Text=@($"{TabSparesTitle} ({Risk.Spares.Count(x => !x.Pmo)})")>
                            <UtilityGrid TItem=SpareModel
                            @ref=SparesGrid
                            Data=@Risk?.Spares.Where(x => !x.Pmo).ToList()
                            FileName=@GridNames.RiskEdit.Spares
                            Interactive=!readOnly
                            AllowXlsExport=true
                            AllowFiltering=true
                            DeleteCallback=@DeleteSpare
                            SaveCallback=@UpdateSpare
                            AddRowCallBack=@(_ => OpenSparePopup(0, false))
                            OpenPopup=@(args => OpenSparePopup(args.Id, false))
                            PasteCallBack=@(args => PasteSpareCallback(args.Item1, args.Item2))
                            AllowCut=true
                            CopyFromPmoCallback=@ReverseDeriveSpare/>
                        </RadzenTabsItem>
                    }

                    @if (IsRiskAnalysisView == true) //Canon for mosquito: I don't want to have the 'copy to PMO'-option in the pull down in the RCM mode
                    {
                        <!-- Tab Risk assessment actions -->
                        <RadzenTabsItem Text=@($"{TabTasksTitle} ({Risk.Tasks.Count(x => !x.Pmo)})")>
                            <UtilityGrid TItem=TaskModel
                            @ref=PreventiveActionsGrid
                            Data=@Risk?.Tasks.Where(x => !x.Pmo).ToList()
                            FileName=@GridNames.RiskEdit.PreventiveActions
                            UseOpenTextInsteadOfEdit=true
                            Interactive=!readOnly
                            AllowXlsExport=true
                            AllowFiltering=true
                            DeleteCallback=@DeleteTask
                            SaveCallback=@SaveTask
                            EditCallBackOverride=@(args => OpenPreventiveActionPopup(args.Id, false))
                            AddRowCallBack=@(_ => OpenPreventiveActionPopup(0, false))
                            PasteCallBack=@(args => PasteTaskCallback(args.Item1, args.Item2))
                            AllowCut=true
                            DropDownOverrides=@TaskModelDropDownOverrides />
                        </RadzenTabsItem>
                    }
                    else
                    {
                        <!-- Tab Risk assessment actions -->
                        <RadzenTabsItem Text=@($"{TabTasksTitle} ({Risk.Tasks.Count(x => !x.Pmo)})")>
                            <UtilityGrid TItem=TaskModel
                                 @ref=PreventiveActionsGrid
                                 Data=@Risk?.Tasks.Where(x => !x.Pmo).ToList()
                                 FileName=@GridNames.RiskEdit.PreventiveActions
                                 UseOpenTextInsteadOfEdit=true
                                 Interactive=!readOnly
                                 AllowXlsExport=true
                                 AllowFiltering=true
                                 DeleteCallback=@DeleteTask
                                 SaveCallback=@SaveTask
                                 AddRowCallBack=@(_ => OpenPreventiveActionPopup(0, false))
                                 EditCallBackOverride=@(args => OpenPreventiveActionPopup(args.Id, false))
                                 PasteCallBack=@(args => PasteTaskCallback(args.Item1, args.Item2))
                                 AllowCut=true
                                 DropDownOverrides=@TaskModelDropDownOverrides
                                 CopyFromPmoCallback=@ReverseDeriveTask />
                        </RadzenTabsItem>
                    }
                    <!-- Tab Risk assessment after -->
                    <RadzenTabsItem Text=@Localizer["RaRiskAfterTabTxt"]>
                        <RiskMatrixContainer Currency=@Currency Risk=@Risk Language=@Language MatrixType="MatrixTypes.After" OnChangeCallback=@EditRiskMatrixCallBack
                                         MtbfLabel=@Localizer["RaMtbfLbl"] YearLabel=@Localizer["RaYearLbl"] ShowPointsLabel=@Localizer["RaShowPointsLbl"]
                                         IsRiskAnalysis=@IsRiskAnalysisView IsSapaView=@IsSapaView IsCsirView=@IsCsirView 
                                         TypesSetToColumn=@TypesSetToColumn ShowValuesLabel=@Localizer["RaShowValuesLbl"] ShowCustomValuesLabel=@Localizer["RaShowCustValuesLbl"] 
                                         IsReadOnly=@RiskIsReadOnly() />
                    </RadzenTabsItem>
                </Tabs>
            </RadzenTabs>
        }
        </div>
    </div>
