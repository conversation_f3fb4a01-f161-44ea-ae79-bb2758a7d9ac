﻿@page "/value-risk-organizer/{RiskObjectId:int}/risks/{RiskId:int}/copy-settings"

<Radzen.Blazor.RadzenTemplateForm TItem=RiskCopySettings
                                  Data=@RiskCopySettings
                                  Submit=@ValidTaskSubmitted
                                  OnInvalidSubmit=@InvalidTaskSubmitted>

    <div class="form-group">@(RisksToCopy?.Count() ?? 0) @Localizer["RcWRiskCountTxt"]
    </div>

    <div class="form-group">
        <label>@GetObjectLevel(ObjectLevel.System):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" TextProperty="Name" ValueProperty="Id" class="form-control"
                        Data="@Objects.Where(x => x.Level == (int)ObjectLevel.System)"
                        @bind-Value="@RiskCopySettings.SystemId" />
    </div>
    <div class="form-group">
        <label>@GetObjectLevel(ObjectLevel.Component):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" TextProperty="Name" ValueProperty="Id" class="form-control"
                        Data="@Objects.Where(x => x.Level == (int)ObjectLevel.Component)"
                        @bind-Value="@RiskCopySettings.ComponentId" />
    </div>
    <div class="form-group">
        <label>@GetObjectLevel(ObjectLevel.Assembly):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" TextProperty="Name" ValueProperty="Id" class="form-control"
                        Data="@Objects.Where(x => x.Level == (int)ObjectLevel.Assembly)"
                        @bind-Value="@RiskCopySettings.AssemblyId" />
    </div>

    <input type="submit" class="btn btn-primary" value=@Localizer["RcWCopyAllBtn"] />
</Radzen.Blazor.RadzenTemplateForm>
