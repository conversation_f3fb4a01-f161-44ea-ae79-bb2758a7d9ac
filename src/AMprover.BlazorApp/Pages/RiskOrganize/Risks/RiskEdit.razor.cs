using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BlazorApp.Components.Pagination;
using AMprover.BlazorApp.Pages.RiskOrganize.Spares;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Failures;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components;
using AMprover.BusinessLogic.Constants;
using Microsoft.JSInterop;
using ObjectModel = AMprover.BusinessLogic.Models.RiskAnalysis.ObjectModel;
using RiskObjectModel = AMprover.BusinessLogic.Models.RiskAnalysis.RiskObjectModel;
using AMprover.BlazorApp.Components.RiskAnalysis;
using System.Globalization;
using System.Text.RegularExpressions;
using AMprover.BlazorApp.Helpers;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Status = AMprover.BusinessLogic.Enums.Status;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Risks
{
    public partial class RiskEdit
    {
        #region Parameters

        [Parameter] public int RiskObjectId { get; set; }

        [Parameter] public int RiskId { get; set; }

        #endregion

        [Inject] private IStringLocalizer<RiskEdit> Localizer { get; set; }
        [Inject] private IGlobalDataService GlobalDataService { get; set; }
        [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
        [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; }
        [Inject] private IScenarioManager ScenarioManager { get; set; }
        [Inject] private IRiskOrganizerManager RiskOrganizerManager { get; set; }
        [Inject] private ILookupManager LookupManager { get; set; }
        [Inject] private ISparePartManager SparePartManager { get; set; }
        [Inject] private IPortfolioSetupManager PortfolioSetupManager { get; set; }
        [Inject] private IFailureManager FailureManager { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; }
        [Inject] private IPageNavigationManager PageNavigationManager { get; set; }
        [Inject] private IDropdownManager DropdownManager { get; set; }
        [Inject] private IJSRuntime JsRuntime { get; set; }
        [Inject] private IObjectManager ObjectManager { get; set; }
        [Inject] private JavaScriptHelper JavaScriptHelper { get; set; }
        [Inject] private DialogService DialogService { get; set; }
        [Inject] private AuthenticationStateProvider AuthenticationStateProvider { get; set; }
        [Inject] private UserManager<UserAccount> UserManager { get; set; }
        [Inject] private ILoggerFactory LoggerFactory { get; set; }

        #region Properties

        private string PageTitle { get; set; }
        private string TabBeforeTitle { get; set; }
        private string TabPmoTitle { get; set; }
        private string TabSparesTitle { get; set; }
        private string TabTasksTitle { get; set; }
        private string TabSparesPmoTitle { get; set; }
        private string TabTasksPmoTitle { get; set; }
        private string PrevActionDetail { get; set; }
        private string RaWithActionsTxt { get; set; }

        private bool IsRiskAnalysisView { get; set; }
        private bool IsCsirView { get; set; }
        public bool IsSapaView { get; set; }
        private bool IsPmoView { get; set; }
        private bool TypesSetToColumn { get; set; }

        private List<DepartmentModel> UserDepartments { get; set; }
        private IEnumerable<string> UserRoles { get; set; }
        private bool IsAdminUser { get; set; }
        public bool IsMaintenanceEngineer => UserRoles?.Contains(RoleConstants.MaintenanceEngineering) == true;
        private bool IsFinancialControl => UserRoles?.Contains(RoleConstants.FinancialControl) == true;
        public bool IsNormalUser => UserRoles?.Contains(RoleConstants.Default) == true;

        private RiskObjectModel _riskObject;

        private RiskObjectModel RiskObject
        {
            get => _riskObject;
            set
            {
                if (value == null)
                    NavigationManager.NavigateTo("/");

                _riskObject = value;

                if (_riskObject == null) return;
                RiskObjectId = _riskObject.Id;
                RiskTree.Initialize(RiskAnalysisManager.GetRisksTreeNodeByRiskObject(RiskObjectId));
                SelectRiskNode(RiskId);
            }
        }

        [Inject] private RiskEditQueryParams QueryParams { get; set; }

        private Paginator Paginator { get; set; }

        private UtilityGrid<SpareModel> SparesGrid { get; set; }
        private UtilityGrid<TaskModel> PreventiveActionsGrid { get; set; }

        public RiskModel Risk { get; set; }

        public TreeGeneric<RiskTreeObject> RiskTree { get; } = new();

        public List<LookupModel> Statuses { get; set; } = [];

        private Status? StatusFilter { get; set; }

        private List<string> CriticalityTypes { get; set; } = [];

        private List<ScenarioModel> Scenarios { get; set; } = [];
        private List<ObjectModel> Objects { get; set; } = [];

        private List<FailureCategory> FailureCategories1 { get; set; } = [];
        private List<FailureCategory> FailureCategories2 { get; set; } = [];

        private RadzenTabs RiskTabs { get; set; }
        private RadzenTabs TotalTabs { get; set; }

        private LookupSettingModel DefaultDepreciationPct { get; set; }
        private LookupSettingModel DefaultSpareManagementPct { get; set; }
        private LookupSettingModel DefaultModificationPct { get; set; }

        public string FileUpload { get; set; }
        private string UploadError { get; set; }

        private RadzenFileInput<string> UploadRef { get; set; }

        private Dictionary<Status?, string> ItemStatusDict { get; set; }
        private Dictionary<int?, string> FailureModeDict { get; set; }
        public Dictionary<int?, string> AdditionalDataDict { get; set; }
        private Dictionary<string, Dictionary<int, string>> TaskModelDropDownOverrides { get; set; }

        private bool ShowGauge { get; set; }

        private string Language => GlobalDataService.Language;

        private string Currency => GlobalDataService.Currency;

        private Dictionary<ObjectLevel, string> ObjectLevels { get; set; }

        private ILogger<RiskEdit> _logger;

        // Properties for grid data binding to ensure proper refresh
        public List<TaskModel> PmoTasks => Risk?.Tasks?.Where(x => x.Pmo).ToList() ?? [];
        public List<TaskModel> NonPmoTasks => Risk?.Tasks?.Where(x => !x.Pmo).ToList() ?? [];

        #endregion

        #region Chart Data

        public class DataItem
        {
            public string RiskType { get; set; }
            public double Cost { get; set; }
            public string CostString { get; set; }
        }

        public string FormatAxis(object value)
        {
            return FormatAsSelectedCurrency((decimal) value);
        }

        private DataItem[] ChartDataActions { get; set; }
        private DataItem[] ChartDataDirectRiskCost { get; set; }
        private DataItem[] ChartDataValueRisk { get; set; }

        #endregion

        #region Lifecycle Methods

        protected override async Task OnInitializedAsync()
        {
            _logger = LoggerFactory.CreateLogger<RiskEdit>();

            await SetupUserAccess();

            FailureModeDict = DropdownManager.GetNullableFailureModesDict();
            TaskModelDropDownOverrides = DropdownManager.GetTaskModelDropDownOverrides();
            RiskObject = RiskOrganizerManager.TryGetRiskObject(RiskObjectId);
            ItemStatusDict = StatusExtensions.GetStatusDictionaryNullable();
            AdditionalDataDict = DropdownManager.GetAdditionalDataDict();

            if (RiskObject == null)
                NavigationManager.NavigateTo("/value-risk-organizer");

            ShowGauge = LookupManager.GetRiskAnalysisShowGauge();
            Scenarios = ScenarioManager.GetAllScenarios();
            Objects = RiskAnalysisManager.GetAllObjects();
            ObjectLevels = ObjectManager.GetObjectLevelNames();
            CriticalityTypes = RiskAnalysisManager.GetCriticalityTypes();

            var failureCategories = FailureManager.GetAllFailureCategories();
            FailureCategories1 = failureCategories.Primary;
            FailureCategories2 = failureCategories.Secondary;

            Statuses = LookupManager.GetLookupByFilter("StatusTypes");
            PageNavigationManager.SetSelectedScenario(RiskObject.ScenarioId);

            DefaultDepreciationPct = LookupManager.GetLookupSettingByPropertyName("DepreciationPct");
            DefaultSpareManagementPct = LookupManager.GetLookupSettingByPropertyName("SpareManPct");
            DefaultModificationPct = LookupManager.GetLookupSettingByPropertyName("ModificationPct");

            LoadExtraData(QueryParams.BottomTabs);

            var analysisType = RiskObject.AnalysisType.ToLower();
            IsRiskAnalysisView = !analysisType.Contains("pmo") && !analysisType.Contains("csir") &&
                                 !analysisType.Contains("sapa");
            IsCsirView = analysisType.Contains("csir");
            IsSapaView =
                analysisType
                    .Contains("sapa"); //sapa calculation enables to calculate with percentages (also enable sapa on settings page (administrator))
            IsPmoView = analysisType.Contains("pmo");
            TypesSetToColumn = LookupManager.GetSetTypesToColumn() && IsSapaView;
            if (!IsPmoView) ShowGauge = false;

            ProcessTaskConfigurationEffectOnRisk(); //PMO acties worden op een of andere manier gereset. Op deze wijze dwing ik een correctie visualisatie af, maar is onjuist****
            SetTitlesBasedOnStatus();

            if (IsRiskAnalysisView || IsSapaView)
            {
                OnChangeBottomTabsComponent(0); //Set the Tab on the first page
            }
            else
            {
                SetChartData();
                OnChangeBottomTabsComponent(2); //Set the Tab on the PMO risk page
            }
        }

        protected override void OnAfterRender(bool firstRender)
        {
            if (firstRender)
            {
                UpdatePaginator();
            }

            base.OnAfterRender(firstRender);
        }

        #endregion

        #region Methods

        private void SetTitlesBasedOnStatus()
        {
            if (IsRiskAnalysisView)
            {
                PageTitle = Localizer["RaHeaderTxt"];
                TabTasksTitle = Localizer["RaActionsTabTxt"];
                PrevActionDetail = Localizer["RaPrevActDetailsTxt"];
                RaWithActionsTxt = Localizer["RaWithActionsTxt"];
            }
            else if (IsSapaView)
            {
                PageTitle = "Strategic Asset Portfolio Analysis";
                TabTasksTitle = Localizer["RaSapaActionsTabTxt"];
                PrevActionDetail = Localizer["RaSapaDetailsTxt"];
                RaWithActionsTxt = Localizer["RaWithSapaActionsTxt"];
            }
            else
            {
                PageTitle = Localizer["RaPMOHeaderTxt"];
                TabTasksTitle = Localizer["RaActionsTabTxt"];
                PrevActionDetail = Localizer["RaPrevActDetailsTxt"];
                RaWithActionsTxt = Localizer["RaWithActionsTxt"];
            }

            TabPmoTitle = Localizer["RaRiskPmoTabTxt"];
            TabBeforeTitle = Localizer["RaRiskBeforeTabTxt"];
            TabSparesTitle = Localizer["RaSparesTabTxt"];
            TabSparesPmoTitle = Localizer["RaSparesPMOTabTxt"];
            TabTasksPmoTitle = Localizer["RaActionsPMOTabTxt"];
        }

        private void SetChartData()
        {
            if (Risk == null)
                return;

            decimal? prevCostPmo = Risk.PreventiveCostsPmo ?? 0;
            decimal? prevCost = Risk.PreventiveCosts ?? 0;
            decimal? directCostPmo = (Risk.DirectCostPmo ?? 0) / (Risk.MtbfPmo ?? 1000000000000);
            decimal? directCost = (Risk.DirectCostAfter ?? 0) / (Risk.MtbfAfter ?? 1000000000000);
            decimal? riskPmo = (Risk.RiskPmo ?? 0) - (decimal) directCostPmo;
            decimal? riskAfter = (Risk.RiskAfter ?? 0) - (decimal) directCost;

            ChartDataActions =
            [
                new()
                {
                    RiskType = "Current", Cost = (double) prevCostPmo,
                    CostString = FormatAsSelectedCurrency(prevCostPmo)
                },
                new() {RiskType = "Analysed", Cost = (double) prevCost, CostString = FormatAsSelectedCurrency(prevCost)}
            ];
            ChartDataDirectRiskCost =
            [
                new()
                {
                    RiskType = "Current", Cost = (double) directCostPmo,
                    CostString = FormatAsSelectedCurrency(directCostPmo)
                },
                new()
                {
                    RiskType = "Analysed", Cost = (double) directCost, CostString = FormatAsSelectedCurrency(directCost)
                }
            ];
            ChartDataValueRisk =
            [
                new() {RiskType = "Current", Cost = (double) riskPmo, CostString = FormatAsSelectedCurrency(riskPmo)},
                new()
                {
                    RiskType = "Analysed", Cost = (double) riskAfter, CostString = FormatAsSelectedCurrency(riskAfter)
                }
            ];
        }

        public int GetInitialPaginatorValue()
        {
            var node = RiskTree.GetRiskTreeNode(RiskId);
            return GetRiskTreeNodes().IndexOf(node);
        }

        private void ToggleGauge(bool? value)
        {
            if (value == null) return;
            var setting = new LookupSettingModel
            {
                Property = PropertyNames.RiskAnalysisShowGauge,
                TextValue = value.ToString(),
                SettingType = SettingType.Text
            };

            LookupManager.SaveLookupSettings(setting);
        }

        public int GetRiskCount()
        {
            return GetRiskTreeNodes().Count;
        }

        public void PaginatorCallback(int riskIndex)
        {
            var riskId = GetRiskTreeNodes().Skip(riskIndex).FirstOrDefault()?.Source?.RiskId;

            if (riskId != null)
            {
                SelectRiskNode(riskId.Value);
                Risk = RiskAnalysisManager.GetRisk(RiskId);
                NavigateToRisk(RiskObjectId, RiskId);
            }
        }

        private void UpdateStatusFilter()
        {
            RiskTree.Initialize(RiskAnalysisManager.GetRisksTreeNodeByRiskObject(RiskObjectId, (int?) StatusFilter));
            SelectRiskNode(RiskId);
        }

        private void SelectRiskNode(int riskId)
        {
            var node = RiskTree.GetRiskTreeNode(riskId);

            if (node == null)
                SelectFirstRiskNode();
            else
                SelectTreeNode(node);

            //Try to correct configuration
            if (RiskObjectId <= 0) RiskObjectId = Risk.RiskObjectId;

            //If RiskObjectId is bigger than 0 then save querystring
            if (RiskObjectId > 0)
                PageNavigationManager.SavePageQueryString($"/value-risk-analysis/{RiskObjectId}/risks/{RiskId}",
                    string.Empty);
        }

        private void SelectFirstRiskNode()
        {
            var riskNode = RiskTree.GetFirstRiskNode();

            if (riskNode != null)
                SelectTreeNode(riskNode);
            else
            {
                Risk = null;
            }
        }

        private void SelectTreeNode(TreeNodeGeneric<RiskTreeObject> node)
        {
            if (node.Source.RiskId == null) return;
            RiskId = node.Source.RiskId.Value;
            Risk = RiskAnalysisManager.GetRisk(RiskId);
            NavigateToRisk(RiskObjectId, RiskId);

            if (!node.IsSelected)
                RiskTree.SelectNode(node);

            var parent = node.Parent;
            while (parent != null)
            {
                parent.Open = true;
                parent = parent.Parent;
            }
        }

        private void ChangeTreeNodeOrder(List<TreeNodeGeneric<RiskTreeObject>> nodes)
        {
            foreach (var node in nodes)
            {
                node.Source.SortOrder = node.SortOrder;
            }

            RiskAnalysisManager.UpdateRiskOrder(nodes.Select(x => x.Source).ToList());
        }

        private void UpdateTaskGridDropdowns()
        {
            TaskModelDropDownOverrides = DropdownManager.GetTaskModelDropDownOverrides();

            if (Risk != null)
            {
                TaskModelDropDownOverrides.Add(nameof(TaskModel.PartOf),
                    Risk.Tasks.ToDictionary(x => x.Id, x => x.Name));
            }

            PreventiveActionsGrid?.RefreshDropdownOverrides(TaskModelDropDownOverrides);
        }

        private List<TreeNodeGeneric<RiskTreeObject>> GetRiskTreeNodes()
        {
            return RiskTree.GetFlattenedNodes(x => x.Source.NodeType == RiskTreeNodeType.Risk);
        }

        private void UpdatePaginator()
        {
            if (Paginator != null)
            {
                var node = RiskTree.GetRiskTreeNode(RiskId);
                var riskNodes = GetRiskTreeNodes();
                Paginator.SetCurrentExternally(riskNodes.IndexOf(node));
            }
        }

        private void OnChangeTopTabsComponent(int index)
        {
            QueryParams.TopTabs = index;
            SetQueryString();
        }

        public void OnChangeBottomTabsComponent(int index)
        {
            QueryParams.BottomTabs = index;
            SetQueryString();
            LoadExtraData(2);
        }

        private void LoadExtraData(int bottomTabIndex)
        {
            if (Risk != null && bottomTabIndex == 2) // Preventive Actions Tab
                Risk.Tasks = RiskAnalysisManager.GetTasksWithDetailsByRisk(Risk.Id);
        }

        private void SetQueryString()
        {
            var url = $"{NavigationManager.Uri.Split('?')[0]}{QueryParams.ToQueryString()}";

            if (NavigationManager.Uri != url)
                NavigationManager.NavigateTo(url);
        }

        private void CloseModal()
        {
            RiskTabs?.Reload();
            SparesGrid?.Grid?.Reload();
            PreventiveActionsGrid?.Grid?.Reload();
            TotalTabs?.Reload();
        }

        public void UpdateCurrentRisk()
        {
            if (Risk.FailureModeId != Risk.FailureMode?.Id)
                ChangeNameBasedOnMode();

            Risk = RiskAnalysisManager.UpdateRisk(Risk);
            StateHasChanged();
        }

        private void CreateNewRisk()
        {
            CreateAndSelectRisk(GetNewRisk());
            OnChangeBottomTabsComponent(IsRiskAnalysisView ? 0 : 2);
        }

        public async Task ImportCurrentTaskPlan(string value)
        {
            // clear ref, the component wants to display an image preview which does not apply
            UploadError = FileUpload = null;
            await UploadTaskPlans(value?.Split(',').LastOrDefault()?.Trim()).ConfigureAwait(false);
            await JsRuntime.InvokeAsync<bool>("ClearFileInputValue", UploadRef.Element).ConfigureAwait(false);
        }

        private async Task UploadTaskPlans(string base64Input)
        {
            if (string.IsNullOrWhiteSpace(base64Input))
                return;

            ShowLoadingDialog();

            var result = await RiskAnalysisManager.ProcessImportedTaskPlans(Risk, base64Input).ConfigureAwait(false);

            if (result.Success)
            {
                Risk = RiskAnalysisManager.GetRisk(RiskId);
            }

            DialogService.Close();
            // TODO: Reload tree??
            ShowImportResultDialog(result);
        }

        private void ShowLoadingDialog()
        {
            DialogService.Open<ImportResultDialog>("Loading",
                new Dictionary<string, object>
                {
                    {nameof(ImportResultDialog.ImportResult), new ImportResult {Status = ImportStatus.Loading}}
                });
        }

        private void ShowImportResultDialog(ImportResult importResult)
        {
            DialogService.Open<ImportResultDialog>(importResult.Success ? "Success" : "Error importing items",
                new Dictionary<string, object>
                {
                    {nameof(ImportResultDialog.ImportResult), importResult}
                });
        }

        public void DeleteSelectedRisk()
        {
            ShowAreYouSurePopup(Risk, Localizer["RaDeleteRiskWarning"],
                EventCallback.Factory.Create<RiskModel>(this, ConfirmedDeleteRisk));
        }

        private void ShowAreYouSurePopup<T>(T item, string text, EventCallback<T> yesCallback)
        {
            DialogService.Open<AreYouSureDialog<T>>("Are you sure?",
                new Dictionary<string, object>
                {
                    {nameof(AreYouSureDialog<T>.Item), item},
                    {nameof(AreYouSureDialog<T>.YesCallback), yesCallback},
                    {nameof(AreYouSureDialog<T>.Text), text}
                },
                new DialogOptions {CloseDialogOnEsc = false, ShowClose = false});
        }

        private void ConfirmedDeleteRisk(RiskModel risk)
        {
            var location = RiskTree.GetNodeLocation(RiskTree.SelectedNode);
            RiskTree.Initialize(RiskAnalysisManager.RemoveRiskNodeFromTree(RiskTree.Node, location), false);
            RiskAnalysisManager.DeleteRisk(Risk.Id);
            SelectNearestNode();
        }

        private void SelectNearestNode()
        {
            var location = RiskTree.GetNodeLocation(RiskTree.SelectedNode);
            var parentNode = location.Count > 1
                ? RiskTree.GetNodeFromLocation(location.Take(location.Count - 1).ToList())
                : null;

            if (parentNode?.Nodes.Any() == true)
            {
                var siblingNode = parentNode.Nodes[Math.Max(0, location.Last() - 1)];
                SelectTreeNode(siblingNode);
            }
            else
            {
                SelectFirstRiskNode();
            }

            UpdatePaginator();
        }

        private void DeleteRiskFromTree(object riskTreeObject)
        {
            if (riskTreeObject is RiskTreeObject risk)
            {
                if (!(risk.RiskId > 0)) return;
                RiskAnalysisManager.DeleteRisk(risk.RiskId.Value);

                if (risk.RiskId == Risk.Id)
                    SelectNearestNode();
                else
                    UpdatePaginator();
            }
            else
            {
                throw new ArgumentException(
                    $"Incorrect configuration, function should only receive object of Type {typeof(RiskTreeObject)}");
            }
        }

        public void PasteInRiskTree((List<RiskTreeObject> source, RiskTreeObject destination) args)
        {
            var copiedNodeType = args.source.FirstOrDefault()?.NodeType ?? RiskTreeNodeType.Risk;
            var destinationType = args.destination.NodeType;

            foreach (var s in args.source)
            {
                if (s.RiskId == null)
                    continue;

                var riskToCopy = RiskAnalysisManager.GetRisk(s.RiskId.Value);
                riskToCopy.Id = 0;

                // handle copying risk to a different RiskObject
                riskToCopy.RiskObjectId = Risk.RiskObjectId;
                riskToCopy.CollectionId = Risk.CollectionId;
                riskToCopy.InstallationId = Risk.InstallationId;

                // You can only paste by clicking on a Node, therefore it's currently not possible to set the SystemId to anything other that the destination Node SystemId
                riskToCopy.SystemId = args.destination.SystemId;

                // We default to pasting a Risk onto a System, in this case both Component and Assembly should be set to null
                riskToCopy.ComponentId = null;
                riskToCopy.AssemblyId = null;

                // If either the source or destination has a relevant value we need to copy, do that
                if ((int) copiedNodeType <= (int) RiskTreeNodeType.Component ||
                    (int) destinationType >= (int) RiskTreeNodeType.Component)
                    riskToCopy.ComponentId = args.destination.ComponentId ?? s.ComponentId;

                // Same for Assembly
                if ((int) copiedNodeType <= (int) RiskTreeNodeType.Assembly ||
                    (int) destinationType >= (int) RiskTreeNodeType.Assembly)
                    riskToCopy.AssemblyId = args.destination.AssemblyId ?? s.AssemblyId;

                CreateAndSelectRisk(riskToCopy);

                Risk.CalculateSpareCost(DefaultDepreciationPct.DecimalValue, DefaultSpareManagementPct.DecimalValue);
            }

            NavigateToRisk(RiskObjectId, RiskId);
            UpdatePaginator();
        }

        public void NewRiskInTree(RiskTreeObject treeObject)
        {
            if (RiskObject == null) return;

            var risk = GetNewRisk();
            risk.SystemId = treeObject?.SystemId;
            risk.ComponentId = treeObject?.ComponentId;
            risk.AssemblyId = treeObject?.AssemblyId;

            CreateAndSelectRisk(risk);
            UpdatePaginator();
        }

        private RiskModel GetNewRisk()
        {
            var failureMode = FailureModeDict.FirstOrDefault();
            return new RiskModel
            {
                RiskObjectId = RiskObjectId,
                RiskObject = RiskObject,
                CollectionId = RiskObject.ParentObjectId,
                InstallationId = RiskObject.ObjectId,
                Name = $"{failureMode.Value} New Risk".Trim(),
                FailureModeId = failureMode.Key,
                SystemId = Risk?.SystemId,
                ComponentId = Risk?.ComponentId,
                AssemblyId = Risk?.AssemblyId
            };
        }

        private void CreateAndSelectRisk(RiskModel risk)
        {
            Risk = RiskAnalysisManager.UpdateRisk(risk);
            RiskId = Risk.Id;
            RiskTree.Initialize(RiskAnalysisManager.AddRiskToTree(RiskTree.Node, Risk), false);
            SelectRiskNode(RiskId);
            _logger.LogInformation($"Creating a {nameof(Risk)} succeeded. Name = {Risk.Name}, ID = {Risk.Id}.");
        }

        private void UpdateObjectAndNavigate(RiskModel risk)
        {
            Risk = RiskAnalysisManager.UpdateRisk(risk);

            if (RiskTree.SelectedNode != null)
                RiskTree.SelectedNode.Name = Risk.Name;

            _logger.LogInformation($"Update of {nameof(Risk)} with Id {Risk.Id} succeeded.");
            NavigateToRisk(Risk.RiskObjectId, Risk.Id);
        }

        private void NavigateToRisk(int riskObjectId, int riskId)
        {
            if (Risk == null || Risk.Id != riskId)
                Risk = RiskAnalysisManager.GetRisk(riskId);

            if (riskObjectId <= 0) riskObjectId = Risk.RiskObjectId;

            LoadExtraData(QueryParams.BottomTabs);

            UpdateTaskGridDropdowns();

            // prevent navigation on opening the page
            if (!NavigationManager.Uri.Contains($"/value-risk-analysis/{riskObjectId}/risks/{riskId}"))
                NavigationManager.NavigateTo($"/value-risk-analysis/{riskObjectId}/risks/{riskId}{GetQueryString()}",
                    false);

            SelectCurrentRiskInTree(RiskId);

            if (IsPmoView) SetChartData();
        }

        private string GetQueryString()
        {
            return NavigationManager.Uri.Contains('?')
                ? $"?{NavigationManager.Uri.Split('?').LastOrDefault()}"
                : string.Empty;
        }

        private void SelectCurrentRiskInTree(int riskId)
        {
            var currentRiskTreeObject = RiskTree.GetRiskTreeNode(riskId);

            if (currentRiskTreeObject != null)
                RiskTree.SelectNode(currentRiskTreeObject);
        }

        public async Task DownloadImage()
        {
            // Invoke JavaScript function to convert HTML to image
            await JavaScriptHelper.InvokeAsync<object>("convertToImage", "content", Risk.Name);
        }

        public void ClickTreeNode(TreeNodeGeneric<RiskTreeObject> node)
        {
            if (RiskTree.MultiSelect)
            {
                if (node.Source.RiskId.HasValue) return;
                RiskTree.SelectNode(node);

                while (node.Source.RiskId == null)
                {
                    node.Open = true;
                    node = node.Nodes.First();
                }

                RiskTree.SelectNode(node);
            }
            else
            {
                if (node.Source.RiskId.HasValue)
                {
                    RiskId = node.Source.RiskId.Value;
                    Risk = RiskAnalysisManager.GetRisk(RiskId);
                    NavigateToRisk(RiskObjectId, RiskId);
                }
                else
                {
                    while (node.Source.RiskId == null)
                        node = node.Nodes.First();

                    RiskTree.SelectNode(node);
                    ClickTreeNode(node);
                }

                PageNavigationManager.SavePageQueryString($"/value-risk-analysis/{RiskObjectId}/risks/{RiskId}");
                ProcessTaskConfigurationEffectOnRisk();
                UpdatePaginator();
            }
        }

        private void ChangeObjectStructure(ObjectLevel level, ObjectModel objectModel)
        {
            // There is no foolproof way to find the previous item without significant refactoring,
            // however since we're only changing some texts, It's fine to just loop over all the possible previous objects
            // the only duplicates will be when multiple Objects (possibly on different levels) have the exact same name
            var previousPossibleObjects = Objects.Where(x => x.Name == RiskTree.SelectedNode.Parent.Name);

            switch (level)
            {
                case ObjectLevel.System:
                    Risk.SystemId = objectModel?.Id;

                    if (Risk.ComponentId == null && Risk.AssemblyId == null &&
                        (string.IsNullOrWhiteSpace(Risk.Function) ||
                         previousPossibleObjects.Any(x => x.Function == Risk.Function)))
                    {
                        Risk.Function = objectModel?.Function;
                    }

                    break;
                case ObjectLevel.Component:
                    Risk.ComponentId = objectModel?.Id;

                    if (Risk.AssemblyId == null &&
                        (string.IsNullOrWhiteSpace(Risk.Function) ||
                         previousPossibleObjects.Any(x => x.Function == Risk.Function)))
                    {
                        Risk.Function = objectModel?.Function;
                    }

                    break;
                case ObjectLevel.Assembly:
                    Risk.AssemblyId = objectModel?.Id;

                    if (string.IsNullOrWhiteSpace(Risk.Function) ||
                        previousPossibleObjects.Any(x => x.Function == Risk.Function))
                    {
                        Risk.Function = objectModel?.Function;
                    }

                    break;
            }

            var location = RiskTree.GetNodeLocation(RiskTree.SelectedNode);
            RiskTree.Initialize(RiskAnalysisManager.RemoveRiskNodeFromTree(RiskTree.Node, location), false);
            RiskTree.Initialize(RiskAnalysisManager.AddRiskToTree(RiskTree.Node, Risk), false);
            UpdateObjectAndNavigate(Risk);
            SelectRiskNode(RiskId);
            UpdatePaginator();
        }

        private void ChangeNameBasedOnMode()
        {
            // Check previous failure mode, used for changing the name
            var previousFailureModeName = Risk.FailureMode?.Name;
            var failureModeName = FailureModeDict.FirstOrDefault(x => x.Key == Risk.FailureModeId).Value;

            // Remove previous failureMode prefix from RiskName
            if (!string.IsNullOrWhiteSpace(previousFailureModeName))
                Risk.Name = Risk.Name.Replace(previousFailureModeName, string.Empty);

            // Prefix RiskName with new FailureMode name, and prevent duplication
            if (!string.IsNullOrWhiteSpace(failureModeName))
            {
                Risk.Name = Risk.Name.Replace(failureModeName, string.Empty);
                Risk.Name = $"{failureModeName.Trim()} {Risk.Name.Trim()}";
            }

            // Cleanup double spaces
            Risk.Name = Regex.Replace(Risk.Name, @"\s+", " ");

            ChangeRiskName(Risk.Name);
        }

        private void UpdateRiskNameDirectly()
        {
            ChangeRiskName();
            UpdateCurrentRisk();
        }

        private void UpdateSortOrderProperty()
        {
            Risk = RiskAnalysisManager.UpdateRisk(Risk);
            RiskTree.SelectedNode.Source.SortOrder = Risk.SortOrder;
            UpdateTreeSortOrder();
        }

        private void ChangeRiskName(string name = null)
        {
            Risk.Name = (name ?? Risk.Name).Trim().LimitLength(100);
            RiskTree.SelectedNode.Name = Risk.Name;
            UpdateTreeSortOrder();
        }

        private void UpdateTreeSortOrder()
        {
            RiskTree.SelectedNode.Parent.Nodes = RiskTree.SelectedNode.Parent.Nodes
                .OrderBy(x => x.Source.SortOrder)
                .ThenBy(x => x.Name).ToList();
        }

        private string GetPageTitle()
        {
            var sb = new StringBuilder();

            sb.Append($"{RiskObject.Scenario.Name} - ");

            if (RiskObject.ParentObjectId != null)
                sb.Append($"{Objects.FirstOrDefault(x => x.Id == RiskObject.ParentObjectId)} - ");

            sb.Append($"{RiskObject.Name}");

            return sb.ToString();
        }

        private void EditRiskMatrixCallBack(RiskModel risk)
        {
            Risk.MtbfAfter = risk.MtbfAfter;
            Risk.MtbfBefore = risk.MtbfBefore;
            Risk.MtbfPmo = risk.MtbfPmo;

            if (IsPmoView) SetChartData();
            ProcessTaskConfigurationEffectOnRisk();

            UpdateCurrentRisk();
        }

        private string GetRiskOrganizerUrl() =>
            $"/value-risk-organizer?scenario={RiskObject?.ScenarioId}&riskobject={RiskObjectId}&scenarios={RiskObject?.ScenarioId}&categories={RiskObject?.ParentObjectId ?? 0}";

        private string GetObjectLevel(ObjectLevel level) =>
            ObjectLevels.TryGetValue(level, out var value) ? value : level.ToString();

        private string FormatAsSelectedCurrency(decimal? value)
        {
            return value == null
                ? string.Empty
                : value.Value.ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
        }

        private string FormatAsNumber(decimal? value)
        {
            return value == null
                ? string.Empty
                : value.Value.ToString("N0", CultureInfo.CreateSpecificCulture(Currency));
        }

        private static string FormatAsPoints(decimal? value)
        {
            return value == null ? string.Empty : value.Value.ToString("N0");
        }

        private static string FormatAsResistanceLevel(decimal? value)
        {
            if (value == null) return string.Empty;

            value = Math.Round((decimal) value);
            if (value > 1) value -= 1; //This is done to define the specific demands of the CSIR Resistance Number
            return value.Value.ToString("N0");
        }

        private bool RiskIsReadOnly()
        {
            if (Risk == null) return false;

            if (IsSapaView)
                return Risk?.RiskObject?.Status is (int)Status.Budgeting or (int)Status.Complete;

            if (IsFinancialControl)
                return true;

            if ((UserDepartments == null || UserDepartments.Count == 0) && IsSapaView)
                return true;

            //if (_riskObject?.DepartmentId.HasValue == true &&
            //    UserDepartments.Any(dept => dept.Id == _riskObject.DepartmentId.Value))
            //    return false;

            return false; //For the moment, not to strict...
        }

        private string GetRiskIsReadOnlyText()
        {
            var statusInt = Risk?.RiskObject?.Status;
            var status = statusInt.HasValue && Enum.IsDefined(typeof(Status), statusInt)
                ? (Status?)statusInt
                : null;

            return status.HasValue
                ? $"READONLY - {status}"
                : "READONLY";
        }

        private async Task SetupUserAccess()
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity?.IsAuthenticated == true)
            {
                UserRoles = user.Claims
                    .Where(c => c.Type == System.Security.Claims.ClaimTypes.Role)
                    .Select(c => c.Value).ToList();

                IsAdminUser = UserRoles.Contains(RoleConstants.Administrators) ||
                              UserRoles.Contains(RoleConstants.PortfolioAdministrators);

                if (!IsAdminUser)
                {
                    var userAccount = await UserManager.FindByNameAsync(user.Identity?.Name);
                    if (userAccount != null)
                    {
                        UserDepartments = RiskAnalysisSetupManager.GetUserDepartments(userAccount.Id).ToList();
                    }
                }
            }
        }

        #endregion

        #region Task Methods

        private void OpenPreventiveActionPopup(int id, bool pmo)
        {
            DialogService.Open<PreventiveActionEdit>(PrevActionDetail,
                new Dictionary<string, object>
                {
                    {"PreventiveActionId", id},
                    {"RiskId", RiskId},
                    {"Callback", EventCallback.Factory.Create<TaskModel>(this, SaveTask)},
                    {"Pmo", pmo},
                    {"IsSapaTask", IsSapaView}
                },
                new DialogOptions {Width = "840px", /*Height = "525px",*/ Resizable = true, Draggable = true});
        }

        private void DeleteTask(TaskModel task)
        {
            var dbTask = Risk.Tasks.FirstOrDefault(x => x.Id == task.Id);
            if (dbTask == null) return;
            Risk.Tasks.Remove(task);
            Risk.Tasks = [..Risk.Tasks];
            RiskAnalysisManager.DeleteTask(task);

            ProcessTaskConfigurationEffectOnRisk();
            StateHasChanged(); 
        }

        private void SaveTask(TaskModel task)
        {
            if (task.ClusterCosts > 0 && task.ClusterCosts == task.Costs && task.ClusterCosts != task.EstCosts)
            {
                DialogService.Open<AreYouSureDialog<TaskModel>>(Localizer["RaOverrideCostsTitle"],
                    new Dictionary<string, object>
                    {
                        {nameof(AreYouSureDialog<TaskModel>.Item), task},
                        {
                            nameof(AreYouSureDialog<TaskModel>.YesCallback),
                            EventCallback.Factory.Create<TaskModel>(this, SaveTaskOverrideCosts)
                        },
                        {
                            nameof(AreYouSureDialog<TaskModel>.NoCallback),
                            EventCallback.Factory.Create<TaskModel>(this, SaveTaskInternal)
                        },
                        {nameof(AreYouSureDialog<TaskModel>.Text), Localizer["RaOverrideCostsText"].Value},
                    },
                    new DialogOptions
                        {CloseDialogOnEsc = false, ShowClose = false, Resizable = false, Draggable = true});

                return;
            }

            SaveTaskOverrideCosts(task);
        }

        private void SaveTaskOverrideCosts(TaskModel task)
        {
            // When saving a Task from the RiskEditor, the Costs need to be copied over from the Estimated Costs
            task.Costs = task.EstCosts;
            SaveTaskInternal(task);
        }

        private void SaveTaskInternal(TaskModel task)
        {
            if (task.PartOf.HasValue)
            {
                if (task.Id == task.PartOf)
                {
                    task.PartOf = null;

                    // Show popup, not Allowed to set itself as parent
                    DialogService.Open<InformationDialog>("field 'PartOf' Cleared.",
                        new Dictionary<string, object>
                        {
                            {"DialogContent", $"'{task.Name}' can't be part of itself"}
                        });
                }
                else
                {
                    var parentTask = Risk.Tasks.Find(x => x.Id == task.PartOf);

                    if (parentTask?.PartOf != null)
                    {
                        task.PartOf = null;
                        DialogService.Open<InformationDialog>("field 'PartOf' Cleared.",
                            new Dictionary<string, object>
                            {
                                {
                                    "DialogContent",
                                    $"'{parentTask.Name}' has a PartOf istelf. Multiple levels of inheritence is not supported"
                                }
                            });
                    }
                }
            }

            // Save the Task
            RiskAnalysisManager.UpdateTask(task);

            // Process the Costs and save the Risk BEFORE refreshing
            ProcessTaskConfigurationEffectOnRisk();
            Risk = RiskAnalysisManager.UpdateRisk(Risk);

            // Refresh the risk after all updates are complete
            Risk = RiskAnalysisManager.GetRisk(Risk.Id);

            // Force component re-render to update grid data bindings after Risk object replacement
            StateHasChanged();
        }

        private void ProcessTaskConfigurationEffectOnRisk()
        {
            UpdateTaskGridDropdowns();

            var defaultModificationPct = LookupManager.GetLookupSettingByPropertyName("ModificationPct");
            Risk?.CalculateTasksCost(defaultModificationPct?.DecimalValue);

            TotalTabs?.Reload();
        }

        private void PasteTaskCallback(TaskModel model, CopyType copyType)
        {
            switch (copyType)
            {
                case CopyType.Copy:
                    var copiedTask = model.CopyAsNew<TaskModel>();
                    if (copiedTask.PartOf.HasValue && !Risk.Tasks.Exists(x => x.Id == copiedTask.PartOf))
                    {
                        copiedTask.PartOf = null;
                    }

                    copiedTask.MrbId = Risk.Id;
                    copiedTask = RiskAnalysisManager.UpdateTask(copiedTask);
                    Risk.Tasks = [..Risk.Tasks.Append(copiedTask)];
                    break;

                case CopyType.Cut:
                    if (model.MrbId == Risk.Id) break;
                    if (model.PartOf.HasValue && !Risk.Tasks.Exists(x => x.Id == model.PartOf))
                    {
                        model.PartOf = null;
                    }

                    model.MrbId = Risk.Id;
                    model = RiskAnalysisManager.UpdateTask(model);
                    Risk.Tasks = [..Risk.Tasks.Append(model)];
                    break;
            }

            ProcessTaskConfigurationEffectOnRisk();
        }

        private void ReverseDeriveTask(TaskModel model)
        {
            Risk.Tasks.Add(RiskAnalysisManager.DeriveTask(model, true));
        }

        private void DeriveTask(TaskModel model)
        {
            Risk.Tasks.Add(RiskAnalysisManager.DeriveTask(model, false));
        }

        #endregion

        #region Spare Methods

        private void OpenSparePopup(int id, bool pmo)
        {
            DialogService.Open<SpareEdit>(@Localizer["RaSparePartDetailsTxt"],
                new Dictionary<string, object>
                {
                    {"SpareId", id},
                    {"RiskId", RiskId},
                    {"Callback", EventCallback.Factory.Create<SpareModel>(this, AddOrEditSpareCallback)},
                    {"Pmo", pmo}
                },
                new DialogOptions {Width = "700px", /*Height = "510px",*/ Resizable = false, Draggable = true});
        }

        private void DeleteSpare(SpareModel spare)
        {
            var dbSpare = Risk.Spares.FirstOrDefault(x => x.Id == spare.Id);
            if (dbSpare == null) return;
            Risk.Spares.Remove(spare);
            Risk.Spares = [..Risk.Spares];
            Risk = RiskAnalysisManager.DeleteSpare(spare);
        }

        private void UpdateSpare(SpareModel spare)
        {
            spare.CalculateSparePartCost();
            SparePartManager.UpdateSpare(spare);
            ProcessSpareConfigurationEffectOnRisk();
        }

        private void AddOrEditSpareCallback(SpareModel spare)
        {
            spare.MrbId = Risk.Id;

            var currentSpare = Risk.Spares.FirstOrDefault(x => x.Id == spare.Id);
            if (currentSpare != null)
                Risk.Spares[Risk.Spares.IndexOf(currentSpare)] = spare;
            else
                Risk.Spares.Add(spare);

            UpdateSpare(spare);
            TotalTabs?.Reload();
            CloseModal();
        }

        private void PasteSpareCallback(SpareModel model, CopyType copyType)
        {
            switch (copyType)
            {
                case CopyType.Copy:
                    var copiedSpare = model.CopyAsNew<SpareModel>();
                    copiedSpare.MrbId = Risk.Id;
                    copiedSpare = SparePartManager.UpdateSpare(copiedSpare);
                    Risk.Spares = [..Risk.Spares.Append(copiedSpare)];
                    break;

                case CopyType.Cut:
                    if (model.MrbId == Risk.Id) break;
                    model.MrbId = Risk.Id;
                    model = SparePartManager.UpdateSpare(model);
                    Risk.Spares = [..Risk.Spares.Append(model)];
                    break;
            }

            ProcessSpareConfigurationEffectOnRisk();
        }

        private void ProcessSpareConfigurationEffectOnRisk()
        {
            //Does task and spare parts calculation
            Risk.CalculatePreventiveCosts(DefaultDepreciationPct.DecimalValue, DefaultSpareManagementPct.DecimalValue,
                DefaultModificationPct.DecimalValue, true);

            Risk = RiskAnalysisManager.UpdateRisk(Risk);
            TotalTabs?.Reload();

            ProcessTaskConfigurationEffectOnRisk(); //PMO acties worden op een of andere manier gereset. Op deze wijze dwing ik een correctie visualisatie af, maar is onjuist****
        }

        private void ReverseDeriveSpare(SpareModel model)
        {
            Risk.Spares.Add(RiskAnalysisManager.DeriveSpare(model, true));
        }

        private void DeriveSpare(SpareModel model)
        {
            Risk.Spares.Add(RiskAnalysisManager.DeriveSpare(model, false));
        }

        #endregion
    }
}