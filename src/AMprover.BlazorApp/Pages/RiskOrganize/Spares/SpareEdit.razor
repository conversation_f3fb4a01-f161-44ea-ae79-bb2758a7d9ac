﻿@page "/value-risk-organizer/spares/{SpareId:int}"

@if (Spare != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem=SpareModel Data=@Spare
                                      Submit=@ValidTaskSubmitted OnInvalidSubmit=@InvalidTaskSubmitted>
        <div class="row">
            <div class="col-sm-2">
                <div class="form-group neg-margin-small">
                    <label>Id:</label>
                    <label class="form-control neg-margin-small">@Spare.Id</label>
                </div>
            </div>
            <div class="col-sm-10">
                <AMproverTextBox @bind-Value="Spare.Name" Label=@Localizer["SeSpareLbl"] Required=true MaxLength="60" />
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <AMproverTextArea @bind-Value=@Spare.Remarks Cols="30" Rows="3" Label=@Localizer["SeRemarksLbl"] />
            </div>
        </div>
        <RadzenTabs>
            <Tabs>
                <RadzenTabsItem Text=@Localizer["SeDescriptionTab"]>
                    <div class="row">
                        <div class="col-sm-4">
                            <AMproverNumberInput Name="ObjectsInstalled"
                                                 Label=@Localizer["SeObjectsInstalledLbl"] TValue="int?"
                                                 @bind-Value="Spare.ObjectCount" />
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput Name="OrderLeadTime"
                                                 Label=@Localizer["SeOrderLeadTimeLbl"] TValue="decimal?"
                                                 @bind-Value="Spare.OrderLeadTime" />
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput Name="Reliability" Min=10 Max=99.99999m
                                                 Label=@Localizer["SeReliabilityLbl"] Format="p5" TValue="decimal?"
                                                 @bind-Value="Spare.Reliability" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <RadzenButton Text=@Localizer["SeCalculateBtn"] Click=@CalculateSpareParts Disabled=@(CalculateSparePartsBtnDisabled || !GlobalDataService.CanEdit) />
                            @if (CalculateSparePartsBtnDisabled || !GlobalDataService.CanEdit)
                            {
                                <small>@Localizer["SeCalcExplanationTxt"]</small>
                            }
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-1"></div>
                        <div class="col-sm-10 bordered-container">
                            <div class="row">
                                <div class="col-sm-4">
                                    <AMproverNumberInput Name="PurchasePrice" Change=@CalculateSpareCosts Min=0 Max=int.MaxValue
                                                         Label=@Localizer["SePurchasePriceLbl"] Format="c0" TValue="decimal?"
                                                         @bind-Value="Spare.PurchasePrice" Required=true />
                                </div>
                                <div class="col-sm-4">
                                    <AMproverNumberInput Name="NoOfItems" Change=@CalculateSpareCosts TValue="int?" Min=0 Max=int.MaxValue
                                                         Label=@Localizer["SeNoOfSparesLbl"]
                                                         @bind-Value="Spare.NoOfItems" Required=true />
                                </div>
                                <div class="col-sm-4">
                                    <AMproverNumberInput ReadOnly="true"
                                                         Label=@Localizer["SeTotalCostLbl"] Format="c0" TValue="decimal?"
                                                         @bind-Value="Spare.Costs" class="form-control" />
                                </div>
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@Localizer["SeDetailsTab"]>
                    <div class="row">
                        <div class="col-sm-4">
                            <AMproverNumberInput TValue="int?" @bind-Value="Spare.PurchaseYear" Label=@Localizer["SeFirstYearLbl"] />
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput Label=@Localizer["SeYearlyCostLbl"]
                                                 Format="c0" TValue="decimal?" @bind-Value="Spare.YearlyCost" class="form-control" />
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput Label=@Localizer["SeDepreciationPctLbl"] TValue="decimal?"
                                                 Format="P2" @bind-Value="Spare.DepreciationPct" class="form-control" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4">
                            <AMproverNumberInput Label=@Localizer["SeStockNumberLbl"] TValue="int?"
                                                 @bind-Value="Spare.StockNumber" class="form-control" />
                        </div>
                        <div class="col-sm-4">
                            <AMproverTextBox @bind-Value="Spare.Category" Label=@Localizer["SeCategoryLbl"] MaxLength="50" />
                        </div>
                        <div class="col-sm-4">
                            <AMproverTextBox @bind-Value="Spare.SupplierId" Label=@Localizer["SeSupplierLbl"] MaxLength="50" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <AMproverTextBox @bind-Value="Spare.VendorId" Label=@Localizer["SeVendorLbl"] MaxLength="50" />
                        </div>
                        <div class="col-sm-6">
                            <AMproverTextBox @bind-Value="Spare.ReferenceId" Label=@Localizer["SeReferenceIdLbl"] MaxLength="50" />
                        </div>
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>

        <br />
        <RadzenButton type="submit" class="btn btn-primary" Text=@Localizer["SeSaveBtn"] Disabled=!GlobalDataService.CanEdit />
    </Radzen.Blazor.RadzenTemplateForm>
}
