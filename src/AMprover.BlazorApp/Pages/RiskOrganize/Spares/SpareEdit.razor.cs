﻿using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Threading.Tasks;
using AMprover.BlazorApp.Enums;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Spares;

public partial class SpareEdit
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private ISparePartManager SparePartManager { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private ILogger<SpareEdit> Logger { get; set; }
    [Inject] private IStringLocalizer<SpareEdit> Localizer { get; set; }
    [Inject] private IGlobalDataService GlobalDataService { get; set; }

    [Parameter] public int SpareId { get; set; }
    [Parameter] public int RiskId { get; set; }
    [Parameter] public bool Pmo { get; set; }
    [Parameter] public EventCallback<SpareModel> Callback { get; set; }

    public SpareModel Spare { get; set; }
    public string ErrorText { get; set; }
    private LookupSettingModel DefaultDepreciationPct { get; set; }
    private LookupSettingModel DefaultSpareManagementPct { get; set; }
    private LookupSettingModel DefaultModificationPct { get; set; }

    private EntityEditorMode EditorMode
    {
        get
        {
            return SpareId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    protected override void OnInitialized()
    {
        switch (EditorMode)
        {
            case EntityEditorMode.Create:
                Spare = new SpareModel { Id = SpareId, Pmo = Pmo };
                break;
            default:
                Spare = SparePartManager.GetSpareById(SpareId);
                break;
        }

        DefaultDepreciationPct = LookupManager.GetLookupSettingByPropertyName("DepreciationPct");
        DefaultSpareManagementPct = LookupManager.GetLookupSettingByPropertyName("SpareManPct");
        DefaultModificationPct = LookupManager.GetLookupSettingByPropertyName("ModificationPct");
    }

    private void CalculateSpareParts()
    {
        var risk = RiskAnalysisManager.GetRisk(Spare.MrbId > 0 ? Spare.MrbId : RiskId);
           
        Spare.CalculateSparePartCount(risk);
        CalculateSpareCosts();
            
        //Does task and spare parts calculation
        risk.CalculatePreventiveCosts(DefaultDepreciationPct.DecimalValue, DefaultSpareManagementPct.DecimalValue,
            DefaultModificationPct.DecimalValue, true);
            
        RiskAnalysisManager.UpdateRisk(risk);
    }

    private void CalculateSpareCosts()
    {
        Spare.CalculateSparePartCost();
        Spare = SparePartManager.UpdateSpare(Spare);
    }

    private bool CalculateSparePartsBtnDisabled => Spare.ObjectCount == null || Spare.OrderLeadTime == null || Spare.Reliability == null;

    private async Task ValidTaskSubmitted(SpareModel spare)
    {
        await Callback.InvokeAsync(spare);
        DialogService.Close();
    }

    private void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ErrorText = "Update NOT executed, invalid form submitted";
        Logger.LogWarning($"Invalid {EditorMode} form submitted for {nameof(Spare)} with Id {SpareId}.");
        Logger.LogWarning(Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }
}