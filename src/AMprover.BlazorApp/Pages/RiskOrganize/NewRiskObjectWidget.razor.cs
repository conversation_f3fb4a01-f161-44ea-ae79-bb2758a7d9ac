using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Collections.Generic;
using System.Linq;

namespace AMprover.BlazorApp.Pages.RiskOrganize;

public partial class NewRiskObjectWidget
{
    [Inject] private ILogger<NewRiskObjectWidget> Logger { get; set; }
    [Inject] private IScenarioManager ScenarioManager { get; set; }
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private IRiskOrganizerManager RiskOrganizerManager { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; }

    [Parameter] public int? SelectedScenario { get; set; }
    [Parameter] public EventCallback<RiskObjectModel> SaveCallBack { get; set; }

    private List<ScenarioModel> Scenarios { get; set; }
    private List<RiskMatrixTemplateModel> Templates { get; set; } = [];
    private List<ObjectModel> Objects { get; set; } = [];
    private List<string> AnalysisTypes { get; set; } = [];
    private Dictionary<ObjectLevel, string> ObjectLevels { get; set; }
    private NewRiskObjectSettings NewRiskObjectSettings { get; set; } = new();

    protected override void OnInitialized()
    {
        Scenarios = ScenarioManager.GetAllScenarios();
        Objects = RiskAnalysisManager.GetAllObjects();
        Templates = RiskAnalysisManager.GetAllTemplates();
        AnalysisTypes = RiskOrganizerManager.GetAnalysisTypes();
        ObjectLevels = ObjectManager.GetObjectLevelNames();

        NewRiskObjectSettings.ScenarioId = SelectedScenario ?? 0;
        GenerateRiskObjectName();
    }

    private void ValidTaskSubmitted(NewRiskObjectSettings settings)
    {
        var newRiskObject = RiskOrganizerManager.CreateRiskObject(settings);

        if (SaveCallBack.HasDelegate)
            SaveCallBack.InvokeAsync(newRiskObject);
    }

    private void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        Logger.LogError("Invalid form submitted in {NewRiskObjectWidgetName} with model: {JsonModel}",
            nameof(NewRiskObjectWidget),
            Newtonsoft.Json.JsonConvert.SerializeObject(args));
    }

    private string GetValidatorMessage(ObjectLevel objectLevel)
    {
        var name = ObjectLevels.TryGetValue(objectLevel, out var level) ? level : objectLevel.ToString();
        return $"Must Choose {name}";
    }

    private string GetObjectLevel(ObjectLevel objectLevel)
    {
        return ObjectLevels.TryGetValue(objectLevel, out var level) ? level : objectLevel.ToString();
    }

    private void GenerateRiskObjectName()
    {
        var analysisType = string.IsNullOrWhiteSpace(NewRiskObjectSettings.AnalyseType) ? "_" : NewRiskObjectSettings.AnalyseType;
        var fmecaShortKey = Templates.FirstOrDefault(x => x.Id == NewRiskObjectSettings.FmecaId)?.ShortName ?? "_";
        var installationName = Objects.FirstOrDefault(x => x.Id == NewRiskObjectSettings.InstallationId)?.Name ?? "_";

        NewRiskObjectSettings.Name = $"{analysisType} - {fmecaShortKey} - {installationName}";
    }
}