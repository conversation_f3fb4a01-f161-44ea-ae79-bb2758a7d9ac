using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;

namespace AMprover.BlazorApp.Pages.RiskOrganize;

public partial class DeleteRiskObjectWidget
{
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IRiskOrganizerManager RiskOrganizerManager { get; set; }

    [Parameter] public RiskObjectModel RiskObject { get; set; }
    [Parameter] public int Risks { get; set; }

    private string ConfirmText { get; set; } = string.Empty;
    private string ErrorMessage { get; set; }

    private void ConfirmDelete()
    {
        if (ConfirmText.Replace(" ", "") != RiskObject.Name.Replace(" ", ""))
            ErrorMessage = "The name is incorrect";
        else
        {
            RiskOrganizerManager.DeleteRiskObject(RiskObject.Id);
            NavigationManager.NavigateTo(NavigationManager.Uri.Replace($"riskobject={RiskObject.Id}", string.Empty), true);
        }
    }
}