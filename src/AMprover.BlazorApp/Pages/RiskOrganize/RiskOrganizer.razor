﻿@page "/value-risk-organizer"

<div class="row">
    <div class="col-6">
        <h2>@($"{Localizer["RoHeaderTxt"]}") @(SelectedScenarios.Any() ? $"{SelectedScenarios.Count}/{Scenarios.Count}" : string.Empty)</h2>
    </div>
</div>

<div class="row header-navigation">
    <div class="col-5">
        <Breadcrumbs Category="Value Risk Organizer"/>
    </div>
    <div class="col-7 text-right">

        <RadzenSplitButton Disabled=!GlobalDataService.CanEdit Icon="help_outline" ButtonStyle="ButtonStyle.Success" Click=@(args => MultiLineInfoClick(args)) class="float-right my-2 mr-1 ml-2">
            <ChildContent>

                <RadzenSplitButtonItem Text=@Localizer["RoHeaderTxt"] Value="1" />
                <RadzenSplitButtonItem Text=@Localizer["RoExportBtnTxt"] Value="2" />
                <RadzenSplitButtonItem Text=@Localizer["RoImportRisksBtn"] Value="3" />
                <RadzenSplitButtonItem Text=@Localizer["RoImportTasksBtn"] Value="4" />
                <RadzenSplitButtonItem Text=@Localizer["RoImportSpareBtn"] Value="5" />

            </ChildContent>
        </RadzenSplitButton>
        <RadzenSplitButton Disabled=!GlobalDataService.CanEdit Icon="input" Text=@Localizer["RoImportBtn"] Click=@(args => OnRiskObjectImportClick(args)) ButtonStyle=ButtonStyle.Secondary class="float-right my-2 mx-0">
            <ChildContent>

                <RadzenSplitButtonItem Text=@Localizer["RoImportRisksBtn"] Value="1" />
                <RadzenSplitButtonItem Text=@Localizer["RoImportTasksBtn"] Value="2" />
                <RadzenSplitButtonItem Text=@Localizer["RoImportSpareBtn"] Value="3" />

            </ChildContent>
        </RadzenSplitButton>

        <RadzenButton Disabled=!GlobalDataService.CanEdit Icon="launch" Text=@(Localizer["RoExportBtnTxt"]) class="float-right my-2 mx-2" Click=@ExportRiskObject/>

        @if (ShowRegenerateImagesBtn && SelectedRiskObject != null)
        {
        <RadzenButton Disabled=!GlobalDataService.CanEdit Text=@(Localizer["RoRegenerateImagesLbl"]) class="float-right my-2 mx-1" Click=@GenerateFmecaImagePopup />
        }
    </div>
</div>

<div class="row">

    <div class="col-xl-2 col-3">

        <!-- Scenario ListBox-->
        <div class="row">
            <div class="col-12">
                <label>@(Localizer["RoScenarioLbl"])</label>
                <RadzenListBox class="riskorganizer-scenario-listbox" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                               @bind-Value=QueryParams.Scenarios Multiple="true" Data=@Scenarios
                               TextProperty="Name" ValueProperty="Id"
                               Change=@(args => UpdateSelectedScenarios((IEnumerable<int>)args, true))/>
            </div>
        </div>

        <!-- Collections ListBox-->
        <div class="row">
            <div class="col-12">
                <label>@GetObjectLevel(ObjectLevel.Collection)</label>
                <RadzenListBox class="riskorganizer-scenario-listbox" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                               @bind-Value=Categories Multiple="true" Data=@Collections
                               TextProperty="Name" ValueProperty="Id" />

            </div>
        </div>
    </div>

    <div class="col-xl-10 col-9">
        <div class="row riskorganizer-tabs-top overflow-auto">
            <div class="col-12">
                <RadzenTabs @ref="TabsComponent" @bind-SelectedIndex="@SelectedTabIndex" Change=@TopTabChange class="h-100">
                    <Tabs>
                        <!-- Scenario Editor -->
                        <RadzenTabsItem Text=@(Localizer["RoScenarioLbl"])>
                            @if (SelectedScenario != null)
                            {
                            <div class="riskorganizer-tab-content mb-3">
                                <div class="row">
                                    <div class="col-9">
                                        <div class="row">
                                            <div class="col-2 col-lg-1">
                                                <div class="form-group neg-margin-small">
                                                    <label>@(Localizer["RoScenarioLbl"]):</label><br/>
                                                    <div class="btn-group neg-margin-small" role="group">
                                                        <RadzenButton Click=PreviousScenario class="btn btn-secondary px-2 px-xl-3" Text="<" Disabled=@(SelectedScenarios.Count <= 1)/>
                                                        <RadzenButton Click=NextScenario class="btn btn-secondary px-2 px-xl-3" Text=">" Disabled=@(SelectedScenarios.Count <= 1)/>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-3 col-lg-2 neg-margin-small">
                                                <label>Id:</label>
                                                <RadzenNumeric Disabled="true" class="form-control neg-margin-small" @bind-Value=@SelectedScenario.Id/>
                                            </div>
                                            <div class="col-3 col-lg-2">
                                                <AMproverTextBox Label="ShortKey" MaxLength="4" @bind-Value=@SelectedScenario.ShortKey Change=@UpdateCurrentScenario/>
                                            </div>
                                            <div class="col-4 col-lg-7">
                                                <AMproverTextBox Label=@(Localizer["RoNameLbl"]) @bind-Value=@SelectedScenario.Name Change=@UpdateCurrentScenario/>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                <AMproverTextBox Label=@(Localizer["RoDescriptionLbl"]) @bind-Value=@SelectedScenario.Description Change=@UpdateCurrentScenario/>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                <AMproverTextArea Rows="5" Label=@(Localizer["RoPreReqLbl"]) @bind-Value=@SelectedScenario.Prerequisites Change=@UpdateCurrentScenario/>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <AMproverTextBox Disabled="true" Label=@(Localizer["RoChildTypeLbl"]) Value=@Strings.NotAvailable/>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                <AMDropdown Data=@StatusDict.ToNullableDictionary()
                                                            @bind-Value=@SelectedScenario.Status
                                                            Change=@UpdateCurrentScenario
                                                            AllowClear="true"
                                                            Label=@(Localizer["RoStatusLbl"])/>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                <AMproverTextArea Disabled=true Label=@(Localizer["RoMetadataLbl"]) Value=@(SelectedScenario.Metadata?.ToString() ?? Strings.NotAvailable)/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            }
                            else
                            {
                            <div class="col-12 my-4">@(Localizer["RoInstructionTxt"])</div>
                            }
                        </RadzenTabsItem>

                        <!-- Risk Object -->
                        <RadzenTabsItem Text=@(Localizer["RoValueRiskTxt"]) Disabled=@(SelectedRiskObject == null)>
                            <div class="riskorganizer-tab-content mb-3">

                                <div class="row">
                                    <div class="col-8">

                                        <div class="row">
                                            <div class="col-1">
                                                <AMproverTextBox Disabled="true" Label="Id" Value=@SelectedRiskObject.Id.ToString()/>
                                            </div>

                                            <div class="col-4 col-xl-5">
                                                <AMproverTextBox @bind-Value=@SelectedRiskObject.Name
                                                                 Label=@(Localizer["RoDescriptionLbl"])
                                                                 MaxLength="50"
                                                                 Change=@(() => UpdateCurrentRiskObject())/>

                                            </div>
                                            <div class="col-4">
                                                <AMDropdown AllowFiltering="true"
                                                            Data=@Scenarios.OrderBy(x => x.Name).ToDictionary(x => x.Id, x => x.Name)
                                                            @bind-Value=@SelectedRiskObject.ScenarioId
                                                            Change=@(() => UpdateScenarioOnRiskObject())
                                                            Label=@(Localizer["RoScenarioLbl"])/>
                                            </div>
                                            <div class="col-2">
                                                <AMDropdown AllowFiltering="true"
                                                            Data=@AnalysisTypes.ToDictionary(x => x, x => x)
                                                            @bind-Value=@SelectedRiskObject.AnalysisType
                                                            Change=@(() => UpdateCurrentRiskObject())
                                                            Label=@(Localizer["RoAnalysisTypeLbl"])/>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-4 neg-margin-small">
                                                <AMDropdown AllowFiltering="true"
                                                            Data=@Templates.OrderBy(x => x.Name).ToDictionary(x => x.Id, x => x.Name)
                                                            @bind-Value=@SelectedRiskObject.FmecaId
                                                            Change=@(() => UpdateCurrentRiskObject())
                                                            Label=@(Localizer["RoValueRiskMatrixLbl"])/>
                                            </div>
                                            <div class="col-2 neg-margin-small">
                                                <AMproverNumberInput TValue="decimal" @bind-Value=@SelectedRiskObject.DownTimeCost
                                                                     Format=C0
                                                                     Min="0"
                                                                     Label=@Localizer["RoDownTimeCostLbl"]
                                                                     Change=@(() => UpdateCurrentRiskObject())  />
                                            </div>
                                            <div class="col-4 neg-margin-small">
                                                <AMDropdown Data=@DepartmentDict.ToNullableDictionary()
                                                            @bind-Value=@SelectedRiskObject.DepartmentId
                                                            Change=@(() => UpdateCurrentRiskObject())
                                                            AllowClear="true"
                                                            Readonly=@(!IsAdminUser || IsAssetManagementUser)
                                                            Label=@(Localizer["RoDepartmentLbl"])/>
                                            </div>
                                            <div class="col-2 neg-margin-small">
                                                <AMDropdown AllowClear="true"
                                                            Data=@StatusDict.ToNullableDictionary()
                                                            @bind-Value=@SelectedRiskObject.Status
                                                            Change=@(() => UpdateCurrentRiskObject())
                                                            Label=@(Localizer["RoStatusLbl"])/>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-6 neg-margin-small">
                                                <div class="form-group">
                                                    <AMDropdown AllowClear="true" AllowFiltering="true"
                                                                Data=@Objects.Where(x => x.Level == 0).OrderBy(x => x.Name).ToDictionary(x => (int?)x.Id, x => x.Name)
                                                                @bind-Value=@SelectedRiskObject.ParentObjectId
                                                                Change=@(() => UpdateCollectionOrIntallationOnRiskObject())
                                                                Label=@GetObjectLevel(ObjectLevel.Collection)/>
                                                </div>
                                            </div>
                                            <div class="col-6 neg-margin-small">
                                                <div class="form-group">
                                                    <AMDropdown AllowFiltering="true"
                                                                Data=@Objects.Where(x => x.Level == 1).OrderBy(x => x.Name).ToDictionary(x => x.Id, x => x.Name)
                                                                @bind-Value=@SelectedRiskObject.ObjectId
                                                                Change=@(() => UpdateCollectionOrIntallationOnRiskObject())
                                                                Label=@GetObjectLevel(ObjectLevel.Installation)/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Saving Gauge -->
                                    <div class="col-4">
                                        <div class="row ro-graph-background">
                                            @if (SelectedRiskObject.AnalysisType != "PMO")
                                            {
                                            <RadzenRadialGauge class="w-100 ro-gauge">
                                                <RadzenRadialGaugeScale StartAngle="-105" EndAngle="105" Step="15" Min="0" Max="105" TickPosition="GaugeTickPosition.Inside">
                                                    <RadzenRadialGaugeScalePointer Value=@((double) Math.Min(105, (SelectedRiskObject.LccPotential ?? 105))) Length="0.8" ShowValue="true">
                                                        <Template Context="pointer">
                                                            <p class="ro-text-gauge text-center">
                                                                Optimization Potential<br />
                                                                @(SelectedRiskObject?.LccPotential?.ToString() ?? Strings.NotAvailable) % (LCC)
                                                            </p>
                                                        </Template>
                                                    </RadzenRadialGaugeScalePointer>
                                                    <RadzenRadialGaugeScaleRange From="0" To="30" Fill="green" />
                                                    <RadzenRadialGaugeScaleRange From="30" To="60" Fill="orange" />
                                                    <RadzenRadialGaugeScaleRange From="60" To="105" Fill="red" />
                                                </RadzenRadialGaugeScale>
                                            </RadzenRadialGauge>
                                            }
                                            else
                                            {
                                            <RadzenChart class="w-100 ro-stackedbar">
                                                <RadzenStackedColumnSeries Data="@ChartDataActions" Fill="green" CategoryProperty="RiskType" ValueProperty="Cost">
                                                    <TooltipTemplate Context="data">
                                                        <div>
                                                            <span>@Localizer["RoActionCosts"]</span> = <strong>@data.CostString</strong>
                                                        </div>
                                                    </TooltipTemplate>
                                                </RadzenStackedColumnSeries>
                                                <RadzenStackedColumnSeries Data="@ChartDataDirectRiskCost" Fill="firebrick" CategoryProperty="RiskType" ValueProperty="Cost">
                                                    <TooltipTemplate Context="data">
                                                        <div>
                                                            <span>@Localizer["RoDirectRiskCosts"]</span> = <strong>@data.CostString</strong>
                                                        </div>
                                                    </TooltipTemplate>
                                                </RadzenStackedColumnSeries><RadzenStackedColumnSeries Data="@ChartDataValueRisk" Fill="red" CategoryProperty="RiskType" ValueProperty="Cost">
                                                    <TooltipTemplate Context="data">
                                                        <div>
                                                            <span>@Localizer["RoValueRiskCosts"]</span> = <strong>@data.CostString</strong>
                                                        </div>
                                                    </TooltipTemplate>
                                                </RadzenStackedColumnSeries>
                                                <RadzenColumnOptions Radius="5" />
                                                <RadzenValueAxis Formatter="@FormatAsEur">
                                                    <RadzenGridLines Visible="true" />
                                                    <RadzenAxisTitle Text=@(Localizer["RoPmoGraphLbl"]) />
                                                </RadzenValueAxis>
                                                <RadzenLegend Visible="false" />
                                            </RadzenChart>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="row">

                                    <div class="col-4 neg-margin-small">
                                        <AMproverTextArea @bind-Value=@SelectedRiskObject.MrbstartPoint
                                                          Change=@(() => UpdateCurrentRiskObject()) Rows="3"
                                                          Label=@(Localizer["RoPreReqLbl"])/>
                                    </div>

                                    <div class="col-4 neg-margin-small">
                                        <AMproverTextArea @bind-Value=@SelectedRiskObject.Object.Function
                                                          Change=@(() => UpdateCurrentAsset()) Rows="3"
                                                          Label=@(Localizer["RoFunctionLbl"])/>
                                    </div>

                                    <div class="col-4 neg-margin-small">
                                        <AMproverTextArea @bind-Value=@SelectedRiskObject.AmrbAttended
                                                          Change=@(() => UpdateCurrentRiskObject()) Rows="3"
                                                          Label=@(Localizer["RoAttendedByLbl"])/>
                                    </div>
                                </div>
                            </div>
                        </RadzenTabsItem>
                    </Tabs>
                </RadzenTabs>
            </div>
        </div>

        <div class="row riskorganizer-tabs-bottom">
            <div class="col-12 neg-margin">
                <RadzenTabs @ref="GridTabsComponent" @bind-SelectedIndex=@SelectedBottomTabIndex Change=@BottomTabChange class="h-100">
                    <Tabs>
                        <!-- Risk Objects -->
                        <RadzenTabsItem Text=@($"{Localizer["RoValueRisksTxt"]} ({SelectedRiskObjects?.Count ?? 0})")>
                            <div class="riskorganizer-tabs-content-bottom">
                                <!-- Risk Object Grid -->
                                <UtilityGrid TItem=RiskObjectModel
                                             @ref=RiskObjectGrid
                                             Data=SelectedRiskObjects
                                             FileName=@GridNames.RiskOrganizer.RiskObjects
                                             MaxRows=5
                                             UseOpenTextInsteadOfEdit=true
                                             Interactive=true
                                             AllowXlsExport=true
                                             AllowFiltering=true
                                             DeleteAreYouSurePopup=false
                                             AddRowCallBack=@(_ => OpenNewRiskObjectWidget())
                                             OpenLccCallback=@(args => OpenLccPage(args))
                                             DeleteCallback=@(args => DeleteRiskObject(args))
                                             EditCallBackOverride=@(args => OpenRisksPage(args))
                                             PasteCallBack=@(args => PasteRiskObject(args.Item1))
                                             RowSelectCallBack=@(args => ClickRiskObjectRow((RiskObjectModel) args))
                                             ContextMenuCondition=@(row => ShouldShowOpenPage(row))
                                             DropDownOverrides=@RiskObjectDropdownOverrides
                                             CssClass="riskorganizer-tabs-content-bottom overflow-hidden"/>
                            </div>
                        </RadzenTabsItem>

                        <!-- Risks -->
                        <RadzenTabsItem Text=@GetRiskTabText() Disabled=@(!GetRisks().Any())>
                            <UtilityGrid TItem=RiskModel
                                         @ref=RisksGrid
                                         Data=GetRisks()
                                         FileName=@GridNames.RiskOrganizer.Risks
                                         MaxRows=5
                                         Interactive=true
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         OpenPageCallback=@(args => OpenRisksPage(args))
                                         DropDownOverrides=@RiskDropDownOverrides
                                         SaveCallback=@UpdateRisk
                                         EditableFields=@(new() {nameof(RiskModel.RiskObjectId)})
                                         DefaultContextMenuAction=ContextMenuItemType.OpenPage
                                         CssClass="riskorganizer-tabs-content-bottom overflow-hidden"/>
                        </RadzenTabsItem>

                        <!-- PMO actions (tasks) -->
                        <RadzenTabsItem Text=@GetPmoActionsTabText() Visible=@(GetPmoStatus()) Disabled=@(!GetPmoTasks().Any())>
                            <UtilityGrid TItem=TaskModel
                                         @ref=PreventiveActionsGrid
                                         Data=GetPmoTasks()
                                         FileName=@GridNames.RiskOrganizer.PreventiveActions
                                         MaxRows=5
                                         UseOpenTextInsteadOfEdit=true
                                         Interactive=true
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         EditCallBackOverride=@(args => OpenRisksPage(args))
                                         DropDownOverrides=@TaskDropDownOverrides
                                         CssClass="riskorganizer-tabs-content-bottom overflow-hidden"/>
                        </RadzenTabsItem>

                        <!-- Preventive actions (tasks) -->
                        <RadzenTabsItem Text=@GetPreventiveActionsTabText() Disabled=@(!GetTasks().Any())>
                            <UtilityGrid TItem=TaskModel
                                         @ref=PreventiveActionsGrid
                                         Data=GetTasks()
                                         FileName=@GridNames.RiskOrganizer.PreventiveActions
                                         MaxRows=5
                                         UseOpenTextInsteadOfEdit=true
                                         Interactive=true
                                         AllowXlsExport=true
                                         AllowFiltering=true
                                         EditCallBackOverride=@(args => OpenRisksPage(args))
                                         DropDownOverrides=@TaskDropDownOverrides
                                         CssClass="riskorganizer-tabs-content-bottom overflow-hidden"/>
                        </RadzenTabsItem>
                    </Tabs>
                </RadzenTabs>
            </div>
        </div>
    </div>
</div>
