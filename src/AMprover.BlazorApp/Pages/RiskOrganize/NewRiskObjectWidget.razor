﻿@page "/new-risk-object"
@inject IStringLocalizer<NewRiskObjectWidget> _localizer

<Radzen.Blazor.RadzenTemplateForm TItem=NewRiskObjectSettings
                                  Data=@NewRiskObjectSettings
                                  Submit=@ValidTaskSubmitted
                                  OnInvalidSubmit=@InvalidTaskSubmitted>

    <div class="form-group">@_localizer["NRoHeaderTxt"]</div>

    <div class="form-group">
        <label>@_localizer["NRoNameTxt"]:</label>
        <RadzenTextBox @bind-Value=@NewRiskObjectSettings.Name Name="Name" Disabled=true MaxLength="50" class="form-control"/>
        <RadzenRequiredValidator Component="Name" />
    </div>

    <div class="form-group">
        <label>@_localizer["NRoScenarioTxt"]:</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        TextProperty="Name" ValueProperty="Id" Name="ScenarioId" class="form-control"
                        Data="@Scenarios"
                        @bind-Value="@NewRiskObjectSettings.ScenarioId" />
        <RadzenNumericRangeValidator Component="ScenarioId" Min="1" Text="Must Choose Scenario" />
    </div>

    <div class="form-group">
        <label>@GetObjectLevel(ObjectLevel.Collection):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        TextProperty="Name" ValueProperty="Id" Name="CollectionId" class="form-control"
                        Data="@Objects.Where(x => x.Level == (int)ObjectLevel.Collection)"
                        @bind-Value="@NewRiskObjectSettings.CollectionId" />
    </div>

    <div class="form-group">
        <label>@GetObjectLevel(ObjectLevel.Installation):</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        TextProperty="Name" ValueProperty="Id" Name="InstallationId" class="form-control"
                        Data="@Objects.Where(x => x.Level == (int)ObjectLevel.Installation)"
                        @bind-Value="@NewRiskObjectSettings.InstallationId" 
                        Change=@GenerateRiskObjectName />
        <RadzenNumericRangeValidator Component="InstallationId" Min="1" Text=@GetValidatorMessage(ObjectLevel.Installation) />
    </div>

    <div class="form-group">
        <label>@_localizer["NRoRiskMatrixTxt"]:</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        TextProperty="Name" ValueProperty="Id" Name="FmecaId" class="form-control"
                        Data="@Templates"
                        @bind-Value="@NewRiskObjectSettings.FmecaId" 
                        Change=@GenerateRiskObjectName />
        <RadzenNumericRangeValidator Component="FmecaId" Min="1" Text="Must Choose Fmeca" />
    </div>

    <div class="form-group">
        <label>@_localizer["NRoAnalyseTypeTxt"]:</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        Name="AnalysisType" class="form-control"
                        Data="@AnalysisTypes"
                        @bind-Value="@NewRiskObjectSettings.AnalyseType" 
                        Change=@GenerateRiskObjectName />
        <RadzenLengthValidator Component="AnalysisType" Min="1" Text="Must Choose AnalysisType" />
    </div>

    <input type="submit" class="btn btn-primary mt-4" value=@_localizer["NRoCreateBtn"] />
</Radzen.Blazor.RadzenTemplateForm>