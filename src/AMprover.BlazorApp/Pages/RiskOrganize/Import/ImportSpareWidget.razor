﻿@using AMprover.BusinessLogic.Models.Import;

<div class="row">
    <div class="col-12">

        @if (FileAnalyzed)
        {
            <div class=@SpareImportAnalysis.GetAnalyzeStatus()>
                Importing: '@SpareImportAnalysis.FileName' <br />
                onto RiskObject '@SpareImportAnalysis.RiskObjectName' <br />

                <br />
                @(FoundSparesInImport() ? "✔" : "❌") Spares in Import: @SpareImportAnalysis.SparesInImport <br />
                @(FoundRiskInImport() ? "✔" : "❌") Risk Id's in Import: @SpareImportAnalysis.RisksInImport <br />

                @if (FoundSparesInImport() && FoundRiskInImport())
                {
                    <span>
                        @(RisksInDbMatchImport() ? "✔" : "❌") Risks found in Database: @SpareImportAnalysis.RisksFoundInDB
                        @if (RisksInDbMatchImport())
                        {
                            <br />
                            <span>
                                @(AddPlusUpdateMatchesTasksInImport() ? "✔" : "❌") Spares that will update: @SpareImportAnalysis.SparesToUpdate <br />
                                @(AddPlusUpdateMatchesTasksInImport() ? "✔" : "❌") Spares that will be added: @SpareImportAnalysis.SparesToAdd <br />
                            </span>
                        }
                    </span>
                }
            </div>
        }

        <div class="form-group py-2">
            @GetText()
        </div>

        @if (Status == ImportSpareStatus.SelectFile || !SpareImportAnalysis.ImportFileAllowed())
        {
            <div class="form-group">
                <AMDropdown @bind-Value=@RiskObjectId
                            Data=@RiskObjects
                            Required=true />
            </div>

            <div class="form-group pb-2">
                <RadzenFileInput @ref=@UploadRef
                            @bind-Value=@FileUpload
                            Accept=".xls, .xlsx"
                            TValue="string"
                            ChooseText=@Localizer["RoImportRiskSelectFile"]
                            Change=@ChangeFile />
            </div>

            <div class="form-group pb-2">
                <AMproverCheckbox @bind-Value=@UseImportId Label="Use Import Id" />
                <div class="alert alert-info" role="alert">@Localizer["RoImportSparesUseImportId"]</div>
            </div>

            <div class="form-group">
                <RadzenButton Click=@AnalyzeImportFile
                        Text=@Localizer["RoImportAnalyzeFile"]
                        Disabled=@(string.IsNullOrWhiteSpace(FileUpload)) />
            </div>
        }
        else if (Status == ImportSpareStatus.ImportSpares)
        {
            <RadzenButton Click=@PreviousStatus
                      Text=@Localizer["RoImportPrevious"] />

            <RadzenButton Click=@ImportSpares
                      Text=@Localizer["RoImportRiskStartImport"] />
        }

        @if(ShowError())
        {
            <div class="form-group pt-3">
                <div class="alert alert-danger" role="alert">
                    @ImportResult.ErrorMessage
                </div>
            </div>
        }

        @if (ShowSuccess())
        {
            <div class="form-group pt-3">
                <div class="alert alert-success" role="alert">
                    Succesfully added @(ImportResult.ItemsAdded) items
                    @if (ImportResult.ItemsUpdated > 0)
                    {
                        <br />
                        <span>Succesfully Updated @(ImportResult.ItemsUpdated) items</span>
                    }
                </div>
            </div>
        }

    </div>
</div>