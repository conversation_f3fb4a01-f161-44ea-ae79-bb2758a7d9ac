﻿@using AMprover.BusinessLogic.Models.Import
<div class="row">
    <div class="col-12">

        @if (FileAnalyzed)
        {
            <div class=@TaskImportAnalysis.GetAnalyzeStatus() >
                Importing: '@TaskImportAnalysis.FileName' <br/>
                onto RiskObject '@TaskImportAnalysis.RiskObjectName' <br/>

                <br/>
                @(FoundTaskInImport() ? "✔" : "❌") Tasks in Import: @TaskImportAnalysis.TasksInImport <br />
                @(FoundRiskInImport() ? "✔" : "❌") Risk Id's in Import: @TaskImportAnalysis.RisksInImport <br />

                @if (FoundTaskInImport() && FoundRiskInImport())
                {
                    <span>
                        @(RisksInDbMatchImport() ? "✔" : "❌") Risks found in Database: @TaskImportAnalysis.RisksFoundInDB
                        @if (RisksInDbMatchImport())
                        {
                            <br />
                            <span>
                                @(AddPlusUpdateMatchesTasksInImport() ? "✔" : "❌") Tasks that will update: @TaskImportAnalysis.TasksToUpdate <br />
                                @(AddPlusUpdateMatchesTasksInImport() ? "✔" : "❌") Tasks that will be added: @TaskImportAnalysis.TasksToAdd <br />
                            </span>
                        }
                    </span>
                }
            </div>
        }

        <div class="form-group py-2">
            @GetText()
        </div>

        @if (Status == ImportTaskStatus.SelectFile || !TaskImportAnalysis.ImportFileAllowed())
        {
            <div class="form-group">
                <AMDropdown @bind-Value=@RiskObjectId
                            Data=@RiskObjects
                            Required=true />
            </div>

            <div class="form-group pb-2">
                <RadzenFileInput @ref=@UploadRef
                            @bind-Value=@FileUpload
                            Accept=".xls, .xlsx"
                            TValue="string"
                            ChooseText=@Localizer["RoImportRiskSelectFile"]
                            Change=@ChangeFile />
            </div>

            <div class="form-group pb-2">
                <AMproverCheckbox @bind-Value=@UseImportId Label="Use Import Id" />
                <div class="alert alert-info" role="alert">@Localizer["RoImportActionUseImportId"]</div>
            </div>

            <div class="form-group">
                <RadzenButton Click=@AnalyzeImportFile
                        Text=@Localizer["RoImportAnalyzeFile"]
                        Disabled=@(string.IsNullOrWhiteSpace(FileUpload)) />
            </div>
        }
        else if (Status == ImportTaskStatus.ImportTasks)
        {
            <RadzenButton Click=@PreviousStatus
                      Text=@Localizer["RoImportPrevious"] />

            <RadzenButton Click=@ImportTasks
                      Text=@Localizer["RoImportRiskStartImport"] />
        }
        else if (Status == ImportTaskStatus.Finished)
        {

        }
        else
        {
            foreach (var replace in ReplaceOptions[Status].ReplaceValues)
            {
                <AMDropdown 
                    Label=@replace.Original
                    Value=@replace.Replaced
                    Data=@ReplaceOptions[Status].Options 
                    Change=@(args => UpdateValue(replace, (string)args))/>
            }

            <RadzenButton Click=@PreviousStatus
                      Text=@Localizer["RoImportPrevious"] />

            <RadzenButton Click=@NextStatus
                      Text=@Localizer["RoImportNext"]
                      Disabled=@(ReplaceOptions[Status].ReplaceValues.Any(x => string.IsNullOrWhiteSpace(x.Replaced))) />
        }

        @if(ShowError())
        {
            <div class="form-group pt-3">
                <div class="alert alert-danger" role="alert">
                    @ImportResult.ErrorMessage
                </div>
            </div>
        }

        @if (ShowSuccess())
        {
            <div class="form-group pt-3">
                <div class="alert alert-success" role="alert">
                    Successfully Added @(ImportResult.ItemsAdded) items
                    @if (ImportResult.ItemsUpdated > 0)
                    {
                        <br/>
                        <span>Successfully Updated @(ImportResult.ItemsUpdated) items</span>
                    }
                </div>
            </div>
        }

    </div>
</div>
