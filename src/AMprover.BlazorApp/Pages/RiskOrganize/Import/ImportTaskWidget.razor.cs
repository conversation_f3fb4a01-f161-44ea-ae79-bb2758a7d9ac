﻿using AMprover.BlazorApp.Components;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Import;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Import;

public partial class ImportTaskWidget
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private ITaskImportManager TaskImportManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private ILogger<ImportTaskWidget> Logger { get; set; }
    [Inject] private IStringLocalizer<RiskOrganizer> Localizer { get; set; }
    private RadzenFileInput<string> UploadRef { get; set; }

    private int RiskObjectId { get; set; }

    private Dictionary<int, string> RiskObjects { get; set; }

    private ImportResult ImportResult { get; set; }

    private List<TaskImportModel> ImportedTasks { get; set; }

    private ImportTaskStatus Status { get; set; }

    private AnalyzeTaskImportResult TaskImportAnalysis { get; set; }

    private Dictionary<ImportTaskStatus, ImportEditStatus> ReplaceOptions { get; set; }

    private string FileUpload { get; set; }

    private bool UseImportId { get; set; }

    protected override void OnInitialized()
    {
        RiskObjects = DropdownManager.GetRiskObjectDict();
        RiskObjectId = RiskObjects.Keys.FirstOrDefault();
        ImportResult = new ImportResult();
        Status = ImportTaskStatus.SelectFile;

        ReplaceOptions = new Dictionary<ImportTaskStatus, ImportEditStatus>
        {
            { ImportTaskStatus.UpdateInitiator, new ImportEditStatus { Options = DropdownManager.GetInitiatorDict().ToStringDict() } },
            { ImportTaskStatus.UpdateExecutor, new ImportEditStatus { Options = DropdownManager.GetExecutorDict().ToStringDict() } },
            { ImportTaskStatus.UpdateIntervalUnit, new ImportEditStatus { Options = DropdownManager.GetIntervalUnitDict().ToStringDict() } },
            { ImportTaskStatus.UpdateWorkPackage, new ImportEditStatus { Options = DropdownManager.GetWorkpackageDict().ToStringDict() } },
            { ImportTaskStatus.UpdatePolicy, new ImportEditStatus { Options = DropdownManager.GetPolicyDict().ToStringDict() } }
        };
    }

    /// <summary>
    /// Blazor's Bind-Value clears the property once you stop rendering the component, therefore we update the values like this
    /// </summary>
    private static void UpdateValue(ReplaceTextValue replaceObj, string text) => replaceObj.Replaced = text;

    private async Task AnalyzeImportFile()
    {
        Status = ImportTaskStatus.SelectFile;
        var base64Input = FileUpload?.Split(',').LastOrDefault()?.Trim();

        if (string.IsNullOrWhiteSpace(base64Input))
            return;

        ImportedTasks = base64Input.GetExcelData<TaskImportModel>(1)
            .Select(x => x.Value.TrimStringProperties())
            .Where(x => !string.IsNullOrWhiteSpace(x.Name))
            .ToList();

        ShowLoadingDialog();
        TaskImportAnalysis = await TaskImportManager.AnalyzeImportFile(ImportedTasks, RiskObjectId, UseImportId);
        TaskImportAnalysis.FileName = UploadRef.FileName;
        TaskImportAnalysis.RiskObjectName = RiskObjects[RiskObjectId];
        DialogService.Close();

        SetReplaceOptionsValues(ImportTaskStatus.UpdateInitiator);
        SetReplaceOptionsValues(ImportTaskStatus.UpdateExecutor);
        SetReplaceOptionsValues(ImportTaskStatus.UpdateIntervalUnit);
        SetReplaceOptionsValues(ImportTaskStatus.UpdatePolicy);
        SetReplaceOptionsValues(ImportTaskStatus.UpdateWorkPackage);

        NextStatus();
    }

    private void SetReplaceOptionsValues(ImportTaskStatus status) =>
        ReplaceOptions[status].ReplaceValues = TaskImportAnalysis.MissingItems(status).Select(x => new ReplaceTextValue(x)).ToList();

    private void NextStatus() => NextStatus(Status, 1);

    private void PreviousStatus() => NextStatus(Status, -1);

    private void NextStatus(ImportTaskStatus status, int step)
    {
        status = (ImportTaskStatus)((int)status + step);
        ImportResult = new();

        switch (status)
        {
            case ImportTaskStatus.Finished or ImportTaskStatus.SelectFile or ImportTaskStatus.ImportTasks:
                Status = status;
                return;

            default:
                if (TaskImportAnalysis.MissingItems(status).Any())
                {
                    Status = status;
                    return;
                }
                break;
        }

        NextStatus(status, step);
    }

    private async Task ImportTasks()
    {
        foreach (var replaceText in ReplaceOptions[ImportTaskStatus.UpdateInitiator].ReplaceValues)
            ImportedTasks.Where(x => x.Initiator == replaceText.Original).ToList().ForEach(x => x.Initiator = replaceText.Replaced);

        foreach (var replaceText in ReplaceOptions[ImportTaskStatus.UpdateExecutor].ReplaceValues)
            ImportedTasks.Where(x => x.Executor == replaceText.Original).ToList().ForEach(x => x.Executor = replaceText.Replaced);

        foreach (var replaceText in ReplaceOptions[ImportTaskStatus.UpdateIntervalUnit].ReplaceValues)
            ImportedTasks.Where(x => x.IntervalUnit == replaceText.Original).ToList().ForEach(x => x.IntervalUnit = replaceText.Replaced);

        foreach (var replaceText in ReplaceOptions[ImportTaskStatus.UpdateWorkPackage].ReplaceValues)
            ImportedTasks.Where(x => x.WorkPackage == replaceText.Original).ToList().ForEach(x => x.WorkPackage = replaceText.Replaced);

        foreach (var replaceText in ReplaceOptions[ImportTaskStatus.UpdatePolicy].ReplaceValues)
            ImportedTasks.Where(x => x.Policy == replaceText.Original).ToList().ForEach(x => x.Policy = replaceText.Replaced);

        await ImportVerifiedTasks();
    }

    private async Task ImportVerifiedTasks()
    {
        NextStatus();
        ShowLoadingDialog();
        ImportResult = await TaskImportManager.ImportTasks(ImportedTasks, RiskObjectId, UseImportId).ConfigureAwait(false);

        // this only closes the loading Dialog, not the current dialog
        DialogService.Close();

        if (ImportResult.Success)
            FileUpload = null;
    }

    private void ShowLoadingDialog()
    {
        DialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    private void ChangeFile() => ImportResult = new ImportResult();

    private bool ShowError() => !string.IsNullOrWhiteSpace(ImportResult.ErrorMessage);

    private bool ShowSuccess() => ImportResult.Success;

    // Helper properties and methods for the view
    private bool FileAnalyzed => Status != ImportTaskStatus.SelectFile && Status != ImportTaskStatus.Finished;

    private string GetText()
    {
        if (FileAnalyzed && !TaskImportAnalysis.ImportFileAllowed())
            return Localizer["RoImportTaskNotAllowed"].Value;

        var text = Localizer[GetKey()].Value;
        text = text.Replace("[type]", Localizer[$"{Status}"]);

        return text;
    }

    private string GetKey() =>
        Status switch
        {
            ImportTaskStatus.SelectFile => "RoImportTaskTxt",
            ImportTaskStatus.ImportTasks => "RoImportReadyForImport",
            ImportTaskStatus.Finished => "RoImportFinished",
            _ => "RoImportUpdateReference"
        };

    private bool FoundTaskInImport()
    {
        return TaskImportAnalysis?.TasksInImport > 0;
    }

    private bool FoundRiskInImport()
    {
        return TaskImportAnalysis?.RisksInImport > 0;
    }

    private bool RisksInDbMatchImport()
    {
        return TaskImportAnalysis.RisksFoundInDB == TaskImportAnalysis.RisksInImport;
    }

    private bool AddPlusUpdateMatchesTasksInImport()
    {
        return TaskImportAnalysis.TasksToUpdate + TaskImportAnalysis.TasksToAdd == TaskImportAnalysis.TasksInImport;
    }
}