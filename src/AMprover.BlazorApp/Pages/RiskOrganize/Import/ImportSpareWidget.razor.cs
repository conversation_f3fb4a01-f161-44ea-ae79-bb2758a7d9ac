﻿using AMprover.BlazorApp.Components;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Import;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using Radzen.Blazor;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Import;

public partial class ImportSpareWidget
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IDropdownManager _dropdownManager { get; set; }
    [Inject] private ISpareImportManager _spareImportManager { get; set; }
    [Inject] private DialogService _dialogService { get; set; }
    [Inject] private ILogger<ImportSpareWidget> Logger { get; set; }
    [Inject] private IStringLocalizer<RiskOrganizer> Localizer { get; set; }
    private RadzenFileInput<string> UploadRef { get; set; }

    private int RiskObjectId { get; set; }

    private Dictionary<int, string> RiskObjects { get; set; }

    private ImportResult ImportResult { get; set; }

    private List<SpareImportModel> ImportedSpares { get; set; }

    private ImportSpareStatus Status { get; set; }

    private AnalyzeSpareImportResult SpareImportAnalysis { get; set; }

    private Dictionary<ImportSpareStatus, ImportEditStatus> ReplaceOptions { get; set; }

    private string FileUpload { get; set; }

    private bool UseImportId { get; set; }

    protected override void OnInitialized()
    {
        RiskObjects = _dropdownManager.GetRiskObjectDict();
        RiskObjectId = RiskObjects.Keys.FirstOrDefault();
        ImportResult = new();
        Status = ImportSpareStatus.SelectFile;
    }

    private async Task AnalyzeImportFile()
    {
        Status = ImportSpareStatus.SelectFile;
        var base64Input = FileUpload?.Split(',').LastOrDefault()?.Trim();

        if (string.IsNullOrWhiteSpace(base64Input))
            return;

        ImportedSpares = base64Input.GetExcelData<SpareImportModel>(1)
            .Select(x => x.Value.TrimStringProperties())
            .Where(x => !string.IsNullOrWhiteSpace(x.Name))
            .ToList();

        ShowLoadingDialog();
        SpareImportAnalysis = await _spareImportManager.AnalyzeImportFile(ImportedSpares, RiskObjectId, UseImportId);
        SpareImportAnalysis.FileName = UploadRef.FileName;
        SpareImportAnalysis.RiskObjectName = RiskObjects[RiskObjectId];
        _dialogService.Close();

        NextStatus();
    }

    private void NextStatus() => NextStatus(Status, 1);

    private void PreviousStatus() => NextStatus(Status, -1);

    private void NextStatus(ImportSpareStatus status, int step)
    {
        status = (ImportSpareStatus)((int)status + step);
        ImportResult = new();

        switch (status)
        {
            case ImportSpareStatus.Finished or ImportSpareStatus.SelectFile or ImportSpareStatus.ImportSpares:
                Status = status;
                return;
        }

        NextStatus(status, step);
    }

    private async Task ImportSpares()
    {
        await ImportVerifiedSpares();
    }

    private async Task ImportVerifiedSpares()
    {
        NextStatus();
        ShowLoadingDialog();
        ImportResult = await _spareImportManager.ImportSpares(ImportedSpares, RiskObjectId, UseImportId).ConfigureAwait(false);

        // this only closes the loading Dialog, not the current dialog
        _dialogService.Close();

        if (ImportResult.Success)
            FileUpload = null;
    }

    private void ShowLoadingDialog()
    {
        _dialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    private void ChangeFile() => ImportResult = new ImportResult();

    private bool ShowError() => !string.IsNullOrWhiteSpace(ImportResult.ErrorMessage);

    private bool ShowSuccess() => ImportResult.Success;

    // Helper properties and methods for the view
    private bool FileAnalyzed => Status != ImportSpareStatus.SelectFile && Status != ImportSpareStatus.Finished;

    private string GetText()
    {
        if (FileAnalyzed && !SpareImportAnalysis.ImportFileAllowed())
            return Localizer["RoImportSpareNotAllowed"].Value;

        var text = Localizer[GetKey()].Value;
        text = text.Replace("[type]", Localizer[$"{Status}"]);

        return text;
    }

    private string GetKey() =>
        Status switch
        {
            ImportSpareStatus.SelectFile => "RoImportSpareBtn",
            ImportSpareStatus.ImportSpares => "RoImportReadyForImport",
            ImportSpareStatus.Finished => "RoImportFinished",
            _ => "RoImportUpdateReference"
        };

    private bool FoundSparesInImport()
    {
        return SpareImportAnalysis?.SparesInImport > 0 == true;
    }

    private bool FoundRiskInImport()
    {
        return SpareImportAnalysis?.RisksInImport > 0 == true;
    }

    private bool RisksInDbMatchImport()
    {
        return SpareImportAnalysis.RisksFoundInDB == SpareImportAnalysis.RisksInImport;
    }

    private bool AddPlusUpdateMatchesTasksInImport()
    {
        return SpareImportAnalysis.SparesToAdd + SpareImportAnalysis.SparesToUpdate == SpareImportAnalysis.SparesInImport;
    }
}