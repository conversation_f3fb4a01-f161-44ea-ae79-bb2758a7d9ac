﻿using AMprover.BlazorApp.Components;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Import;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Radzen;
using Radzen.Blazor;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages.RiskOrganize.Import;

public partial class ImportRisksWidget
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private IRiskImportManager RiskImportManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IJSRuntime JSRuntime { get; set; }
    [Inject] private ILogger<ImportRisksWidget> Logger { get; set; }
    [Inject] private IStringLocalizer<RiskOrganizer> Localizer { get; set; }

    private RadzenFileInput<string> UploadRef { get; set; }

    private int RiskObjectId { get; set; }

    private Dictionary<int, string> RiskObjects { get; set; }

    private bool GenerateObjects { get; set; }

    private bool GenerateFailureModes { get; set; }

    private AnalyzeRiskImportResult ImportAnalyzeResult { get; set; } = new();

    private ImportResult ImportResult { get; set; }

    private bool UseImportId { get; set; }

    private string FileUpload { get; set; }

    protected override void OnInitialized()
    {
        RiskObjects = DropdownManager.GetRiskObjectDict();
        RiskObjectId = RiskObjects.Keys.FirstOrDefault();
        ImportResult = new();
        ImportAnalyzeResult = new();
        FileUpload = null;
    }

    private async Task AnalyzeFile()
    {
        await AnalyzeImport(FileUpload?.Split(',').LastOrDefault()?.Trim()).ConfigureAwait(false);
    }

    private async Task AnalyzeImport(string base64Input)
    {
        ShowLoadingDialog();
        await Task.Delay(1);

        var fileName = !string.IsNullOrWhiteSpace(UploadRef.FileName)
            ? UploadRef.FileName
            : ImportAnalyzeResult.FileName;

        ImportAnalyzeResult = RiskImportManager.AnalyzeImportFile(base64Input, fileName, RiskObjectId, UseImportId);

        // this only closes the loading Dialog, not the current dialog
        DialogService.Close();
    }

    private async Task UploadRisks()
    {
        ShowLoadingDialog();
        await Task.Delay(1);
        ImportResult = RiskImportManager.ImportRisks(ImportAnalyzeResult.Risks, RiskObjectId, GenerateObjects, GenerateFailureModes, UseImportId);

        // this only closes the loading Dialog, not the current dialog
        DialogService.Close();

        if (ImportResult.Success)
            FileUpload = null;

        ImportAnalyzeResult.Status = ImportRiskStatus.Finished;
    }

    private void ShowLoadingDialog()
    {
        DialogService.Open<ImportResultDialog>("Loading",
            new Dictionary<string, object>
            {
                { nameof(ImportResultDialog.ImportResult), new ImportResult { Status = BusinessLogic.Enums.ImportStatus.Loading} }
            });
    }

    private void ChangeFile()
    {
        ImportResult = new();
    }

    private bool ShowError() => !string.IsNullOrWhiteSpace(ImportResult.ErrorMessage);

    private bool ShowSuccess() => ImportResult.Success;

    private void PreviousStatus()
    {
        ImportAnalyzeResult.Status = ImportRiskStatus.SelectFile;
    }

    // Helper properties for the view
    private ImportRiskStatus Status => ImportAnalyzeResult.Status;

    // Helper methods for the view
    private bool FoundRiskInImport() => ImportAnalyzeResult.RisksInFile > 0;
    private bool ObjectsSuccess() => ImportAnalyzeResult.MissingObjects == 0 || GenerateObjects;
    private bool FailureModeSuccess() => ImportAnalyzeResult.MissingFailureModes == 0 || GenerateFailureModes;
    private bool AnalyzeSuccess() => FoundRiskInImport() && ObjectsSuccess() && FailureModeSuccess();
}