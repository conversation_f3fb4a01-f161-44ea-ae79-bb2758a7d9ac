﻿@using AMprover.BusinessLogic.Models.Import
<div class="row">
    <div class="col-12">

        @if (Status == ImportCommonActionStatus.SelectFile)
        {
            <div class="form-group">
                @Localizer["CaImportPopupText"]
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@ImportAnalyzeResult.GenerateStrategies /> @Localizer["CaImportGenerateStrategy"] <br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@ImportAnalyzeResult.GenerateInitiators /> @Localizer["CaImportGenerateInitiator"] <br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@ImportAnalyzeResult.GenerateExecutors /> @Localizer["CaImportGenerateExecutor"] <br />
            </div>

            <div class="form-group">
                <RadzenCheckBox @bind-Value=@ImportAnalyzeResult.GenerateUnitTypes /> @Localizer["CaImportGenerateUnitType"] <br /><br />
            </div>

            <div class="form-group pb-2">
                <RadzenFileInput @ref=@UploadRef
                                 @bind-Value=@FileUpload
                                 Accept=".xls, .xlsx"
                                 TValue="string"
                                 ChooseText=@Localizer["CaImportSelectFile"]
                                 Change=@ChangeFile />
            </div>

            <div class="form-group">
                <RadzenButton Click=@AnalyzeFile
                              Text=@Localizer["CaImportAnalyzeFile"]
                              Disabled=@(string.IsNullOrWhiteSpace(FileUpload)) />
            </div>
        }
        else if (Status == ImportCommonActionStatus.StartImport)
        {
            <!-- Show status details -->
            <div class=@(AnalyzeSuccess() ? "alert alert-success" : "alert alert-danger")>
                Importing: '@Result.FileName' <br />

                @if (!CanProcessFile())
                {
                    @if (!FoundItemsInImport())
                    {
                        <div class="m-2">
                            There were no Risks found in your import file. Please check that you uploaded the correct file.
                        </div>
                    }
                    @if(!IntervalUnitsSuccess())
                    {
                        <div class="m-2">
                            Interval units are missing and can not be automatically generated. Please create the following Interval Units manually:
                            <ul>
                                @foreach(var unit in Result.MissingIntervalUnits)
                                {
                                    <li>@unit</li>
                                }
                            </ul>
                        </div>
                    }
                    @if(!WorkPackageSuccess())
                    {
                        <div class="m-2">
                            Workpackages are missing and can not be automatically generated. Please create the following Workpackages manually:
                            <ul>
                                @foreach (var wp in Result.MissingWorkpackages)
                                {
                                    <li>@wp</li>
                                }
                            </ul>
                        </div>
                    }
                }
                else
                {
                    <div>

                        @(FoundItemsInImport() ? "✔" : "❌") Common actions in Import: @Result.ImportItems.Count <br />
                        ✔ Common Actions that will be added: @Result.ItemsToAdd <br />
                        ✔ Common Actions that will be updated: @Result.ItemsToUpdate <br /><br />

                        <!-- Strategies Handling -->
                        @if (Result.GenerateStrategies && Result.MissingStrategies.Count > 0)
                        {
                            <span>✔ Strategies that will be added to the database: @Result.MissingStrategies.Count <br /></span>
                        }
                        else if (Result.MissingStrategies.Count > 0)
                        {
                            <span>❌ Strategies missing from the database: @Result.MissingStrategies.Count <br /></span>
                        }

                        <!-- Initiators Handling -->
                        @if (Result.GenerateInitiators && Result.MissingInitiators.Count > 0)
                        {
                            <span>✔ Initiators that will be added to the database: @Result.MissingInitiators.Count <br /></span>
                        }
                        else if (Result.MissingInitiators.Count > 0)
                        {
                            <span>❌ Initiators missing from the database: @Result.MissingInitiators.Count <br /></span>
                        }

                        <!-- Executors Handling -->
                        @if (Result.GenerateExecutors && Result.MissingExecutors.Count > 0)
                        {
                            <span>✔ Executors that will be added to the database: @Result.MissingExecutors.Count <br /></span>
                        }
                        else if (Result.MissingExecutors.Count > 0)
                        {
                            <span>❌ Executors missing from the database: @Result.MissingExecutors.Count <br /></span>
                        }

                        <!-- UnitType Handling -->
                        @if (Result.GenerateUnitTypes && Result.MissingUnitTypes.Count > 0)
                        {
                            <span>✔ UnitTypes that will be added to the database: @Result.MissingUnitTypes.Count <br /></span>
                        }
                        else if (Result.MissingUnitTypes.Count > 0)
                        {
                            <span>❌ UnitTypes missing from the database: @Result.MissingUnitTypes.Count <br /></span>
                        }

                    </div>
                }
            </div>

            <div class="row mt-5">
                <div class="col-6 center">
                    <RadzenButton Click=@PreviousStatus
                                  Text=@Localizer["CaImportPrevious"] />
                </div>
                <div class="col-6 center">
                    <RadzenButton Click=@UploadRisks
                                  Text=@Localizer["CaImportStartImport"]
                                  Disabled=!AnalyzeSuccess() />
                </div>
            </div>

        }
        else if(Status == ImportCommonActionStatus.Finished)
        {
            @if (ShowError())
            {
                <div class="form-group pt-3">
                    <div class="alert alert-danger" role="alert">
                        @ImportResult.ErrorMessage
                    </div>
                </div>
            }

            @if (ShowSuccess())
            {
                <div class="form-group pt-3">
                    <div class="alert alert-success" role="alert">
                        Successfully added @(ImportResult.ItemsAdded) items <br/>
                        Successfully updated @(ImportResult.ItemsUpdated) items
                    </div>
                </div>
            }
        }
    </div>
</div>
