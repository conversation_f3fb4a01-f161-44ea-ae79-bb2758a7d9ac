﻿@page "/value-risk-exports"
@using AMprover.BusinessLogic.Models.Reports
@using AMprover.BusinessLogic.Models.Import

<div class="row">
    <div class="col-6">
        <h2>@Localizer["RpExportHeaderTxt"]</h2>
    </div>
</div>

<div class="row header-navigation">
    <div class="col-6">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
                <NavLink class="breadcrumb-item" href="value-risk-organizer/" Match="NavLinkMatch.All">
                    Value Risk Organizer
                </NavLink>
                <NavLink class="breadcrumb-item" aria-current="page" Match="NavLinkMatch.All">
                    Value Risk Exporter
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@Localizer["RpExportMenuTitle"] DialogContent=@Localizer["RpExportMenuTxt"] />
    </div>
</div>

<div class="row">
    <div class="col-12">

        <div class="row">
            <div class="col-3">
                <AMDropdown 
                    Label=@Localizer["RpScenarioLbl"]
                    Data=@Scenarios
                    @bind-Value=@SelectedScenario
                    Change=@UpdateRiskObjectDropdown
                    ContainerClass="form-group mt-3"/>
            </div>
        </div>

        <div class="row">
            <div class="col-3">
                <AMDropdown 
                    Label=@Localizer["RpRiskObjectLbl"]
                    Data=@RiskObjects
                    @bind-Value=@SelectedRiskObject
                    Change=@GetData
                    ContainerClass="form-group mt-3"/>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <RadzenTabs>
                    <Tabs>

                        <!-- Tasks -->
                        <RadzenTabsItem Text=@GetRiskTabHeader() >
                            <div class="row">
                                <div class="col-12">

                                    <UtilityGrid TItem=RiskImportModel
                                        Data=Risks
                                        FileName=@GridNames.Imports.Risks
                                        Interactive=false
                                        AllowXlsExport=true
                                        AllowFiltering=true
                                        MaxRows="20"
                                        DynamicWidth=true />

                                </div>
                            </div>
                        </RadzenTabsItem>

                        <!-- Tasks -->
                        <RadzenTabsItem Text=@GetTaskTabHeader() >
                            <div class="row">
                                <div class="col-12">

                                    <UtilityGrid 
                                        TItem=TaskImportModel
                                        Data=Tasks
                                        FileName=@GridNames.Imports.PreventiveActions
                                        Interactive=false
                                        AllowXlsExport=true
                                        AllowFiltering=true
                                        MaxRows="20"
                                        DynamicWidth=true />

                                </div>
                            </div>
                        </RadzenTabsItem>

                        <!-- Spares -->
                        <RadzenTabsItem Text=@GetSpareTabHeader() >
                            <div class="row">
                                <div class="col-12">

                                    <UtilityGrid 
                                        TItem=SpareImportModel
                                        Data=Spares
                                        FileName=@GridNames.Imports.Spares
                                        Interactive=false
                                        AllowXlsExport=true
                                        AllowFiltering=true
                                        MaxRows="20"
                                        DynamicWidth=true />

                                </div>
                            </div>
                        </RadzenTabsItem>
                        <!-- TaskPlan -->
                        <RadzenTabsItem Text=@GetTaskPlanTabHeader()>
                            <div class="row">
                                <div class="col-12">

                                    <UtilityGrid TItem=TaskPlanReportItemModel
                                                 Data=TaskPlan
                                                 FileName=@GridNames.Imports.TaskPlan
                                                 Interactive=false
                                                 AllowXlsExport=true
                                                 AllowFiltering=true
                                                 MaxRows="20"
                                                 DynamicWidth=true />

                                </div>
                            </div>
                        </RadzenTabsItem>

                    </Tabs>
                </RadzenTabs>
            </div>
        </div>

    </div>
</div>