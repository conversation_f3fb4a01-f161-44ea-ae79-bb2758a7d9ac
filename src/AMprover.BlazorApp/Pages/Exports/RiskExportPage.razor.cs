﻿using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Pages.Report;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.Import;
using AMprover.BusinessLogic.Models.Reports;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;

namespace AMprover.BlazorApp.Pages.Exports;

public partial class RiskExportPage
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; } = default!;
    [Inject] private DialogService DialogService { get; set; } = default!;
    [Inject] private ILookupManager LookupManager { get; set; } = default!;
    [Inject] private IObjectManager ObjectManager { get; set; } = default!;
    [Inject] private IStringLocalizer<ReportPage> Localizer { get; set; } = default!;
    [Inject] private IDropdownManager DropdownManager { get; set; } = default!;
    [Inject] private IRiskExportManager RiskExportManager { get; set; } = default!;
    [Inject] private ReportViewQueryParams QueryParams { get; set; } = default!;

    [Parameter]
    public string SelectedReportType { get; set; }



    public List<ObjectModel> Objects { get; set; } = [];

    private Dictionary<int, string> Scenarios { get; set; } = new();

    private Dictionary<int, string> RiskObjects { get; set; } = new();

    private int SelectedScenario { get; set; }

    private int SelectedRiskObject { get; set; }

    public Dictionary<int, string> SiCategories { get; set; } = new();

    private Dictionary<ObjectLevel, string> ObjectLevels { get; set; } = new();

    public List<RiskImportModel> Risks { get; set; }
    public List<SpareImportModel> Spares { get; set; }
    public List<TaskImportModel> Tasks { get; set; }
    public List<TaskPlanReportItemModel> TaskPlan { get; set; }

    private string TabTaskPlanTitle { get; set; }
    private string TabSparesTitle { get; set; }
    private string TabTasksTitle { get; set; }
    private string TabRiskTitle { get; set; }

    protected override void OnInitialized()
    {
        Scenarios = DropdownManager.GetScenarioDict();
        Scenarios = (new Dictionary<int, string> { { 0, "Please Select" } }).Concat(Scenarios).ToDictionary(k => k.Key, v => v.Value);

        RiskObjects = DropdownManager.GetRiskObjectDict();
        RiskObjects = (new Dictionary<int, string> { { 0, "Please Select" } }).Concat(RiskObjects).ToDictionary(k => k.Key, v => v.Value);

        SiCategories = DropdownManager.GetSiCategoriesDict();
        ObjectLevels = ObjectManager.GetObjectLevelNames();

        SelectedRiskObject = RiskObjects.Keys.FirstOrDefault();

        SetTitlesTabs();
    }

    private void UpdateRiskObjectDropdown()
    {
        if(SelectedScenario == 0)
        {
            RiskObjects = DropdownManager.GetRiskObjectDict();
            RiskObjects = (new Dictionary<int, string> { { 0, "Please Select" } }).Concat(RiskObjects).ToDictionary(k => k.Key, v => v.Value);
        }
        else
        {
            RiskObjects = DropdownManager.GetRiskObjectDictByScenarioId(SelectedScenario);
            RiskObjects = (new Dictionary<int, string> { { 0, "Please Select" } }).Concat(RiskObjects).ToDictionary(k => k.Key, v => v.Value);
        }
    }

    private void GetData()
    {
        Risks = RiskExportManager.GetRisksForExport(SelectedRiskObject);
        Tasks = RiskExportManager.GetTasksByRiskObject(SelectedRiskObject);
        Spares = RiskExportManager.GetSparesByRiskObject(SelectedRiskObject);
        TaskPlan = RiskExportManager.GetTaskPlanByRiskObject(SelectedRiskObject);
    }

    private string GetRiskTabHeader() => $"{TabRiskTitle} ({Risks?.Count ?? 0})";

    private string GetTaskTabHeader() => $"{TabTasksTitle} ({Tasks?.Count ?? 0})";

    private string GetSpareTabHeader() => $"{TabSparesTitle} ({Spares?.Count ?? 0})";

    private string GetTaskPlanTabHeader() => $"{TabTaskPlanTitle} ({TaskPlan?.Count ?? 0})";

    private void SetTitlesTabs()
    {
        TabTaskPlanTitle = Localizer["RpTaskPLanTabTxt"];
        TabSparesTitle = Localizer["RpSparesTabTxt"];
        TabTasksTitle = Localizer["RpActionsTabTxt"];
        TabRiskTitle = Localizer["RpRiskTabTxt"];
    }
}