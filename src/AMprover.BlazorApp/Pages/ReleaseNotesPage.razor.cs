using AMprover.BusinessLogic;
using Markdig;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Pages;

public partial class ReleaseNotesPage
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IGlobalDataService GlobalDataService { get; set; }
    [Inject] private IStringLocalizer<ReleaseNotesPage> Localizer { get; set; }
    [Inject] private IWebHostEnvironment WebHostEnvironment { get; set; }

    private ILogger<ReleaseNotesPage> _logger;

    public string MarkDownContent { get; set; }

    protected override void OnInitialized()
    {
        _logger = LoggerFactory.CreateLogger<ReleaseNotesPage>();
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadReleaseNotes();
    }

    private async Task LoadReleaseNotes()
    {
        try
        {
            // Get current language from LookupManager
            var language = GlobalDataService.Language;

            // Try to load release notes for current language
            var path = Path.Combine(WebHostEnvironment.WebRootPath, $"ReleaseNotes.{language[..2]}.md");

            // Fallback to English if file doesn't exist
            if (!File.Exists(path))
            {
                _logger.LogInformation("Release notes not found for language {Language}, falling back to English", language);
                path = Path.Combine(WebHostEnvironment.WebRootPath, "ReleaseNotes.en.md");
            }

            // Read and convert markdown to HTML
            var markDown = await File.ReadAllTextAsync(path);
            MarkDownContent = Markdown.ToHtml(markDown);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading release notes");
            MarkDownContent = $"<p class='text-danger'>{Localizer["ReleaseNotesErrorLoading"]}</p>";
        }
    }
}