﻿@page "/VDMXL-view/{LccId:int?}"
@using AMprover.BusinessLogic.Models.LCC

<div class="row">
    <div class="col-6">
        <p><h2>@((MarkupString)Localizer["VdmXlHeaderTxt"].Value)</h2></p>
    </div>
</div>

<div class="row header-navigation">
    <div class="col-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
                <NavLink class="breadcrumb-item" aria-current="page" Match="NavLinkMatch.All">
                    VDMXL-view
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-4 text-center">
        <Paginator Initial=@GetInitialPaginatorValue()
                   Count=@GetScenarioCount()
                   CallBack=@PaginatorCallback
                   @ref=@Paginator />
    </div>
    <div class="col-4 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@Localizer["VdmXlMenuTitle"] DialogContent=@Localizer["VdmXlMenuTxt"] />
        <RadzenButton Icon="add_circle_outline" Text=@Localizer["VdmXlNewVdmXl"] class="float-right my-2 mx-0" Click=@OpenNewLccWidget ButtonStyle=ButtonStyle.Secondary Disabled=!GlobalDataService.CanEdit />
        <RadzenButton class="float-right my-2 mx-2" Icon="refresh" Text=@Localizer["VdmXlRecalculateBtn"] Click=@SaveAndReCalculateLcc
                      ButtonStyle=ButtonStyle.Secondary Disabled=Busy BusyText="Calculating..." IsBusy=Busy />
    </div>
</div>

<div class="row">
    <div class="col-sm-3">
        <RadzenDropDown AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" Data="@RiskScenarios" @bind-value=SelectedScenario
                        TextProperty="Name" ValueProperty="Id" TValue="int" Change=@(args => SelectScenario(args)) />

        <RadzenTabs @ref=TreeTab TabPosition=TabPosition.Right class="hide-tabs-nav">
            <Tabs>
                <RadzenTabsItem>
                    <TreeComponent TItem=LccTreeObject
                                   Treeview=@LccTree
                                   NodeClickCallback=@ClickTreeNode
                                   DeleteCallback=@(args => DeleteLccFromTree(args))/>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
    <div class="col-sm-9">
        <RadzenTabs @ref=VdmTab TabPosition=TabPosition.Right class="hide-tabs-nav">
            <Tabs>
                <RadzenTabsItem>
                    @if (SelectedLcc != null)
                    {
                        <EditForm Model="@SelectedLcc">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="row">
                                        <div class="col-sm-2">
                                            <div class="form-group vdmxl-neg-margin-small">
                                                <label>Id:</label>
                                                <span class="form-control readonly vdmxl-neg-margin-small">@SelectedLcc.Id</span>
                                            </div>
                                        </div>
                                        <div class="col-sm-10">
                                            <AMproverTextBox @bind-Value=@SelectedLcc.Name MaxLength="50" Label=@Localizer["VdmXlNameLbl"] />
                                        </div>
                                    </div>
                                    <div class="vdmxl-frame-overview vdmxl-bg-image">
                                        <div class="row col-sm-12">
                                            <div class="col-sm-3 mt-2">
                                                <RadzenRow class="float-left vdmxl-view-before">
                                                    <p>@Localizer["VdmXlVRwoActionsTxt"]</p>
                                                </RadzenRow>
                                                @if (!_isSapaView)
                                                {
                                                    <RadzenRow class="float-left vdmxl-view-pmo">
                                                        <p>@Localizer["VdmXlVRPmoTxt"]</p>
                                                    </RadzenRow>
                                                }
                                                <RadzenRow class="float-left vdmxl-view-after">
                                                    <p>@Localizer["VdmXlVRwithActionsTxt"]</p>
                                                </RadzenRow>
                                            </div>
                                            <div class="col-sm-6 center vdmxl-frame-specific ">
                                                <text class="rz-text-align-center bold">@Localizer["VdmXlRisksTxt"]:</text>
                                                <RadzenRow Gap=0>
                                                    <RadzenColumn Size="5" class="rz-text-align-left vdmxl-view-index rz-p-1">
                                                        <p>@((MarkupString)RiskTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before rz-p-1">@*Visible=false Size="0"*@
                                                        <p>@((MarkupString)RiskTxt[1])</p>
                                                    </RadzenColumn>
                                                    @if (!_isSapaView)
                                                    {
                                                        <RadzenColumn class="vdmxl-view-pmo rz-p-1">
                                                            <p>@(
                                                                 (MarkupString)RiskTxt[2]
                                                                 )</p>
                                                        </RadzenColumn>
                                                    }
                                                    <RadzenColumn class="vdmxl-view-after rz-p-1">
                                                        <p>@((MarkupString)RiskTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                           
                                            <div class="col-sm-3 center">
                                                <RadzenRow class="rz-text-align-center" Gap=0>
                                                    <RadzenColumn class="rz-text-align-left vdmxl-view-index mt-2">
                                                        <p>@((MarkupString)NpvTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before mt-2">
                                                        <p>@((MarkupString)NpvTxt[1])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                                @if (!_isSapaView)
                                                {
                                                    <RadzenRow class="rz-text-align-center" Gap=0>
                                                        <RadzenColumn class="vdmxl-neg-margin" />
                                                        <RadzenColumn class="vdmxl-view-pmo vdmxl-neg-margin">
                                                            <p>@((MarkupString)NpvTxt[2])</p>
                                                        </RadzenColumn>
                                                    </RadzenRow>
                                                }
                                                <RadzenRow class="rz-text-align-center" Gap=0>
                                                    <RadzenColumn class="vdmxl-neg-margin" />
                                                    <RadzenColumn class="vdmxl-view-after vdmxl-neg-margin">
                                                        <p>@((MarkupString)NpvTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                                <RadzenRow class="rz-text-align-center" Gap=0>
                                                    <RadzenColumn class="rz-text-align-left vdmxl-view-index vdmxl-neg-margin">
                                                        <p>@((MarkupString)AecTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before vdmxl-neg-margin">
                                                        <p>@((MarkupString)AecTxt[1])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                                @if (!_isSapaView)
                                                {
                                                    <RadzenRow class="rz-text-align-center" Gap=0>
                                                        <RadzenColumn class="vdmxl-neg-margin" />
                                                        <RadzenColumn class="vdmxl-view-pmo vdmxl-neg-margin">
                                                            <p>@(
                                                                 (MarkupString)AecTxt[2]
                                                                 )</p>
                                                        </RadzenColumn>
                                                    </RadzenRow>
                                                }
                                                <RadzenRow class="rz-text-align-center" Gap=0>
                                                    <RadzenColumn class="vdmxl-neg-margin" />
                                                    <RadzenColumn class="vdmxl-view-after vdmxl-neg-margin">
                                                        <p>@((MarkupString)AecTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                        </div>
                                        <br /><br /><br /><br /><br />
                                        <div class="row col-sm-12">
                                            <div class="col-sm-4 float-left vdmxl-frame-specific">
                                                <div>
                                                    <text class="bold">@Localizer["VdmXlUtlizationTxt"]:</text>
                                                </div>
                                                <RadzenRow class="rz-text-align-center rz-border-lightgrey" Gap=0>
                                                    <RadzenColumn Size="5" class="rz-text-align-left vdmxl-view-index rz-p-1">
                                                        <p>@((MarkupString)UtilizationTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before rz-p-1">
                                                        <p>@((MarkupString)UtilizationTxt[1])</p>
                                                    </RadzenColumn>
                                                    @if (!_isSapaView)
                                                    {
                                                        <RadzenColumn class="vdmxl-view-pmo rz-p-1">
                                                            <p>@((MarkupString)UtilizationTxt[2])</p>
                                                        </RadzenColumn>
                                                    }
                                                    <RadzenColumn class="vdmxl-view-after rz-p-1">
                                                        <p>@((MarkupString)UtilizationTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                            <div class="col-sm-4 center"></div>
                                            <div class="col-sm-4 float-right vdmxl-frame-specific">
                                                <div>
                                                    <text class="bold">@Localizer["VdmXlOpexTxt"]:</text>
                                                </div>
                                                <RadzenRow class="rz-text-align-center rz-border-lightgrey" Gap=0>
                                                    <RadzenColumn Size="5" class="rz-text-align-left vdmxl-view-index rz-p-1">
                                                        <p>@((MarkupString)OpexTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before rz-p-1">
                                                        <p>@((MarkupString)OpexTxt[1])</p>
                                                    </RadzenColumn>
                                                    @if (!_isSapaView)
                                                    {
                                                        <RadzenColumn class="vdmxl-view-pmo rz-p-1">
                                                            <p>@((MarkupString)OpexTxt[2])</p>
                                                        </RadzenColumn>
                                                    }
                                                    <RadzenColumn class="vdmxl-view-after rz-p-1">
                                                        <p>@((MarkupString)OpexTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                        </div>
                                        <br /><br /><br /><br /><br />
                                        <div class="row col-sm-12">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6 center vdmxl-frame-specific">
                                                <div >
                                                    <text class="bold">@Localizer["VdmXlCapexTxt"]:</text>
                                                </div>
                                                <RadzenRow class="rz-text-align-center rz-border-lightgrey" Gap=0>
                                                    <RadzenColumn Size="5" class="rz-text-align-left vdmxl-view-index rz-p-1">
                                                        <p>@((MarkupString)CapexTxt[0])</p>
                                                    </RadzenColumn>
                                                    <RadzenColumn class="vdmxl-view-before rz-p-1">
                                                        <p>@((MarkupString)CapexTxt[1])</p>
                                                    </RadzenColumn>
                                                    @if (!_isSapaView)
                                                    {
                                                        <RadzenColumn class="vdmxl-view-pmo rz-p-1">
                                                            <p>@((MarkupString)CapexTxt[2])</p>
                                                        </RadzenColumn>
                                                    }
                                                    <RadzenColumn class="vdmxl-view-after rz-p-1">
                                                        <p>@((MarkupString)CapexTxt[3])</p>
                                                    </RadzenColumn>
                                                </RadzenRow>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <br />
                        </EditForm>
                    }
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
</div>
