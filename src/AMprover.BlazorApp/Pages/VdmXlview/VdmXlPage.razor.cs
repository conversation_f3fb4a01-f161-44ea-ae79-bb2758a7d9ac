using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using AMprover.BlazorApp.Components;
using AMprover.BlazorApp.Components.Pagination;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.LCC;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using <PERSON><PERSON>zen;
using Radzen.Blazor;
using AMprover.BlazorApp.Pages.LCC;
using AMprover.BusinessLogic.Helpers;

namespace AMprover.BlazorApp.Pages.VdmXlview;

public partial class VdmXlPage
{
    [Inject] private ILCCManager LccManager { get; set; }
    [Inject] private IScenarioManager ScenarioManager { get; set; }
    [Inject] private IPageNavigationManager PageNavigationManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IStringLocalizer<VdmXlPage> Localizer { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] public IGlobalDataService GlobalDataService { get; set; }

    public bool Busy { get; set; }

    [Parameter] public int? LccId { get; set; }

    private List<ScenarioModel> RiskScenarios { get; set; }

    private TreeGeneric<LccTreeObject> LccTree { get; set; } = new();

    private LccTreeObject CurrentLcc { get; set; }

    private LccModel SelectedLcc { get; set; }

    private LccDetailModel SelectedLccDetail { get; set; }

    private VdmxlModel SelectedVdmDetail { get; set; }

    private string[] RiskTxt { get; set; } = new string[4]; //for effect columns
    private string[] UtilizationTxt { get; set; } = new string[4]; //for Before/PMO/After
    private string[] OpexTxt { get; set; } = new string[4]; //for Before/PMO/After
    private string[] CapexTxt { get; set; } = new string[4]; //for Before/PMO/After
    private string[] NpvTxt { get; set; } = new string[4]; //for Txt and Value
    private string[] AecTxt { get; set; } = new string[4]; //for Txt and Value

    private RadzenTabs TreeTab { get; set; }

    private RadzenTabs VdmTab { get; set; }

    private Paginator Paginator { get; set; }

    private bool _isSapaView;

    public int SelectedScenario { get; set; }

    protected override void OnInitialized()
    {
        base.OnInitialized();

        Busy = true;
        RiskScenarios = ScenarioManager.GetAllScenarios();

        if (LccId != null)
        {
            SelectedLcc = LccManager.GetLccDetailed(LccId.Value);
            SelectedScenario = SelectedLcc.ScenarioId
                            ?? SelectedLcc.RiskObject?.ScenarioId
                            ?? RiskScenarios.FirstOrDefault()?.Id
                            ?? 0;

            _isSapaView = SelectedLcc.RiskObject?.AnalysisType == "SAPA";

            LccTree.Initialize(LccManager.GetTreeViewForScenario(SelectedScenario));
            PageNavigationManager.SetSelectedScenario(SelectedScenario);

            var node = LccTree.GetLccTreeNode(LccId.Value);

            if (node != null)
                LccTree.SelectNode(node);
        }
        else
        {
            SelectedScenario = PageNavigationManager.GetSelectedScenario() ?? RiskScenarios.FirstOrDefault()?.Id ?? 0;
            SelectFirstLcc();
        }

        SelectVdmXlTexts();
        Busy = false;
    }

    private void SelectVdmXlTexts()
    {
        for (var n = 0; n <= 3; n++)
        {
            RiskTxt[n] = string.Empty;
            UtilizationTxt[n] = string.Empty;
            OpexTxt[n] = string.Empty;
            CapexTxt[n] = string.Empty;
            NpvTxt[n] = string.Empty;
            AecTxt[n] = string.Empty;
        }

        var tempVdmXlValue = new decimal?[4];

        if (SelectedLcc == null) return;
        SelectedVdmDetail = SelectedLcc.Vdmxl;
        if (SelectedVdmDetail == null) return;

        //Set Txt:
        var template = SelectedLcc.RiskObject.Fmeca;
        var gridColumn = template.MainGrid.TableColumns;
        var fmecaColumn = 0;

        foreach (var item in gridColumn
        .Where(gridColumn => gridColumn.IsEffectColumn))
        {
            try
            {
                var directCostColumn = item.Setting == 1;
                var utilizationColumn = item.Setting == 2;
                var isPercentage = item.IsPercentage;
                //For now in the Value Risk Matrix 'Utilization' is classified as Circuit Affected Cost (column.Setting == 2). Perhaps this should be extended with the option Utiliztion and other Circuit Affected Cost.
                switch (fmecaColumn)
                {
                    case 0:
                        tempVdmXlValue[1] = SelectedVdmDetail.VdmFmeca1Before;
                        tempVdmXlValue[2] = SelectedVdmDetail.VdmFmeca1Pmo;
                        tempVdmXlValue[3] = SelectedVdmDetail.VdmFmeca1After;
                        break;
                    case 1:
                        tempVdmXlValue[1] = SelectedVdmDetail.VdmFmeca2Before;
                        tempVdmXlValue[2] = SelectedVdmDetail.VdmFmeca2Pmo;
                        tempVdmXlValue[3] = SelectedVdmDetail.VdmFmeca2After;
                        break;
                    case 2:
                        tempVdmXlValue[1] = SelectedVdmDetail.VdmFmeca3Before;
                        tempVdmXlValue[2] = SelectedVdmDetail.VdmFmeca3Pmo;
                        tempVdmXlValue[3] = SelectedVdmDetail.VdmFmeca3After;
                        break;
                    case 3:
                        tempVdmXlValue[1] = SelectedVdmDetail.VdmFmeca4Before;
                        tempVdmXlValue[2] = SelectedVdmDetail.VdmFmeca4Pmo;
                        tempVdmXlValue[3] = SelectedVdmDetail.VdmFmeca4After;
                        break;
                    case 4:
                        tempVdmXlValue[1] = SelectedVdmDetail.VdmFmeca5Before;
                        tempVdmXlValue[2] = SelectedVdmDetail.VdmFmeca5Pmo;
                        tempVdmXlValue[3] = SelectedVdmDetail.VdmFmeca5After;
                        break;
                    case 5:
                        tempVdmXlValue[1] = SelectedVdmDetail.VdmFmeca6Before;
                        tempVdmXlValue[2] = SelectedVdmDetail.VdmFmeca6Pmo;
                        tempVdmXlValue[3] = SelectedVdmDetail.VdmFmeca6After;
                        break;
                    case 6:
                        tempVdmXlValue[1] = SelectedVdmDetail.VdmFmeca7Before;
                        tempVdmXlValue[2] = SelectedVdmDetail.VdmFmeca7Pmo;
                        tempVdmXlValue[3] = SelectedVdmDetail.VdmFmeca7After;
                        break;
                    case 7:
                        tempVdmXlValue[1] = SelectedVdmDetail.VdmFmeca8Before;
                        tempVdmXlValue[2] = SelectedVdmDetail.VdmFmeca8Pmo;
                        tempVdmXlValue[3] = SelectedVdmDetail.VdmFmeca8After;
                        break;
                }

                if (utilizationColumn)
                {
                    UtilizationTxt[0] += FmecaHelper.EffectName(fmecaColumn, template).TruncateMax(30, 0) + ": ";

                    for (var n = 1; n <= 3; n++)
                    {
                        UtilizationTxt[n] += tempVdmXlValue[n] == 0
                            ? "-"
                            : FormatAsSelectedCurrency(tempVdmXlValue[n], isPercentage);
                    }
                }
                else if (directCostColumn)
                {
                    OpexTxt[0] += Localizer["VdmXlCorrectiveMxlbl"] + ":<br/>";
                    for (var n = 1; n <= 3; n++)
                    {
                        OpexTxt[n] += tempVdmXlValue[n] == 0
                            ? "-<br/>"
                            : FormatAsSelectedCurrency(tempVdmXlValue[n], isPercentage) + "<br/>";
                    }
                }
                else
                {
                    if (fmecaColumn != 0) // no break at first line
                    {
                        RiskTxt[0] += "<br/>" + FmecaHelper.EffectName(fmecaColumn, template).TruncateMax(30, 0) + ":";
                        for (var n = 1; n <= 3; n++)
                        {
                            RiskTxt[n] += tempVdmXlValue[n] == 0
                                ? "<br/>-"
                                : "<br/>" + FormatAsSelectedCurrency(tempVdmXlValue[n], isPercentage);
                        }
                    }
                    else
                    {
                        RiskTxt[0] += FmecaHelper.EffectName(fmecaColumn, template).TruncateMax(30, 0) + ":";
                        for (var n = 1; n <= 3; n++)
                        {
                            RiskTxt[n] += tempVdmXlValue[n] == 0
                                ? "-"
                                : FormatAsSelectedCurrency(tempVdmXlValue[n], isPercentage);
                        }
                    }
                }
            }
            catch
            {
                NoCalcMethodSet();
            }
            fmecaColumn++;
        }

        //add Task Downtime to Utilization
        if (SelectedVdmDetail.VdmDtCostTasks != 0)
        {
            UtilizationTxt[0] += "<br/>" + Localizer["VdmXlTaskDownTimelbl"] + ":";
            UtilizationTxt[3] += "<br/>" + FormatAsSelectedCurrency(SelectedVdmDetail.VdmDtCostTasks, false);
        }

        OpexTxt[0] += Localizer["VdmXlPreventiveMxlbl"] + ":";
        OpexTxt[1] += "-";
        OpexTxt[2] += SelectedVdmDetail.VdmOpexPmo == 0
            ? "-"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmOpexPmo, false);
        OpexTxt[3] += SelectedVdmDetail.VdmOpexAfter == 0
            ? "-"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmOpexAfter, false);
        CapexTxt[0] += Localizer["VdmXlSparesModlbl"] + ":<br/>";
        CapexTxt[1] += "-<br/>";
        CapexTxt[2] += SelectedVdmDetail.VdmCapexPmo == 0
            ? "-<br/>"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmCapexPmo, false) + "<br/>";
        CapexTxt[3] += SelectedVdmDetail.VdmCapexAfter == 0
            ? "-<br/>"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmCapexAfter, false) + "<br/>";
        CapexTxt[0] += Localizer["VdmXlInitialInvestmentlbl"] + ":<br/>";
        CapexTxt[1] += SelectedLcc.ReplacementValue == 0
            ? "-<br/>"
            : FormatAsSelectedCurrency(SelectedLcc.ReplacementValue, false) + "<br/>";
        CapexTxt[2] += SelectedLcc.ReplacementValue == 0
            ? "-<br/>"
            : FormatAsSelectedCurrency(SelectedLcc.ReplacementValue, false) + "<br/>";
        CapexTxt[3] += SelectedLcc.ReplacementValue == 0
            ? "-<br/>"
            : FormatAsSelectedCurrency(SelectedLcc.ReplacementValue, false) + "<br/>";
        CapexTxt[0] += Localizer["VdmXlOptLifeTimeTxt"] + ":";
        CapexTxt[3] += SelectedLcc.Npvyear == 0
            ? "-"
            : SelectedLcc.Npvyear + " " + Localizer["VdmXlTimeUnitTxt"];

        NpvTxt[0] += Localizer["VdmXlNpvTxt"] + ":";
        NpvTxt[1] += SelectedVdmDetail.VdmNpvBefore == 0
            ? "-"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmNpvBefore, false);
        NpvTxt[2] += SelectedVdmDetail.VdmNpvPmo == 0
            ? "-"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmNpvPmo, false);
        NpvTxt[3] += SelectedVdmDetail.VdmNpvAfter == 0
            ? "-"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmNpvAfter, false);

        AecTxt[0] += Localizer["VdmXlAecTxt"] + ":";
        AecTxt[1] += SelectedVdmDetail.VdmAecBefore == 0
            ? "-"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmAecBefore, false);
        AecTxt[2] += SelectedVdmDetail.VdmAecPmo == 0
            ? "-"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmAecPmo, false);
        AecTxt[3] += SelectedVdmDetail.VdmAecAfter == 0
            ? "-"
            : FormatAsSelectedCurrency(SelectedVdmDetail.VdmAecAfter, false);
    }

    private string FormatAsSelectedCurrency(object value, bool isPercentage)
    {
        if (isPercentage)
            return value != null ? ((decimal)value / 100).ToString("P0", CultureInfo.CreateSpecificCulture(GlobalDataService.Currency)) : "-";
        return value != null ? ((decimal)value).ToString("C0", CultureInfo.CreateSpecificCulture(GlobalDataService.Currency)) : "-";
    }

    private void NoCalcMethodSet()
    {
        DialogService.Open<WarningDialog>(Localizer["VdmXlInfoHeader"].ToString(),
            new Dictionary<string, object> { { "DialogContent", Localizer["VdmXlInfoTxt"].ToString() } },
            new DialogOptions { Width = "350px", Resizable = false, Draggable = true });
    }

    private void SelectScenario(object args)
    {
        SelectedScenario = (int)args;
        PageNavigationManager.SetSelectedScenario(SelectedScenario);
        LccTree.Initialize(LccManager.GetTreeViewForScenario(SelectedScenario));
        SelectFirstLcc();
    }

    private void ClickTreeNode(TreeNodeGeneric<LccTreeObject> node)
    {
        CurrentLcc = node.Source;

        if (CurrentLcc.Id.HasValue)
        {
            Busy = true;
            if (SelectedLcc?.Id != CurrentLcc.Id)
            {
                SelectedLcc = LccManager.GetLccDetailed(CurrentLcc.Id.Value);
                PageNavigationManager.SavePageQueryString($"/VDMXL-view/{CurrentLcc.Id}", string.Empty);
                NavigationManager.NavigateTo($"/VDMXL-view/{CurrentLcc.Id}");
            }

            if (SelectedLcc != null)
                SelectedLccDetail = SelectedLcc.Details.FirstOrDefault();

            UpdatePaginator(CurrentLcc.Id);

            SelectVdmXlTexts();
            Busy = false;
        }
    }

    private void SaveAndReCalculateLcc()
    {
        Busy = true;
        CreateOrUpdateObjectAndNavigate(SelectedLcc);
        SelectVdmXlTexts();
        Busy = false;
    }

    private void CreateOrUpdateObjectAndNavigate(LccModel lcc)
    {
        SelectedLcc = LccManager.CalculateLcc(lcc);
    }

    private void OpenNewLccWidget()
    {
        DialogService.Open<NewLCCWidget>
        (Localizer["VdmXlWHeaderTxt"],
            new Dictionary<string, object>
            {
                    {nameof(NewLCCWidget.ScenarioId), SelectedScenario},
                    {nameof(NewLCCWidget.CallBack), EventCallback.Factory.Create<int>(this, UpdateTree)}
            });
    }

    private void UpdateTree(int lccRiskObjectId)
    {
        LccTree.Initialize(LccManager.GetTreeViewForScenario(SelectedScenario));
        SelectedLcc = LccManager.GetLccDetailed(lccRiskObjectId);
        SaveAndReCalculateLcc();

        var node = LccTree.GetFlattenedNodes().FirstOrDefault(x => x.Id == lccRiskObjectId);

        if (node != null)
        {
            LccTree.SelectNode(node);
        }

        VdmTab?.Reload();
        TreeTab?.Reload();
    }

    private void DeleteLccFromTree(LccTreeObject lccTreeObject)
    {
        if (!(lccTreeObject.Id > 0))
            return;

        LccManager.DeleteLcc(lccTreeObject.Id.Value);

        if (lccTreeObject.Id == SelectedLcc.Id)
            SelectFirstLcc();

        SelectVdmXlTexts();
    }

    private int GetInitialPaginatorValue()
    {
        var node = LccTree.GetLccTreeNode(LccId ?? 0);
        return GetLccTreeNodes().IndexOf(node);
    }

    private void PaginatorCallback(int lccIndex)
    {
        var treeNode = GetLccTreeNodes()?.Skip(lccIndex).FirstOrDefault();

        if (treeNode == null)
            return;

        LccTree.SelectNode(treeNode);
        ClickTreeNode(treeNode);
    }

    private void UpdatePaginator(int? lccId)
    {
        lccId ??= LccId;

        if (Paginator == null)
            return;

        var node = GetLccTreeNodes().FirstOrDefault(x => x.Id == lccId);
        if (node != null)
            Paginator.SetCurrentExternally(GetLccTreeNodes().IndexOf(node));
    }

    private int GetScenarioCount() => GetLccTreeNodes().Count;

    private List<TreeNodeGeneric<LccTreeObject>> GetLccTreeNodes() => LccTree.GetFlattenedNodes();

    private void SelectFirstLcc(int? lccId = null)
    {
        LccTree.Initialize(LccManager.GetTreeViewForScenario(SelectedScenario));
        TreeTab?.Reload();

        CurrentLcc = LccTree.GetFlattenedNodes().FirstOrDefault(x => x.Id == lccId)?.Source;

        SelectedLcc = CurrentLcc?.Id != null
            ? LccManager.GetLccDetailed(CurrentLcc.Id.Value)
            : null;

        var node = LccTree.GetFlattenedNodes().FirstOrDefault(x => x.Id == lccId);
        node ??= LccTree.Node?.Nodes?.FirstOrDefault();

        if (node != null)
            LccTree.SelectNode(node);
    }
}