﻿<table class="table table-bordered icon-hover" style="table-layout: fixed">
    <tbody>
        @for (var index = 0; index < RiskMatrixTemplate.MainGrid.TableContent.Count; index++)
        {
            var tempIndex = index;
            var row = RiskMatrixTemplate.MainGrid.TableContent[index];
            <tr style="height:50px">
                @for (var cellIndex = 0; cellIndex < row.Cells.Count; cellIndex++)
                {
                    var cellAggregation = GetCell(cellIndex, index);
                    var tempCellIndex = cellIndex;
                    var cell = row.Cells[cellIndex];
                    var column = RiskMatrixTemplate.MainGrid.TableColumns[cellIndex];
                    <td id="@cell.UniqueId(_tab)" style="@cell.BackGround();" class="rabs-cell-container">

                        <div class="rabs-cell-top">
                            @(index == 0 ? cell.GetHeader(_tab) : column.HasSubColumns ?
                                cell.GetConcatenatedSubGridValue(RiskMatrixTemplate, tempIndex, tempCellIndex, _tab, GlobalDataService.Language) :
                                cell.GetValue(RiskMatrixTemplate, _tab, tempCellIndex))
                        </div>


                        @if(index > 0)
                        {
                            <div class="rabs-cell-bottom">
                                <div style="cursor: pointer; display: inline-block;" @onclick=@(() => ShowRisks(cellAggregation))>
                                    @GetCellSelectedContent(cellAggregation)
                                </div>
                            </div>
                        }

                        @if (tempIndex == 0)
                        {
                            var chanceCount = GetChanceCount(cellIndex);
                            if (chanceCount != null)
                            {
                                <div>
                                    @GetChanceContent((int) chanceCount)
                                </div>
                            }
                        }

                        @if (tempIndex == 0 && column.HasSubColumns)
                        {
                            if (ActivatedSubgrids.Contains(tempCellIndex))
                            {
                                <a style="position:absolute;left:10px;bottom: 1px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => RemoveActiveSubgrid(tempCellIndex))">
                                    <i class="fa-solid fa-arrow-circle-left"></i>
                                </a>
                            }
                            else
                            {
                                <a style="position:absolute;left:10px;bottom: 1px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => AddActiveSubgrid(tempCellIndex))">
                                    <i class="fa-solid fa-arrow-circle-right"></i>
                                </a>
                            }

                            <a style="position:absolute;left:30%;top: -12px;cursor:pointer;z-index:100" class="icon">
                                <i class="fas fa-plus-circle"></i>
                            </a>
                        }
                    </td>

                    @if (column.HasSubColumns && ActivatedSubgrids.Contains(tempCellIndex))
                    {
                        var subgrid = RiskMatrixTemplate.SubGrids.FirstOrDefault(x => x.ColumnId == tempCellIndex);

                        if (subgrid == null) continue;

                        for (var subgridColumnIndex = 0; subgridColumnIndex < subgrid.TableColumns.Count; subgridColumnIndex++)
                        {
                            var tempSubgridColumIndex = subgridColumnIndex;
                            var subGridCell = subgrid.TableContent[tempIndex].Cells[tempSubgridColumIndex];

                            if (tempIndex == 0)
                            {
                                <td class="risk-matrix-subgrid-cell" id="@subGridCell.UniqueId(_tab, true)" style="@subGridCell.BackGround();position: relative; width: 50px;">
                                    @subGridCell.GetHeader(_tab)
                                </td>
                            }
                            else
                            {
                                <td class="risk-matrix-subgrid-cell" id="@subGridCell.UniqueId(_tab, true)" style="@subGridCell.BackGround();position: relative; width: 50px;">
                                    @subGridCell.GetValue(RiskMatrixTemplate, _tab, tempCellIndex)
                                </td>
                            }
                        }
                    }
                }
            </tr>
        }
    </tbody>
</table>
