﻿@page "/value-risks-on-abs"

<h2>@Localizer["RabsTitle"]</h2>

<div class="row header-navigation">
    <div class="col-4">
        <Breadcrumbs Category="Value Risks on ABS" />
    </div>
    <div class="col-4 text-right">
    </div>
    <div class="col-4 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@Localizer["RabsMenuTitle"] DialogContent=@Localizer["RabsMenuTxt"] />
        <RadzenButton Disabled=!GlobalDataService.CanEdit Icon="launch" Text=@Localizer["RabsLinkAbsBtn"] class="float-right my-2" Click=@NavLinkAbs />
    </div>
</div>

<div class="row">
    <div class="col-3 mt-3">

        <div class="row">
            <div class="col-12">
                <div class="float-left mr-3">@Localizer["RabsFmecaMatrix"]:</div>
            </div>
        </div>

        <div class="row">
            <div class="col-10">
                <AMDropdown Data=@RiskMatrixTemplates.ToDictionary(x => x, x => x.Name)
                            @bind-Value=@RiskMatrixTemplate
                            Change=@ChangeMatrix />
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="float-left mr-3">@Localizer["RabsShowOnlyFilled"]:</div>
                <div class="float-left">
                    <AMproverCheckbox @bind-Value=@ShowOnlyAssetsWithData />
                </div>
            </div>
        </div>

        @if (Assets.Any())
        {
            <TreeComponent 
                TItem=RiskOnAbsTreeModel
                Treeview=RiskOnAbsTree
                CssClass="abs-tree"
                NodeClickCallback=@ClickNode
                OpenCallback=@OpenRiskFromNode
                ExpandNodeWhenSelected=false />
        }

    </div>

    <div class="col-9">

        <RadzenTabs Change=@ChangeTab SelectedIndex=@Query.TabId >
            <Tabs>
                @foreach(var matrix in MatrixTypeList)
                {
                    <RadzenTabsItem Text=@GetTabTitle(matrix) >

                        @if (RiskMatrixTemplate != null)
                        {
                            <RiskOnAbsMatrixComponent 
                                RiskMatrixTemplate=@RiskMatrixTemplate
                                MatrixSelectionAggregation=@MatrixSelectionAggregation />
                        }

                    </RadzenTabsItem>
                }

                <RadzenTabsItem Text=@Localizer["RabsCritTabHeader"]>

                    @if (SelectedNode.IsRiskNode)
                    {
                        @Localizer["RabsCritRiskSelected"]
                    }
                    else
                    {
                        @if(Criticality == null)
                        {
                            <p>
                                @Localizer["RabsCritNotAvailable"]
                            </p>
                        }
                        else
                        {
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th scope="col">@Localizer["RabsCritKey"]</th>
                                        <th scope="col">@Localizer["RabsCritValue"]</th>
                                    </tr>
                                </thead>
                                @foreach (var row in CriticalityDetails)
                                {
                                    <tr>
                                        <td>@row.Key</td>
                                        <td>@row.Value</td>
                                    </tr>
                                }
                            </table>
                        }
                    }
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>

    </div>
</div>

@code {
    private string GetTabTitle(MatrixTypes matrixType) => @Localizer[$"RabsMatrix{matrixType}"];
}
