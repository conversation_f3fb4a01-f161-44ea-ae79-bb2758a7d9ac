using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace AMprover.BlazorApp.Pages.Administration;

public partial class Users
{
    [Inject] private UserManager<UserAccount> UserManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }

    private List<UserAccount> ColUsers { get; set; } = [];
    private string StrError { get; set; } = string.Empty;

    protected override void OnInitialized()
    {
        // Get all the users
        GetUsers();
    }

    public void AddNewUser()
    {
        NavigationManager.NavigateTo($"/administration/users/{Guid.Empty}");
    }

    private void NavigateToUser(string userId)
    {
        NavigationManager.NavigateTo($"/administration/users/{userId}");
    }

    private void GetUsers()
    {
        // clear any error messages
        StrError = string.Empty;
            
        // Get users and sort them
        var users = UserManager.Users.ToList();
        ColUsers = users
            .OrderBy(ua => ua.UserName?.Split('@').Skip(1).FirstOrDefault())
            .ThenBy(ua => ua.Name)
            .ToList();
    }
}