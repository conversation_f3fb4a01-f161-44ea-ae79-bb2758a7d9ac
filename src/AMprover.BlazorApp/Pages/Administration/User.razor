﻿@page "/administration/users/{UserId}"
@using AMprover.BlazorApp.Enums

@if (UserAccount != null)
{
    switch (EditorMode)
    {
        case EntityEditorMode.Create:
            <h4>Create new user account</h4>
            break;
        case EntityEditorMode.Update:
            <h4>Update user account for: @UserAccount.Name</h4>
            break;
    }

    <EditForm Model="@UserAccount"
              OnValidSubmit=@UpdateUserAccountValidFormSubmitted
              OnInvalidSubmit=@UpdateUserAccountInvalidFormSubmitted>
        <DataAnnotationsValidator />

        <div class="form-group">
            <RadzenTextBox class="form-control" Placeholder="Userid" @bind-Value=UserAccount.Id Disabled="true" />
        </div>

        <div class="form-group">
            <RadzenTextBox class="form-control" Placeholder="Full name" @bind-Value=UserAccount.Name />
        </div>

        <div class="form-group">
            <RadzenTextBox class="form-control" Placeholder="Username" @bind-Value=UserAccount.UserName type="email" />
            <small class="form-text text-muted">Must be unique within AMprover.</small>
        </div>
        
        <div class="form-group">
            <RadzenTextBox class="form-control" Placeholder="Company" @bind-Value=UserAccount.Company />
        </div>
        
        @if (EditorMode == EntityEditorMode.Create)
        {
            <div class="form-group">
                <RadzenTextBox class="form-control" Placeholder="Password" @bind-Value=UserAccount.PasswordHash type="password" />
            </div>
        }

        <div class="form-group">
            <RadzenDropDown class="form-control" TValue="string" TextProperty="Value" ValueProperty="Key" Data="@AvailableRoles" Placeholder="Select a role" @bind-Value="CurrentRole" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"/>
        </div>

        <div class="form-group">
            <RadzenButton ButtonType="ButtonType.Submit" Text="Save" />
            <span style="color:red">@ValidationError</span>
        </div>
    </EditForm>

    if (EditorMode == EntityEditorMode.Update)
    {
        <hr />
        <PortfolioAssignments UserId="@UserId" />
        <hr />
        <h4>Configure new password</h4>
        <div class="form-group">
            <RadzenTextBox class="form-control" Placeholder="Password" @bind-Value=NewPassword type="password" />
        </div>

        <span style="color:red">@ValidationErrorPassword</span>
        <div class="form-group">
            <RadzenButton Text="Cancel" ButtonStyle="ButtonStyle.Light" Click="@Cancel" />
            <RadzenButton Text="Update password" Click="@UpdatePassword" />
        </div>
        <hr />
        <h4>Delete user</h4>
        <RadzenButton Text="Cancel" ButtonStyle="ButtonStyle.Light" Click="@Cancel" />
        <RadzenButton Text="Delete" ButtonStyle="ButtonStyle.Danger" Click="@DeleteUser" />
    }

    <hr />
    <RadzenButton Text="Return to all users" ButtonStyle="ButtonStyle.Light" Click="@NavigateToOverview" />
}