﻿@page "/administration/users"
@using AMprover.Data.Constants;
@using AMprover.Data.Entities.Identity
@inject IStringLocalizer<Users> Localizer

<AuthorizeView Roles="@RoleConstants.Administrators">
    <Authorized>
        <h3>User management</h3>
        <p>Below you can manage all users of AMprover, for all portfolios.</p>

        <Radzen.Blazor.RadzenButton Icon="add_circle_outline" Text="Add User" Size="ButtonSize.Small" style="margin-bottom: 10px" Click="AddNewUser"/>

        <RadzenDataGrid TItem="UserAccount" Data="@ColUsers" FilterMode="FilterMode.Simple" AllowFiltering="true" AllowPaging="true" AllowSorting="true" PageSize ="15">
            <Columns>
                <RadzenDataGridColumn TItem="UserAccount" Property="@nameof(UserAccount.Id)" Title="@nameof(UserAccount.Id)"/>
                <RadzenDataGridColumn TItem="UserAccount" Property="@nameof(UserAccount.Name)" Title="@nameof(UserAccount.Name)"/>
                <RadzenDataGridColumn TItem="UserAccount" Property="@nameof(UserAccount.UserName)" Title="@nameof(UserAccount.UserName)" />
                <RadzenDataGridColumn TItem="UserAccount" Property="@nameof(UserAccount.Company)" Title="@nameof(UserAccount.Company)" />
                <RadzenDataGridColumn TItem="UserAccount" Filterable="false" Sortable="false" TextAlign="TextAlign.Center" Width="10%">
                    <Template Context="useraccount">
                        <RadzenButton Icon="edit" Text="Edit" Size="ButtonSize.Small" Click="@(args => NavigateToUser(useraccount.Id))"/>
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </Authorized>
</AuthorizeView>