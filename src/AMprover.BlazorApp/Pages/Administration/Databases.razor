﻿@page "/administration/databases"
@using AMprover.Data.Constants;

<AuthorizeView Roles="@RoleConstants.Administrators">
    <Authorized>

        <h2 class="mb-3">Database Management</h2>

        <RadzenTabs @ref=Tabs>
            <Tabs>
                <RadzenTabsItem Text="Assign Members">

                    <div class="row">
                        <div class="col-3">
                            <h3>Databases</h3>
                            <AMDropdown Data=@Portfolios.ToDictionary(x => x, x => x.Name)                                       
                                        @bind-Value=@SelectedPortfolio/>
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="col-12">
                            <div class="row">
                                <div class="col-6">

                                    @if (SelectedPortfolio != null)
                                    {
                                        <h3>Users Assigned</h3>
                                        <UtilityGrid Data=@SelectedPortfolio.Users
                                                     RowSelectCallBack=@(args => RemoveUserFromDatabase((Data.Entities.Identity.UserAccount) args))
                                                     FileName=@GridNames.Admin.Users
                                                     AllowFiltering=true
                                                     AllowSorting=true/>

                                        <hr/>

                                        <h3>Users not assigned</h3>
                                        <UtilityGrid Data=@Users.Except(SelectedPortfolio.Users).ToList()
                                                     RowSelectCallBack=@(args => AddUserToDatabase((Data.Entities.Identity.UserAccount) args))
                                                     FileName=@GridNames.Admin.Users
                                                     AllowFiltering=true
                                                     AllowSorting=true/>
                                    }
                                </div>
                            </div>

                        </div>
                    </div>
                </RadzenTabsItem>

                <RadzenTabsItem Text="Migrations">

                    <div class="row">
                        <div class="col-3">
                            <h3>Databases</h3>
                            <AMDropdown Data=@Portfolios.ToDictionary(x => x, x => x.Name)
                                        @bind-Value=@SelectedPortfolio/>
                        </div>
                    </div>

                    <hr/>

                    <div class="row">
                        <div class="col-12">
                            <div class="row">
                                <div class="col my-2 mx-2">

                                    <h3>Migrations</h3>

                                    <AMproverCheckbox @bind-Value=@PerformRiskFmecaMigration
                                                      Label="Perform Risk Fmeca Migrations"/>

                                    <RadzenButton Text="Update Selected Database"
                                                  Click=@(async () => await UpdateSelectedDatabase())
                                                  Disabled=@(Loading || SelectedPortfolio == null)/>

                                    <RadzenButton Text="Update All Databases"
                                                  Click=@(async () => await UpdateAllDatabases())
                                                  Disabled=@Loading/>

                                    <div>
                                        @if (Output?.Any() == true)
                                        {
                                            <hr/>
                                            <h3>Output</h3>
                                        }

                                        @if (Output != null)
                                        {
                                            foreach (var message in Output)
                                            {
                                                <p>
                                                    @foreach (var text in message)
                                                    {
                                                        @text
                                                        <br/>
                                                    }
                                                </p>
                                            }
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </Authorized>
</AuthorizeView>
