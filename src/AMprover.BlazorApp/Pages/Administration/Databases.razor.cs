using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using AMprover.BusinessLogic;
using AMprover.Data.Infrastructure;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using AMprover.BusinessLogic.Helpers;
using AMprover.Data.Constants;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;
using AMprover.Data.Extensions;
using AMprover.BusinessLogic.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using AMprover.Data.Entities.Identity;
using Microsoft.Extensions.DependencyInjection;
using AMprover.Data.Repositories;
using AutoMapper;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Constants;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Pages.Administration;

public partial class Databases
{
    [Inject] private AuthenticationStateProvider AuthenticationStateProvider { get; set; }
    [Inject] private UserManager<UserAccount> UserManager { get; set; }
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IPortfolioManager PortfolioManager { get; set; }
    [Inject] private IConfiguration Config { get; set; }
    [Inject] private IServiceProvider ServiceProvider { get; set; }
    [Inject] private IMapper Mapper { get; set; }

    private readonly ILogger<Databases> _logger;
    private string _userName;

    private List<PortfolioModel> Portfolios { get; set; }
    private PortfolioModel SelectedPortfolio { get; set; }
    private bool PerformRiskFmecaMigration { get; set; }
    private List<UserAccount> Users { get; set; }
    private RadzenTabs Tabs { get; set; }
    private List<List<string>> Output { get; set; }
    private bool Loading { get; set; }

    public Databases()
    {
        _logger = LoggerFactory?.CreateLogger<Databases>();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        
        _userName = await AuthenticationStateProvider.GetLoggedInUsername();
        
        Portfolios = PortfolioManager.GetAll();
        SelectedPortfolio = Portfolios.FirstOrDefault();
        Users = [..UserManager.Users.ToList()];
        Users = SortUsers(Users);
        Output = [];

        foreach (var user in Users)
        {
            foreach (var portfolio in LoadPortfoliosForUser(user))
            {
                Portfolios.Find(x => x.Id == portfolio.Id)?.Users.Add(user);
            }
        }

        foreach (var portfolio in Portfolios)
            portfolio.Users = SortUsers(portfolio.Users);
    }

    private async Task UpdateAllDatabases()
    {
        Loading = true;
        Output = [];

        await Print("Starting Update all databases", true);

        foreach (var portfolio in Portfolios)
        {
            await Print("##########################################", true);
            await Print("");

            await UpdateDatabase(portfolio);
        }

        Loading = false;

        await Print("##########################################", true);
        await Print("Finished", true);
    }

    private List<PortfolioModel> LoadPortfoliosForUser(UserAccount user)
    {
        // The IPortfolioProvider is used often, and in DI added as Scoped. Might lead to concurrency
        // conflicts. As the risk is high when doing update, we need to ensure - for this administration purpose - we
        // have a fresh set of data. On other UIs it is fine to keep that Scoped instead of going to Transient globally.
        using var isolatedScope = ServiceProvider.CreateScope();
        var isolatedPortfolioProvider =
            isolatedScope.ServiceProvider.GetRequiredService<IPortfolioRepository>();

        return isolatedPortfolioProvider.GetForUser(user.Id).AsEnumerable().Select(Mapper.Map<PortfolioModel>).ToList();
    }

    private async Task UpdateSelectedDatabase()
    {
        Loading = true;
        Output = [];
        await Print("Starting Update Single Databases", true);

        await UpdateDatabase(SelectedPortfolio);

        Loading = false;
    }

    private void AddUserToDatabase(UserAccount user) =>
        AssignUserToDatabase(user, true);

    private void RemoveUserFromDatabase(UserAccount user) =>
        AssignUserToDatabase(user, false);

    private void AssignUserToDatabase(UserAccount user, bool assign)
    {
        PortfolioManager.Assign(user.Id, SelectedPortfolio.Id, assign);

        if (assign)
        {
            SelectedPortfolio.Users.Add(user);
            SelectedPortfolio.Users = SortUsers(SelectedPortfolio.Users);
            SelectedPortfolio.Users = [..SelectedPortfolio.Users];
            return;
        }

        SelectedPortfolio.Users.Remove(user);
        SelectedPortfolio.Users = [..SelectedPortfolio.Users];
    }

    private async Task UpdateDatabase(PortfolioModel portfolio)
    {
        try
        {
            await Print($"Updating: {portfolio.DatabaseName}");

            var connectionString = GetConnectionString(portfolio.DatabaseName);
            var optionsBuilder = new DbContextOptionsBuilder<AssetManagementDbContext>();
            optionsBuilder.UseSqlServer(connectionString);
            var context = new AssetManagementDbContext(optionsBuilder.Options);
            await EnsureUpdatedWithLatestMigrations(context, portfolio.DatabaseName);

            if (PerformRiskFmecaMigration)
            {
                await ResetRiskImportModelGrid(context);
                await PerformRiskFmecaSelectionsMigration(context);
            }
        }
        catch (Exception ex)
        {
            Output.Add([$"Failed to check Migrations for {portfolio}"]);

            while (ex != null)
            {
                Output.Last().Add($"-- {ex.Message}");
                ex = ex.InnerException;
            }
        }
    }

    private async Task EnsureUpdatedWithLatestMigrations(AssetManagementDbContext context, string databaseName)
    {
        await Print($"Checking {databaseName} for migrations");

        var pendingMigrations = (await context.Database.GetPendingMigrationsAsync()).ToList();

        foreach (var migration in pendingMigrations)
        {
            await Print($"Will apply: {migration}");
        }

        if (pendingMigrations.Any())
        {
            try
            {
                await context.Database.MigrateAsync();
                await Print($"Finished Migrating {databaseName}");
            }
            catch (Exception ex)
            {
                await Print($"Failed to apply migration: {ex.Message}");

                while (ex != null)
                {
                    await Print($"-- {ex.Message}");
                    ex = ex.InnerException;
                }
            }
        }
        else
        {
            await Print($"No Migrations for {databaseName}.");
        }
    }

    private string GetConnectionString(string databaseName)
    {
        var defaultConnString = Config.GetConnectionString(ConnectionstringConstants.DefaultConnectionStringName);

        if (string.IsNullOrWhiteSpace(defaultConnString))
        {
            throw new IndexOutOfRangeException(
                $"Missing connection string in configuration. Name: {ConnectionstringConstants.DefaultConnectionStringName}.");
        }

        var connStringObject = new SqlConnectionStringBuilder(defaultConnString)
        {
            InitialCatalog = databaseName
        };

        return connStringObject.ToString();
    }

    private static List<UserAccount> SortUsers(IEnumerable<UserAccount> users) => 
        users.OrderBy(ua => ua.UserName?.Split('@').Skip(1).FirstOrDefault()).ThenBy(ua => ua.UserName).ToList();

    private async Task PerformRiskFmecaSelectionsMigration(AssetManagementDbContext context)
    {
        var mrbs = context.Mrb
            .Include(x => x.RiskObject).ThenInclude(x => x.RiskObjFmeca)
            .ToList();

        var risks = mrbs
            .Select(Mapper.Map<RiskModel>)
            .ToList();

        var risksWithTooManyEffectColumns = 0;
        foreach (var risk in risks)
        {
            // For some databases there are a lot of corrupt Matrix Selections.
            // These grid will have multiple empty columns in it, we need to remove those.
            if(int.TryParse(risk.RiskObject.Fmeca.Data.FmecaMainGrid.EffectColumns, out var columnCount))
            {
                if(risk.MainDataGrid.TableColumns.Count > columnCount)
                {
                    risk.MainDataGrid.TableColumns.RemoveRange(columnCount, risk.MainDataGrid.TableColumns.Count - columnCount);
                    risksWithTooManyEffectColumns++;
                }
            }

            risk.FmecaSelections = risk.ToFmecaSelections();

            risk.MainDataGrid.CustomMtbfBefore = $"{risk.MtbfBefore:0.####}";
            risk.MainDataGrid.CustomMtbfAfter = $"{risk.MtbfAfter:0.####}";
            risk.MainDataGrid.CustomMtbfPmo = $"{risk.MtbfPmo:0.####}";

            var existingMrb = mrbs.Find(x => x.Mrbid == risk.Id);
            context.Update(Mapper.Map(risk, existingMrb));
        }

        if(risksWithTooManyEffectColumns > 0)
            await Print($"Removed Invalid Empty Fmeca Columns from {risksWithTooManyEffectColumns} Risks");

        await Print($"Updated Fmeca Selections on {risks.Count} Risks");
        await context.SaveChangesAndClearAsync(_userName);
    }

    private async Task ResetRiskImportModelGrid(AssetManagementDbContext context)
    {
        var gridColumns = context.LookupGridColumn.Where(x => x.ControlName == GridNames.Imports.Risks).ToList();
        context.LookupGridColumn.RemoveRange(gridColumns);
        await context.SaveChangesAndClearAsync(_userName);

        await Print($"Cleared {gridColumns.Count} invalid gridColumnSettings");
    }

    private Task Print(string text, bool lineBreak = false)
    {
        if(lineBreak)
        {
            Output.Add([text]);
        }
        else
        {
            Output.LastOrDefault()?.Add(text);
        }

        Tabs.Reload();
        return Task.Delay(1);
    }
}