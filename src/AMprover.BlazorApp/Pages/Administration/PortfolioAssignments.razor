﻿@page "/Administration/PortfolioAssignments/{UserId}"
@using AMprover.Data.Constants

<AuthorizeView Roles="@RoleConstants.Administrators">
    <Authorized>
        <h4>Portfolio assignments: @CounterT</h4>
        @foreach (var portfolio in AllPortfolios)
        {
            _index++;
            var componentName = "Checkbox" + _index;
            var isChecked = IsAssigned(portfolio.Id);
            <RadzenCheckBox TValue="bool" @bind-Value=@isChecked
                            Change=@(args => OnPortfolioAssignmentChanged((bool?)args, portfolio.Id))/>
            <RadzenLabel Text=@portfolio.Name Component="@componentName" Style="margin-left: 5px;"/>
            <br/>
        }
        <small class="text-muted">(un)checking a portfolio will be saved directly.</small>
    </Authorized>
</AuthorizeView>
