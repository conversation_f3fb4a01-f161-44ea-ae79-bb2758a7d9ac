using System;
using System.Collections.Generic;
using System.Linq;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models;
using AMprover.Data.Repositories;
using AutoMapper;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.Administration;

public partial class PortfolioAssignments
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private IMapper Mapper { get; set; }
    [Inject] private IPortfolioManager PortfolioManager { get; set; }
    [Inject] private IServiceProvider ServiceProvider { get; set; }

    [Parameter] public string UserId { get; set; }

    private string CounterT { get; set; }
    private List<PortfolioModel> AllPortfolios { get; set; }
    private List<PortfolioModel> UserPortfolios { get; set; }
    private int _index = 0;

    protected override void OnInitialized()
    {
        base.OnInitialized();
        AllPortfolios = PortfolioManager.GetAll().OrderBy(x => x.Name).ToList();
        LoadPortfoliosForUser();
    }

    private void LoadPortfoliosForUser()
    {
        // The IPortfolioProvider is used often, and in DI added as Scoped. Might lead to concurrency
        // conflicts. As the risk is high when doing update, we need to ensure - for this administration purpose - we
        // have a fresh set of data. On other UIs it is fine to keep that Scoped instead of going to Transient globally.
        using var isolatedScope = ServiceProvider.CreateScope();
        var isolatedPortfolioProvider = isolatedScope.ServiceProvider.GetRequiredService<IPortfolioRepository>();

        UserPortfolios = isolatedPortfolioProvider.GetForUser(UserId).AsEnumerable().Select(Mapper.Map<PortfolioModel>)
            .ToList();

        foreach (var portfolio in AllPortfolios)
        {
            portfolio.Checked = UserPortfolios.FirstOrDefault(x => x.Id == portfolio.Id)?.DatabaseName;
        }

        CounterT = $"{UserPortfolios.Count} / {AllPortfolios.Count}";
    }

    private void OnPortfolioAssignmentChanged(bool? value, int portfolioId)
    {
        PortfolioManager.Assign(UserId, portfolioId, value ?? false);
        LoadPortfoliosForUser();
    }

    private bool IsAssigned(int portfolioId)
    {
        return UserPortfolios.Any(p => p.Id == portfolioId);
    }
}