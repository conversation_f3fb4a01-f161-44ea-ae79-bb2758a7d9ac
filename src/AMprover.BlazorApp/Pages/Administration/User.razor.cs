using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using DeepCopy;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.JSInterop;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Enums;

namespace AMprover.BlazorApp.Pages.Administration;

public partial class User
{
    [Inject] private IJSRuntime JsRuntime { get; set; }
    [Inject] private UserManager<UserAccount> UserManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private IServiceProvider ServiceProvider { get; set; }

    [Parameter] public string UserId { get; set; }

    public UserAccount UserAccount { get; set; }

    /// <summary>
    /// Clone of the object we're editing to be able to 'reset to initial values'
    /// </summary>
    private UserAccount BeforeEditingUserAccount { get; set; }
    private string BeforeEditingUserAccountRole { get; set; }

    /// Options to display in the roles dropdown when editing a user
    public Dictionary<string, string> AvailableRoles { get; set; } = AMproverIdentityConstants.Roles.GetAll();

    public string CurrentRole { get; set; }

    public string ValidationError { get; set; }

    public string ValidationErrorPassword { get; set; }

    public string NewPassword { get; set; }

    public EntityEditorMode EditorMode
    {
        get
        {
            var userGuid = Guid.Parse(UserId);
            return userGuid == Guid.Empty ? EntityEditorMode.Create : EntityEditorMode.Update;
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadEditor();
    }

    private async Task LoadEditor()
    {
        NewPassword = null;
        switch (EditorMode)
        {
            case EntityEditorMode.Update:
                await DetermineUserAndCurrentRole();
                break;
            default:
                UserAccount = new UserAccount { Id = UserId };
                CurrentRole = RoleConstants.Default;
                break;
        }

        BeforeEditingUserAccount = DeepCopier.Copy(UserAccount);
        BeforeEditingUserAccountRole = CurrentRole;
    }

    private async Task DetermineUserAndCurrentRole()
    {
        // The UserManager is put into DI with .Scoped. Blazor is working differently, so DB concurrency errors might occur
        // Workaround is to (re)get a fresh record, update the properties you can change via the UI, and update it back. 
        using var isolatedScope = ServiceProvider.CreateScope();
        var isolatedUserManager = isolatedScope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();

        var freshUserAccount = await isolatedUserManager.FindByIdAsync(UserId);
        UserAccount = freshUserAccount;

        var roles = await isolatedUserManager.GetRolesAsync(freshUserAccount);

        if (roles.Contains(RoleConstants.Administrators))
            CurrentRole = RoleConstants.Administrators;
        else if (roles.Contains(RoleConstants.ReadOnly))
            CurrentRole = RoleConstants.ReadOnly;
        else
            CurrentRole = RoleConstants.Default;
    }

    public void Cancel()
    {
        UserAccount.Name = BeforeEditingUserAccount.Name;
        UserAccount.UserName = BeforeEditingUserAccount.UserName;
        UserAccount.Email = BeforeEditingUserAccount.Email;
        CurrentRole = BeforeEditingUserAccountRole;

        NavigateToOverview();
    }

    public async Task UpdateUserAccountValidFormSubmitted(EditContext editContext)
    {
        ValidationError = null;

        var updatedUserAccountViaUi = (UserAccount)editContext.Model;
        // We enforce the e-mail equals the username, both in lowercase.
        updatedUserAccountViaUi.UserName = updatedUserAccountViaUi.UserName.ToLower();
        updatedUserAccountViaUi.Email = updatedUserAccountViaUi.UserName;

        IdentityResult dbUpdateResult;

        switch (EditorMode)
        {
            case EntityEditorMode.Update:
                dbUpdateResult = await UpdateUserToDatabase(updatedUserAccountViaUi);
                if (CanContinueAfterDatabaseChanges(dbUpdateResult))
                {
                    await HandleRoleAssignmentUpdate();
                    NavigateToOverview(); // Go back to user overview, nothing to do left
                }
                break;
            case EntityEditorMode.Create:
                dbUpdateResult = await CreateUserToDatabase(updatedUserAccountViaUi);
                if (CanContinueAfterDatabaseChanges(dbUpdateResult))
                {
                    UserId = updatedUserAccountViaUi.Id;
                    await HandleRoleAssignmentUpdate();

                    NavigateToUser(updatedUserAccountViaUi.Id); // After creating the account, an ID has been assigned, can't use this.UserId (=0000-000...)
                }
                break;
        }
    }

    public void UpdateUserAccountInvalidFormSubmitted(EditContext editContext)
    {
        ValidationError = "Please check the form for errors.";
    }

    private async Task<IdentityResult> UpdateUserToDatabase(UserAccount updatedUserAccountViaUi)
    {
        // The UserManager is put into DI with .Scoped. Blazor is working differently, so DB concurrency errors might occur
        // Workaround is to (re)get a fresh record, update the properties you can change via the UI, and update it back. 
        var freshUserFromDbToUpdate = await UserManager.FindByIdAsync(UserId);
        freshUserFromDbToUpdate.Name = updatedUserAccountViaUi.Name;
        freshUserFromDbToUpdate.UserName = updatedUserAccountViaUi.UserName;
        freshUserFromDbToUpdate.Email = updatedUserAccountViaUi.Email;
        freshUserFromDbToUpdate.Company = updatedUserAccountViaUi.Company;

        var updateResult = await UserManager.UpdateAsync(freshUserFromDbToUpdate);

        return updateResult;
    }

    private async Task<IdentityResult> CreateUserToDatabase(UserAccount newUserAccountViaUi)
    {
        using var isolatedScope = ServiceProvider.CreateScope();
        var isolatedUserManager = isolatedScope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();

        var createResult = await isolatedUserManager.CreateAsync(newUserAccountViaUi, newUserAccountViaUi.PasswordHash);

        return createResult;
    }

    private bool CanContinueAfterDatabaseChanges(IdentityResult dbUpdateResult)
    {
        if (dbUpdateResult.Succeeded)
            return true;

        ValidationError = dbUpdateResult.Errors.Any() ? string.Join('+', dbUpdateResult.Errors.Select(err => err.Description)) : "Error updating user";
        return false;
    }

    private async Task HandleRoleAssignmentUpdate()
    {
        using var isolatedScope = ServiceProvider.CreateScope();
        var isolatedUserManager = isolatedScope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();

        var freshDbUserAccount = await isolatedUserManager.FindByIdAsync(UserId);
        var roles = await isolatedUserManager.GetRolesAsync(freshDbUserAccount);

        var isAdministrator = roles.Contains(RoleConstants.Administrators);
        var isReadOnly = roles.Contains(RoleConstants.ReadOnly);

        switch (CurrentRole)
        {
            // Default user means not part of Admin Role and Not part of ReadOnly Role
            case RoleConstants.Default:
                if (isAdministrator)
                    await isolatedUserManager.RemoveFromRoleAsync(freshDbUserAccount, RoleConstants.Administrators);

                if (isReadOnly)
                    await isolatedUserManager.RemoveFromRoleAsync(freshDbUserAccount, RoleConstants.ReadOnly);
                break;

            // Administrator has Admin Role and does not have ReadOnly Role
            case RoleConstants.Administrators:
                if (!isAdministrator)
                    await isolatedUserManager.AddToRoleAsync(freshDbUserAccount, RoleConstants.Administrators);

                if (isReadOnly)
                    await isolatedUserManager.RemoveFromRoleAsync(freshDbUserAccount, RoleConstants.ReadOnly);
                break;

            // ReadOnly has ReadOnly Role, and does not have Admin Role
            case RoleConstants.ReadOnly:
                if (isAdministrator)
                    await isolatedUserManager.RemoveFromRoleAsync(freshDbUserAccount, RoleConstants.Administrators);

                if (!isReadOnly)
                    await isolatedUserManager.AddToRoleAsync(freshDbUserAccount, RoleConstants.ReadOnly);
                break;
        }
    }

    public async Task UpdatePassword()
    {
        using (var isolatedScope = ServiceProvider.CreateScope())
        {
            var isolatedUserManager = isolatedScope.ServiceProvider.GetRequiredService<UserManager<UserAccount>>();

            if (!string.IsNullOrWhiteSpace(NewPassword))
            {
                var freshUserAccount = await isolatedUserManager.FindByIdAsync(UserId);
                var resetToken = await isolatedUserManager.GeneratePasswordResetTokenAsync(freshUserAccount);
                var userAdjustmentResult = await isolatedUserManager.ResetPasswordAsync(freshUserAccount, resetToken, NewPassword);
                if (userAdjustmentResult.Succeeded == false)
                {
                    ValidationErrorPassword = userAdjustmentResult.Errors.Any() ? string.Join('+', userAdjustmentResult.Errors.Select(err => err.Description)) : "Error updating password";
                    return;
                }
            }
        }

        //reset property to initial value
        NewPassword = null;

        //go back to user overview
        NavigateToOverview();
    }

    public async Task DeleteUser()
    {
        // TODO: Makes this a nice Blazorize UI confirmation dialog -- maybe even a generic one.
        var confirmDelete = await JsRuntime.InvokeAsync<bool>("confirm", "Do you want to delete it?");
        if (confirmDelete)
        {
            // Get the user
            var targetUser = await UserManager.FindByIdAsync(UserId);
            if (targetUser != null)
            {
                try
                {
                    // Delete the user
                    await UserManager.DeleteAsync(targetUser);
                }
                finally
                {
                    // Refresh Users
                    NavigateToOverview();
                }
            }
        }
    }

    public void NavigateToOverview()
    {
        NavigationManager.NavigateTo("/administration/users");
    }

    private void NavigateToUser(string userId)
    {
        NavigationManager.NavigateTo($"/administration/users/{userId}");
    }
}
