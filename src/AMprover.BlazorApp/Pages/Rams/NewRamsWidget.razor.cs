using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.Rams;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Radzen;
using System;
using System.Collections.Generic;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Models.RiskAnalysis;

namespace AMprover.BlazorApp.Pages.Rams;

public partial class NewRamsWidget
{
    [Inject] private ILogger<NewRamsWidget> Logger { get; set; }
    [Inject] private IScenarioManager ScenarioManager { get; set; }
    [Inject] private IRamsManager RamsManager { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IStringLocalizer<NewRamsWidget> Localizer { get; set; }

    [Parameter] public Action<int?> CallBack { get; set; }

    private List<ScenarioModel> Scenarios { get; set; }

    public NewRamsModel NewRams { get; } = new();

    public bool IsBusy { get; set; }

    protected override void OnInitialized()
    {
            Scenarios = ScenarioManager.GetAllScenarios();
        }

    private void ValidTaskSubmitted(NewRamsModel rams)
    {
            var generatedGuid = Guid.NewGuid();

            //Generate content model and add initial container
            var content = new RamsDiagramContentModel
            {
                ParallelTracks = 1,
                Parts =
                [
                    new RamsComponentModel
                    {
                        Id = generatedGuid,
                        Type = RamsComponentType.Container,
                        Title = "[New container]",
                        Changed = DateTime.Now,
                        Dept = 1
                    }
                ]
            };

            var rootContainer = new RamsModel
            {
                NodeId = generatedGuid,
                Container = true,
                Descr = "[New container]",
                Name = "[New container]",
                Mtbftechn = 1,
                Mtbffunct = 1,
                LabdaFunctional = 1,
                LabdaTechnical = 1,
                WeibullShape = 1,
                CharacteristicLife = 1
            };

            var diagram = RamsManager.CreateOrEditRamsDiagram(new RamsDiagramModel
            {
                Name = NewRams.Name,
                ScenId = NewRams.ScenarioId,
                AvailableTime = 8760D,
                TestInterval = 1000,
                Horizon = 1,
                CalculateAvailability = true,
                PeriodFrom = 0,
                PeriodTo = 1,
                Serialized = JsonConvert.SerializeObject(content),
                Rams = [rootContainer]
            });

            rootContainer.DiagramId = diagram.Id;
            RamsManager.CreateOrEditRams(rootContainer);

            CallBack?.Invoke(diagram.Id);
            DialogService.Close();
        }

    private void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
            Logger.LogError($"Invalid form submitted in {nameof(NewRamsWidget)}");
            Logger.LogError(JsonConvert.SerializeObject(args));
        }
}