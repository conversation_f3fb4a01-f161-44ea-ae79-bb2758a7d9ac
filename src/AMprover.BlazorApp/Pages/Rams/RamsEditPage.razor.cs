using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BlazorApp.Components.Rams;
using AMprover.BlazorApp.Helpers;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.Rams.Converters;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using DeepCopy;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using NuGet.Packaging;
using Radzen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Models.Rams.Undo;
using EnumExtensions = AMprover.BusinessLogic.Extensions.EnumExtensions;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BlazorApp.Pages.Rams
{
    public partial class RamsEditPage
    {
        [Inject] private IDropdownManager DropdownManager { get; set; }
        [Inject] private ILCCManager LccManager { get; set; }
        [Inject] private JavaScriptHelper JavaScriptHelper { get; set; }
        [Inject] private IRamsManager RamsManager { get; set; }
        [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
        private readonly SemaphoreSlim _semaphore = new(10);
        [Inject] private DialogService DialogService { get; set; }
        [Inject] private ILoggerFactory LoggerFactory { get; set; }
        [Inject] private ILookupManager LookupManager { get; set; }
        [Inject] private ILogger<RamsEditPage> Logger { get; set; }

        private List<Guid> _lastMovedComponents = [];
        private UtilityGrid<RamsModel> RamsGrid;

        [Inject] public IStringLocalizer<RamsEditPage> Localizer { get; set; }

        [Inject] public IGlobalDataService GlobalDataService { get; set; }

        private string Language => GlobalDataService.Language;
        public bool DisableAddButton { get; set; }
        public bool NewSelection { get; set; }
        private int? SelectedDiagram { get; set; }
        public RamsDiagramModel CurrentDiagram { get; private set; }
        private RadzenTabs TreeTab { get; set; }
        private RadzenTabs EditorTab { get; set; }
        public RadzenTabsItem EditorTabGroup { get; set; }
        public RadzenTabsItem EditorTabBlock { get; set; }
        private RadzenTabs BlockContentTab { get; set; }
        private RadzenTabs ContainerContentTab { get; set; }

        private bool DisableGroupTab { get; set; } = true;
        private bool DisableBlockTab { get; set; } = true;
        private bool ShowDiagram { get; set; } = true;

        private bool ShowDisplayMode { get; set; }

        private bool ShowHidePanelMode { get; set; }

        private int ShownValues { get; set; } = 1;
        private int ShownNotation { get; set; } = 1;

        private int ShownFunctions { get; set; } = 1;
        private int ShownCalculation { get; set; } = 4;
        private int ShownReliability { get; set; } = 2;

        private int SelectedTabIndex { get; set; } = 1;
        public RamsComponentModel SelectedConnectionDestination { get; set; }
        public TreeGeneric<RamsTreeObject> RamsTree { get; set; } = new();
        private List<RamsModel> RamsComponents { get; set; } = [];
        private Guid? SelectedComponent { get; set; }
        private RamsModel SelectedNode { get; set; }
        private RamsModel SelectedGroup { get; set; }
        public RamsComponentModel SelectedItem { get; private set; }
        public RamsComponentModel CopiedItem { get; set; }
        private RamsModel CopiedRamsItem { get; set; }
        private Dictionary<int, string> ScenarioDict { get; set; } = new();
        private Dictionary<int, string> RiskObjectDict { get; set; } = new();
        private Dictionary<int, string> AssetDict { get; set; } = new();
        private Dictionary<int, string> BlockStatusDict { get; set; } = new();
        private Dictionary<int, string> LinkMethodDict { get; set; } = new();
        private Dictionary<int, string> LinkTypeDict { get;  set; } = new();
        public Dictionary<int, string> Risks { get; private set; } = new();
        public Dictionary<int, string> Objects { get; private set; } = new();
        private Dictionary<int, string> XooNData { get; } = new();
        private RamsDiagramContentModel DiagramContent { get; set; } = new();
        public RamsDiagramComponent DiagramComponent { get; set; }
        private bool DisableBlockEditing { get; set; }

        private RamsDisplayModel DisplayMode { get;  set; } = new()
        {
            ShowFunctional = false,
            ShowMtbf = true,
            ShowLambda = false,
            ShowPfd = false,
            ShowSil = false,
            ShowSilAc = false,
            ShowYears = true
        };

        private bool LinkSelectionMethodActive { get; set; }

        private bool DisabledDropdowns { get;  set; } = true;

        private LookupSettingModel RamsAllowedDeptSetting { get;  set; }

        private LookupSettingModel RamsTotalAllowedBlocksSetting { get; set; }

        public List<TreeNodeGeneric<RamsTreeObject>> Diagrams { get; private set; }

        public RadzenSplitButton DiagramButton { get; set; }

        public TreeGeneric<RiskTreeObject> RiskTree { get; } = new();

        public LimitedList<UndoEntry> UndoList { get; private set; }

        private string MtbfLabel => DisplayMode.ShowYears
            ? $"MTBF ({Localizer["RamsYearTxt"]})"
            : $"MTBF ({Localizer["RamsHourTxt"]})";

        protected override Task OnInitializedAsync()
        {
            //use this list to store changes that can be undone
            UndoList = new LimitedList<UndoEntry>(10);

            //Clean selection
            SelectedComponent = null;
            SelectedItem = null;
            SelectedGroup = null;
            SelectedNode = null;
            DisableBlockTab = true;
            DisableGroupTab = true;
            SelectedTabIndex = 0;
            DisabledDropdowns = false;

            //Load RAMS settings
            RamsAllowedDeptSetting = LookupManager.GetLookupSettingByPropertyName(PropertyNames.RamsAllowedDept);
            RamsTotalAllowedBlocksSetting =
                LookupManager.GetLookupSettingByPropertyName(PropertyNames.RamsTotalAllowedBlocks);

            //Load dropdowns
            ScenarioDict = DropdownManager.GetScenarioDict();
            AssetDict = DropdownManager.GetAssetDict();
            BlockStatusDict = EnumExtensions.GetEnumDictionary<RamsBlockStatus>([3, 5, 8]);
            LinkMethodDict = EnumExtensions.GetEnumDictionary<RamsLinkMethod>([1, 2, 9, 10, 17, 18, 999]);
            LinkTypeDict = EnumExtensions.GetEnumDictionary<RamsLinkType>([2, 3]);

            Objects = DropdownManager.GetObjectsDict(ObjectLevel.System);
            Risks = DropdownManager.GetRisksDict();

            //fill tree with scenario's and rams diagrams
            RamsTree.Initialize(RamsManager.GetRamsDiagramTreeByScenario());

            //Load diagrams for adding a diagram
            Diagrams = RamsTree.GetFlattenedNodes();

            if (SelectedDiagram != null && SelectedDiagram != 0)
            {
                var selectedNode = Diagrams.Find(x => x.Id == SelectedDiagram);
                RamsTree.SelectNode(selectedNode);
                UpdateTreeAndDiagram();
            }

            return Task.CompletedTask;
        }

        private void OpenNewRamsWidget()
        {
            CleanSelections();
            RemoveLines();

            DialogService.Open<NewRamsWidget>
            ("New RAMS diagram",
                new Dictionary<string, object>
                {
                    {nameof(NewRamsWidget.CallBack), new Action<int?>(UpdateTreeAndDiagram)}
                });
        }

        private void CleanSelections()
        {
            SelectedDiagram = null;
            CurrentDiagram = new RamsDiagramModel();
            SelectedComponent = null;
            SelectedNode = null;
            SelectedGroup = null;
            SelectedItem = null;
            SelectedTabIndex = 0;
            DiagramContent = new RamsDiagramContentModel();
            DiagramComponent = null;
            DisableBlockTab = true;
            DisableGroupTab = true;
        }

        #region Tree methods

        private void UpdateTreeAndDiagram(int? nodeId = null)
        {
            CleanSelections();
            RamsTree.Initialize(RamsManager.GetRamsDiagramTreeByScenario());

            var newSelectedNode = nodeId == null
                ? RamsTree.GetFlattenedNodes().MaxBy(x => x.Id)
                : RamsTree.GetFlattenedNodes().Find(x => x.Id == nodeId);
            if (newSelectedNode == null) return;

            SelectedDiagram = newSelectedNode.Id;

            CurrentDiagram = RamsManager.GetRamsDiagramContent(SelectedDiagram.Value);
            CurrentDiagram = RamsManager.RecalculateDiagram(CurrentDiagram,
                new CalculationParameters(CurrentDiagram),
                new CalculationParameters(CurrentDiagram),
                DisplayMode, false, GlobalDataService.Language);

            RamsTree.SelectNode(newSelectedNode);
            RamsComponents = CurrentDiagram.Rams ?? [];

            Load();
            TreeTab?.Reload();
            EditorTab?.Reload();
            BlockContentTab?.Reload();
            ContainerContentTab?.Reload();

            SelectedTabIndex = 0;
            CleanDiagram();
        }

        public async Task ClickTreeNode(TreeNodeGeneric<RamsTreeObject> node)
        {
            NewSelection = true;
            SelectedComponent = null;
            SelectedItem = null;
            SelectedGroup = null;
            SelectedNode = null;
            DisableBlockTab = true;
            DisableGroupTab = true;
            SelectedTabIndex = 0;
            DisabledDropdowns = false;

            if (node.Source.Id == 0)
            {
                if (node.Nodes.Exists(x => x.Source.Id > 0))
                {
                    node = node.Nodes.Find(x => x.Source.Id > 0);
                    SelectedDiagram = node?.Source.Id;
                }
            }
            else
            {
                SelectedDiagram = node.Source.Id;
            }

            if (SelectedDiagram != null)
            {
                CurrentDiagram = RamsManager.GetRamsDiagramContent(SelectedDiagram.Value);

                try
                {
                    using var stream = new MemoryStream(Encoding.UTF8.GetBytes(CurrentDiagram.Serialized));
                    using var reader = new StreamReader(stream);
                    await using var jsonReader = new JsonTextReader(reader);
                    var serializer = JsonSerializer.Create(new JsonSerializerSettings
                    {
                        Culture = new CultureInfo(GlobalDataService.Language)
                    });
                    DiagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
                }
                catch
                {
                    Logger.LogError("Cannot convert diagram to diagram content for {CurrentDiagramId}",
                        CurrentDiagram.Id);
                }

                //If serialized data is not a real diagram representation regenerate it based on the rams containers and blocks.
                if (DiagramContent == null || !DiagramContent.Parts.Any())
                {
                    var migrationResult = await ProcessMigration();

                    if (migrationResult)
                    {
                        using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(CurrentDiagram.Serialized)))
                        using (var reader = new StreamReader(stream))
                        await using (var jsonReader = new JsonTextReader(reader))
                        {
                            var serializer = JsonSerializer.Create(new JsonSerializerSettings
                            {
                                Culture = new CultureInfo(GlobalDataService.Language)
                            });
                            DiagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
                        }

                        await ReOrderComponents(DiagramContent.Parts);
                        CurrentDiagram.Serialized = JsonConvert.SerializeObject(DiagramContent);

                        CurrentDiagram = RamsManager.RecalculateDiagram(CurrentDiagram,
                            new CalculationParameters(CurrentDiagram),
                            new CalculationParameters(CurrentDiagram),
                            DisplayMode, true, GlobalDataService.Language);
                    }
                }
                else
                {
                    CurrentDiagram = RamsManager.RecalculateDiagram(CurrentDiagram,
                        new CalculationParameters(CurrentDiagram),
                        new CalculationParameters(CurrentDiagram),
                        DisplayMode, false, GlobalDataService.Language);
                }

                if (CurrentDiagram.RiskObject.HasValue)
                    RiskTree.Initialize(
                        RiskAnalysisManager.GetRisksTreeNodeByRiskObject(CurrentDiagram.RiskObject.Value));

                RamsTree.SelectNode(node);
                RamsComponents = CurrentDiagram.Rams ?? [];
            }

            if (CurrentDiagram?.ScenId != null)
                RiskObjectDict = DropdownManager.GetRiskObjectDictByScenarioId(CurrentDiagram.ScenId.Value);

            await Load();
            await CleanDiagram();
        }

        public void ClickRiskTreeNode(TreeNodeGeneric<RiskTreeObject> node)
        {
            RamsManager.SwitchFunctionalObject(node, SelectedItem,
                SelectedItem.Type == RamsComponentType.Block ? SelectedNode : SelectedGroup);

            SaveRamsComponent(SelectedItem.Type == RamsComponentType.Block ? SelectedNode : SelectedGroup, true,
                true);
        }

        #endregion

        #region Diagram methods

        public void AddCustomConnection()
        {
            SelectedItem.CustomConnectors.Add(new RamsComponentConnectorModel
            {
                Destination = SelectedConnectionDestination.Id,
                LocationDestination = RamsComponentConnectorLocation.left,
                LocationSource = RamsComponentConnectorLocation.right,
                TypeDestination = SelectedConnectionDestination.Type
            });
        }

        public void SelectComponent(RamsComponentModel selectedItem)
        {
            DisabledDropdowns = true;
            XooNData.Clear();

            //Go into linking random objects
            if (LinkSelectionMethodActive)
            {
                selectedItem.CustomConnectors.Add(new RamsComponentConnectorModel
                {
                    Destination = SelectedItem.Id,
                    TypeDestination = SelectedItem.Type,
                    LocationSource = RamsComponentConnectorLocation.left,
                    LocationDestination = RamsComponentConnectorLocation.right
                });

                SaveRamsDiagram();
                LinkSelectionMethodActive = false;
            }
            //Normal select/deselect behavior
            else
            {
                //Deselect item
                if (SelectedComponent == selectedItem.Id)
                {
                    SelectedComponent = null;
                    SelectedItem = null;
                    SelectedGroup = null;
                    SelectedNode = null;
                    DisableBlockTab = true;
                    DisableGroupTab = true;
                    SelectedTabIndex = 0;
                    DisabledDropdowns = false;
                    return;
                }

                SelectedComponent = selectedItem.Id;

                switch (selectedItem.Type)
                {
                    //Select node/block
                    case RamsComponentType.Block:
                        SelectedNode = CurrentDiagram.Rams.Find(x => x.NodeId == selectedItem.Id);

                        //If it cant be found try to reload the RAMS collection
                        if (SelectedNode == null)
                        {
                            CurrentDiagram.Rams = RamsManager.GetRams(CurrentDiagram.Id);
                            SelectedNode = CurrentDiagram.Rams.Find(x => x.NodeId == selectedItem.Id);
                        }

                        SelectedItem = selectedItem;
                        SelectedGroup = null;
                        DisableGroupTab = true;
                        DisableBlockTab = false;
                        SelectedTabIndex = 2;

                        if (SelectedNode.RiskId.HasValue)
                        {
                            var selectedTreeNode = RiskTree.GetFlattenedNodes()
                                .Find(x => x.Source.RiskId == SelectedNode.RiskId);

                            if (selectedTreeNode != null)
                                RiskTree.SelectNode(selectedTreeNode);

                            if (selectedTreeNode == null) return;
                            var parent = selectedTreeNode.Parent;
                            while (parent != null)
                            {
                                parent.Open = true;
                                parent = parent.Parent;
                            }
                        }

                        DisableBlockEditing = DisableBlockEditingCheck();
                        break;
                    //Select group/container
                    case RamsComponentType.Container:
                        SelectedGroup = CurrentDiagram.Rams.Find(x => x.NodeId == selectedItem.Id);

                        if (SelectedGroup == null)
                            return;

                        SelectedItem = selectedItem;
                        SelectedNode = null;
                        DisableBlockTab = true;
                        DisableGroupTab = false;
                        SelectedTabIndex = 1;

                        if (SelectedGroup.ObjectId.HasValue)
                        {
                            var selectedTreeNode = RiskTree.GetFlattenedNodes()
                                .Find(x => x.Source.SystemId == SelectedGroup.ObjectId);

                            if (selectedTreeNode != null)
                                RiskTree.SelectNode(selectedTreeNode);

                            if (selectedTreeNode == null) return;
                            var parent = selectedTreeNode.Parent;
                            while (parent != null)
                            {
                                parent.Open = true;
                                parent = parent.Parent;
                            }
                        }

                        XooNData.AddRange(selectedItem.Parts
                            .DistinctBy(x => x.ParallelTrack)
                            .Select((x, index) => new {Track = x.ParallelTrack, Index = index + 1})
                            .DistinctBy(item => item.Track)
                            .OrderBy(item => item.Index)
                            .ToDictionary(item => item.Index, item => item.Index.ToString())
                            .ToList());

                        break;
                }
            }

            DisabledDropdowns = false;
        }

        /// <summary>
        ///     Used when switching between diagrams
        /// </summary>
        private async Task Load()
        {
            if (CurrentDiagram?.Serialized == null)
            {
                DiagramContent = new RamsDiagramContentModel();
                return;
            }

            using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(CurrentDiagram.Serialized)))
            using (var reader = new StreamReader(stream))
            await using (var jsonReader = new JsonTextReader(reader))
            {
                var serializer = JsonSerializer.Create(new JsonSerializerSettings
                {
                    Culture = new CultureInfo(GlobalDataService.Language),
                    Converters = new List<JsonConverter>
                    {
                        new InfinityConverter()
                    }
                });
                DiagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
            }

            var filteredRams = CurrentDiagram.Rams
                .Where(ram => ram.PartOf == 0 || CurrentDiagram.Rams.Exists(r => r.PartOf == ram.Id)).ToList();
            var countComponents = CountComponents(DiagramContent?.Parts);
            if (countComponents < filteredRams.Count - 1 && CurrentDiagram.Rams.All(x => x.PartOf != -1))
            {
                var generatedContent = new RamsDiagramContentModel();
                var rootNode = CurrentDiagram.Rams.Find(x => x.Container && x.PartOf == 0);

                if (rootNode != null)
                {
                    var rootComponent = new RamsComponentModel
                    {
                        Id = rootNode.NodeId,
                        Title = rootNode.Name.Trim(),
                        Order = 1,
                        Type = RamsComponentType.Container,
                        Changed = rootNode.DateModified ?? DateTime.Now
                    };
                    generatedContent.Parts.Add(rootComponent);
                    ProcessRegenerationChildren(rootNode, rootComponent, CurrentDiagram.Rams);

                    DiagramContent = generatedContent;

                    await SaveAndReorderRamsDiagram(null, false);
                }
            }

            //Check if configuration limit has been reached
            if (RamsTotalAllowedBlocksSetting?.IntValue != null)
                DisableAddButton = countComponents >= RamsTotalAllowedBlocksSetting.IntValue;
        }

        private static void ProcessRegenerationChildren(RamsModel parent, RamsComponentModel parentComponent,
            List<RamsModel> availableNodes)
        {
            var children = availableNodes.Where(x => x.PartOf == parent.Id);

            var order = 1;
            foreach (var child in children.OrderBy(x => x.DateModified))
            {
                var childComponent = new RamsComponentModel
                {
                    Id = child.NodeId,
                    Title = child.Name,
                    Order = order,
                    Type = availableNodes.Exists(x => x.PartOf == child.Id)
                        ? RamsComponentType.Container
                        : RamsComponentType.Block,
                    Changed = child.DateModified ?? DateTime.Now,
                    GroupId = parent.NodeId
                };

                parentComponent.Parts.Add(childComponent);

                if (childComponent.Type == RamsComponentType.Container)
                    ProcessRegenerationChildren(child, childComponent, availableNodes);

                order++;
            }
        }

        private async Task<bool> ProcessMigration()
        {
            //Build content if non-existent
            if (DiagramContent == null || !DiagramContent.Parts.Any())
                DiagramContent = new RamsDiagramContentModel();

            //Get serialized model AMprover 4
            MigrationModel.Root oldDiagram;
            using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(CurrentDiagram.Serialized)))
            using (var reader = new StreamReader(stream))
            await using (var jsonReader = new JsonTextReader(reader))
            {
                var serializer = JsonSerializer.Create(new JsonSerializerSettings
                {
                    Culture = new CultureInfo(GlobalDataService.Language)
                });
                oldDiagram = serializer.Deserialize<MigrationModel.Root>(jsonReader);
            }

            //First get root container
            var rootContainer = CurrentDiagram.Rams.FirstOrDefault(x => x.PartOf == -1);

            if (rootContainer == null)
                return false;

            ShowLoadingDialog();

            var ramsComponentModel = new RamsComponentModel
            {
                Id = Guid.NewGuid(),
                Title = rootContainer.Name.Trim(),
                Order = 1,
                Type = RamsComponentType.Container,
                Changed = DateTime.Now,
                ParallelTrack = 1,
                Dept = 1
            };

            rootContainer.NodeId = ramsComponentModel.Id;
            rootContainer.PartOf = 0;

            RamsManager.CreateOrEditRams(rootContainer);

            DiagramContent.Parts.Add(ramsComponentModel);

            //Find old ID which is only stored in schema. 
            var id = FindIdFromOldDiagramByName(oldDiagram, rootContainer.Name, oldDiagram.ID, new List<int>());

            //Do nested children generation based on the actual existing relationship of parentId, groupId
            ProcessMigrationChildren(rootContainer, ramsComponentModel, oldDiagram, id);

            await SaveRamsDiagram(true, true, false);

            ShowCompleteDialog("Finished converting AMProver4 diagram.");
            return true;
        }

        private void ProcessMigrationChildren(RamsModel parent, RamsComponentModel parentComponent,
            MigrationModel.Root oldDiagram, int id)
        {
            //Get rams items but exclude self
            var children = RamsManager.GetRamsItemByParent(CurrentDiagram.Id, id).Where(x => x.Id != parent.Id)
                .OrderBy(x => x.ReadSequence).ToList();

            var order = 1;
            var isParallel = parent.ParallelBlocks == children.Count;
            var parallelTrack = 1;
            var itemsToSkip = new List<int>();

            foreach (var child in children.OrderBy(x => x.DateModified))
            {
                var childComponent = new RamsComponentModel
                {
                    Id = Guid.NewGuid(),
                    Title = child.Name.Trim(),
                    Order = order,
                    ParallelTrack = parallelTrack,
                    Type = child.Container
                        ? RamsComponentType.Container
                        : RamsComponentType.Block,
                    Changed = child.DateModified ?? DateTime.Now,
                    GroupId = parent.NodeId,
                    Results = new RamsReliabilityResultsModel
                    {
                        MtbfFunctional = child.Mtbffunct ?? 1,
                        MtbfTechnical = child.Mtbftechn ?? 1,
                        Mttr = child.Mttr ?? 0,
                        AvailFunctional = child.AvailabilityInput ?? 0,
                        AvailTechnical = child.AvailabilityOutput ?? 0
                    }
                };

                //Add new nodeId to rams item
                child.NodeId = childComponent.Id;
                //Reset part of because AMprover4 bases this not on the table prop but on a loosly coupled json model
                var originalPartOf = child.PartOf;
                child.PartOf = parent.Id;

                RamsManager.CreateOrEditRams(child);

                parentComponent.Parts.Add(childComponent);
                parentComponent.ParallelTracks = isParallel ? children.Count : 1;

                if (childComponent.Type == RamsComponentType.Container)
                {
                    id = FindIdFromOldDiagramByName(oldDiagram, child.Name, originalPartOf, itemsToSkip);
                    itemsToSkip.Add(id);
                    ProcessMigrationChildren(child, childComponent, oldDiagram, id);
                }

                switch (isParallel)
                {
                    case false:
                        order++;
                        break;
                    case true:
                        parallelTrack++;
                        break;
                }
            }
        }

        private static int FindIdFromOldDiagramByName(MigrationModel.Root oldDiagram, string name, int? startingId,
            ICollection<int> itemsToSkip)
        {
            return oldDiagram.Nme == name
                ? oldDiagram.ID
                : FindIdRecursive(oldDiagram.Bs, name, oldDiagram.ID, startingId, itemsToSkip);
        }

        private static int FindIdRecursive(List<MigrationModel.B> items, string name, int parentId, int? startingId,
            ICollection<int> itemsToSkip)
        {
            if (items == null || !items.Any())
                return -1; // If name is not found in this branch

            foreach (var nestedItem in items)
            {
                if (parentId == startingId)
                    if (nestedItem.Nme == name && !itemsToSkip.Contains(nestedItem.ID))
                        return nestedItem.ID;

                // If the nested item has children, recursively search through them
                if (nestedItem.Bs == null || !nestedItem.Bs.Any())
                    continue;

                var id = FindIdRecursive(nestedItem.Bs, name, nestedItem.ID, startingId, itemsToSkip);
                if (id != -1) // If id is found in the recursion, return it
                    return id;
            }

            return -1; // If name is not found in this branch
        }

        public async Task AddItemsToNewContainer(NewContainerModel newContainerModel)
        {
            if (_lastMovedComponents.EqualsList(newContainerModel.ComponentsToMove) ||
                newContainerModel.DropItem?.GroupId == null) return;

            var selectedContainer = FindComponent(newContainerModel.DropItem.GroupId.Value, DiagramContent.Parts);

            if (selectedContainer == null) return;
            selectedContainer.Parts ??= [];

            var newContainer = new RamsComponentModel
            {
                Id = Guid.NewGuid(),
                Title = "[New Group]",
                GroupId = selectedContainer.Id,
                Order = newContainerModel.OriginalOrder ?? newContainerModel.DropItem.Order,
                ParallelTracks = 2, //By default, set on 2 when dropping on another
                Dept = newContainerModel.DropItem.Dept,
                Changed = DateTime.Now,
                ParallelTrack = newContainerModel.DropItem.ParallelTrack,
                Type = RamsComponentType.Container
            };

            var selectedRams = RamsManager.GetRamsItem(selectedContainer.Id);

            selectedContainer.Parts.Add(newContainer);

            var newRamsContainer = new RamsModel
            {
                NodeId = newContainer.Id,
                Container = true,
                PartOf = selectedRams.Id,
                Name = newContainer.Title,
                Descr = newContainer.Title,
                Mtbftechn = 0,
                Mtbffunct = 0,
                LabdaFunctional = 1,
                LabdaTechnical = 1,
                WeibullShape = 1,
                CharacteristicLife = 1,
                XooN = 1,
                DiagramId = CurrentDiagram.Id,
                TestInterval = 8760
            };

            var result = RamsManager.CreateOrEditRams(newRamsContainer);

            //When using undo the just created container should be removed
            var changedItems = new List<ChangedRamsModel>
            {
                new ChangedRamsModel
                {
                    Item = result,
                    Change = UndoAction.Delete
                }
            };

            //Add changed rams items which were put into new container
            changedItems.AddRange(RamsComponents.Where(x => newContainerModel.ComponentsToMove.Contains(x.NodeId))
                .Select(x => new ChangedRamsModel
                {
                    Change = UndoAction.Modify,
                    Item = x
                }));

            UndoList.Add(new UndoEntry
            {
                ChangedItems = changedItems,
                SerializedDiagram = CurrentDiagram.Serialized
            });

            if (result.Id != 0)
                CurrentDiagram.Rams.Add(result);

            //Move both items into new container
            foreach (var componentToMove in newContainerModel.ComponentsToMove)
            {
                var parent = CurrentDiagram.Rams.Find(x => x.NodeId == newContainer.Id);

                if (parent != null && CurrentDiagram.Rams.Find(x => x.NodeId == componentToMove) != null)
                    CurrentDiagram.Rams.Find(x => x.NodeId == componentToMove).PartOf = parent.Id;

                MoveComponent(DiagramContent.Parts, componentToMove, newContainer.Id);
            }

            //Save tracing for duplicate call to this method
            _lastMovedComponents = newContainerModel.ComponentsToMove;
            await ReOrderComponents(DiagramContent.Parts);

            CleanDiagram();
        }

        private static RamsComponentModel FindComponent(Guid id, List<RamsComponentModel> parts)
        {
            foreach (var part in parts)
            {
                if (part.Id == id) return part;

                if (part.Type != RamsComponentType.Container || !part.Parts.Any())
                    continue;

                var result = FindComponent(id, part.Parts);

                if (result != null)
                    return result;
            }

            return null;
        }

        private static int CountComponents(List<RamsComponentModel> parts)
        {
            var count = 0;
            foreach (var part in parts)
            {
                count++;

                if (part.Type == RamsComponentType.Container && part.Parts.Any())
                    CountComponents(part.Parts, ref count);
            }

            return count;
        }

        private static void CountComponents(List<RamsComponentModel> parts, ref int count)
        {
            foreach (var part in parts)
            {
                count++;

                if (part.Type == RamsComponentType.Container && part.Parts.Any())
                    CountComponents(part.Parts, ref count);
            }
        }

        public async Task SaveAndReorderRamsDiagram(RamsComponentModel toMove, bool loadDiagram = true)
        {
            if (toMove is {GroupId: not null})
            {
                UndoList.Add(new UndoEntry
                {
                    ChangedItems =
                    [
                        new ChangedRamsModel
                        {
                            Change = UndoAction.Modify,
                            Item = RamsComponents.FirstOrDefault(x => x.NodeId == toMove.Id)
                        }
                    ],
                    SerializedDiagram = DeepCopier.Copy(CurrentDiagram.Serialized)
                });
                MoveComponent(DiagramContent.Parts, toMove.Id, toMove.GroupId);
            }

            await ReOrderComponents(DiagramContent.Parts);

            if (loadDiagram)
                await SaveRamsDiagram();
            else
                await SaveRamsDiagram(true, false, false);

            await CleanDiagram();
        }

        public Task CreateLcvCalculation()
        {
            //First check if create LCC is checked
            if (CurrentDiagram.WantLcc == true)
            {
                //Only if lcc doesn't exist yet, create one
                if (LccManager.GetLccByRamsId(CurrentDiagram.Id) == null)
                {
                    LccManager.CreateLccForRamsDiagram(CurrentDiagram, GlobalDataService.Language);
                }
            }
            else
            {
                //No need to check if exists just remove everything related to ramsdiagram id.
                LccManager.DeleteLccByRamsDiagram(CurrentDiagram.Id);
            }

            return SaveRamsDiagram(true);
        }

        public Task SaveRamsDiagram()
        {
            return SaveRamsDiagram(true);
        }

        private async Task SaveRamsDiagram(bool saveCalculations, bool loadFromDatabase = false,
            bool loadDiagram = true,
            string serialized = null)
        {
            CurrentDiagram.Serialized = serialized == null
                ? JsonConvert.SerializeObject(DiagramContent, new JsonSerializerSettings
                {
                    Culture = new CultureInfo(GlobalDataService.Language)
                })
                : serialized;

            for (var i = CurrentDiagram.Rams.Count - 1; i >= 0; i--)
            {
                var ram = CurrentDiagram.Rams[i];
                if (FindComponent(ram.NodeId, DiagramContent.Parts) != null)
                    continue;

                RamsManager.DeleteRams(ram.NodeId);
                CurrentDiagram.Rams.RemoveAt(i);
            }

            CurrentDiagram = RamsManager.RecalculateDiagram(CurrentDiagram,
                new CalculationParameters(CurrentDiagram),
                new CalculationParameters(CurrentDiagram),
                DisplayMode, saveCalculations, GlobalDataService.Language, loadFromDatabase);

            if (loadDiagram)
                await Load();
        }

        private void SaveRamsDiagramContent()
        {
            CurrentDiagram.Serialized = JsonConvert.SerializeObject(DiagramContent, new JsonSerializerSettings
            {
                Culture = new CultureInfo(GlobalDataService.Language)
            });

            RamsManager.CreateOrEditRamsDiagram(CurrentDiagram);
        }

        private void SaveRamsDiagramContent(string serializedData)
        {
            CurrentDiagram.Serialized = serializedData;
            RamsManager.CreateOrEditRamsDiagram(CurrentDiagram);
        }

        public async Task CollapseContainer()
        {
            SaveRamsDiagramContent();
            await CleanDiagram();
        }

        private Task RecalculateRamsDiagram()
        {
            CurrentDiagram = RamsManager.RecalculateDiagram(CurrentDiagram,
                new CalculationParameters(CurrentDiagram),
                new CalculationParameters(CurrentDiagram),
                DisplayMode, false, GlobalDataService.Language);

            return Load();
        }

        private static async Task ReOrderComponents(IReadOnlyCollection<RamsComponentModel> components, int dept = 0)
        {
            foreach (var component in components.OrderBy(x => x.Order).ThenByDescending(x => x.Changed))
            {
                //Reset dept
                component.Dept = dept + 1;
                //If duplicate orders exist move up a column
                var duplicateOrderItems = components
                    .Where(x => x.Order == component.Order && x.ParallelTrack == component.ParallelTrack).ToList();

                //But only the oldest
                if (duplicateOrderItems.Any() && duplicateOrderItems.Count > 1 &&
                    component == duplicateOrderItems.MinBy(x => x.Changed))
                {
                    component.Order++;
                    component.Changed = DateTime.Now;
                }

                //Check if no empty columns in between
                var i = 1;
                if (component.Order > 0)
                    while (components.Where(x => x.ParallelTrack == component.ParallelTrack).OrderBy(x => x.Order)
                               .All(x => x.Order != component.Order - 1 && component.Order - 1 > 0) &&
                           i < components.MaxBy(x => x.Order).Order)
                    {
                        i++;
                        component.Order--;
                    }

                if (component.Type == RamsComponentType.Container)
                    await ReOrderComponents(component.Parts, component.Dept);

                component.Connectors.Clear();

                //Connection to previous block
                if (component.Order > 1)
                {
                    //Get previous blocks
                    var previousBlocks = components
                        .Where(x => x.Order == component.Order - 1 && x.ParallelTrack == component.ParallelTrack)
                        .OrderBy(x => x.ParallelTrack).ToList();

                    if (previousBlocks.Any())
                        foreach (var previousBlock in previousBlocks.Where(previousBlock =>
                                     previousBlock != null && previousBlock.Id != component.Id))
                            component.Connectors.Add(new RamsComponentConnectorModel
                            {
                                Destination = previousBlock.Id,
                                TypeDestination = previousBlock.Type,
                                LocationDestination = RamsComponentConnectorLocation.right,
                                LocationSource = RamsComponentConnectorLocation.left
                            });

                    //connect from container to last block items
                    if (component.Type == RamsComponentType.Container)
                    {
                        var lastBlocks = component.Parts.GroupBy(x => x.ParallelTrack)
                            .Select(x => x.MaxBy(y => y.Order));

                        foreach (var previousBlock in lastBlocks.Where(x => x.Id != component.Id))
                            component.Connectors.Add(new RamsComponentConnectorModel
                            {
                                Destination = previousBlock.Id,
                                TypeDestination = previousBlock.Type,
                                LocationDestination = RamsComponentConnectorLocation.right,
                                LocationSource = RamsComponentConnectorLocation.right
                            });
                    }
                }
                //connection to parent
                else if (component.Type == RamsComponentType.Block && component.GroupId != null &&
                         component.Id != component.GroupId)
                {
                    component.Connectors.Add(new RamsComponentConnectorModel
                    {
                        Destination = component.GroupId.Value,
                        TypeDestination = RamsComponentType.Container,
                        LocationDestination = component.Order == 1
                            ? RamsComponentConnectorLocation.left
                            : RamsComponentConnectorLocation.right,
                        LocationSource = component.Order == 1
                            ? RamsComponentConnectorLocation.left
                            : RamsComponentConnectorLocation.right
                    });
                }
                else if (component.Type == RamsComponentType.Container)
                {
                    //Connection to previous block inside
                    if (component.Parts.Any())
                        component.Connectors.AddRange(component.Parts.GroupBy(x => x.ParallelTrack)
                            .Select(x => x.MaxBy(y => y.Order))
                            .Select(x =>
                                new RamsComponentConnectorModel
                                {
                                    Destination = x.Id,
                                    TypeDestination = RamsComponentType.Block,
                                    LocationDestination = RamsComponentConnectorLocation.right,
                                    LocationSource = RamsComponentConnectorLocation.right
                                }).ToList());

                    //If nested group connect to group parent
                    if (component.GroupId != null)
                        component.Connectors.Add(new RamsComponentConnectorModel
                        {
                            Destination = component.GroupId.Value,
                            LocationDestination = RamsComponentConnectorLocation.left,
                            LocationSource = RamsComponentConnectorLocation.left,
                            TypeDestination = RamsComponentType.Container
                        });
                }
            }
        }

        private static void MoveComponent(List<RamsComponentModel> components, Guid guidToMove, Guid? newParent)
        {
            var componentToMove = NestedRemoveComponent(components, guidToMove);
            NestedInsertComponent(components, componentToMove, newParent);
        }

        private static RamsComponentModel NestedRemoveComponent(List<RamsComponentModel> components, Guid guidToMove)
        {
            RamsComponentModel componentToMove = null;
            for (var i = 0; i < components.Count; i++)
            {
                if (components[i].Id == guidToMove)
                {
                    componentToMove = components[i];
                    components.RemoveAt(i);
                    break;
                }

                if (components[i].Type == RamsComponentType.Container)
                    componentToMove = NestedRemoveComponent(components[i].Parts, guidToMove);

                if (componentToMove != null) return componentToMove;
            }

            return componentToMove;
        }

        private static void NestedInsertComponent(IEnumerable<RamsComponentModel> components,
            RamsComponentModel toInsert,
            Guid? newParent)
        {
            foreach (var component in components.Where(x => x.Type == RamsComponentType.Container))
            {
                if (newParent != null)
                {
                    if (component.Id == newParent)
                    {
                        toInsert.GroupId = newParent;
                        toInsert.ParallelTrack ??= 1;
                        toInsert.Dept = component.Dept + 1;
                        toInsert.Connectors.Clear();

                        component.Parts.Add(toInsert);

                        break;
                    }
                }
                else
                {
                    //remove optional existing parent from component
                    component.GroupId = null;

                    if (component.Id == toInsert.GroupId)
                    {
                        component.Parts.Add(toInsert);
                        break;
                    }
                }

                NestedInsertComponent(component.Parts, toInsert, newParent);
            }
        }

        public Task SaveRamsComponent()
        {
            return SaveRamsComponent(false);
        }

        public Task SaveRamsComponentAndRecalculate()
        {
            return SaveRamsComponent(true);
        }

        public Task ProcessClassificationChangesAndSaveRamsComponent()
        {
            //Set DCD field based on the classification dropdown
            switch (SelectedNode?.ClassDc)
            {
                case "DD":
                    SelectedNode.Dcd = SelectedItem.Dcd = 0.9;
                    break;
                case "DU":
                    SelectedNode.Dcd = SelectedItem.Dcd = 0.1;
                    break;
                case "SD":
                case "SU":
                    SelectedNode.Dcd = SelectedItem.Dcd = 1.0;
                    break;
            }

            return SaveRamsComponent(true);
        }

        public Task SaveRamsComponent(bool recalculate)
        {
            if (SelectedItem.Type == RamsComponentType.Block)
            {
                SelectedItem.Classification = SelectedNode?.ClassDc;
                SelectedItem.Title = SelectedNode?.Name;
                SelectedItem.Status = SelectedNode?.Status != null
                    ? (RamsBlockStatus) SelectedNode.Status.Value
                    : RamsBlockStatus.Undecided;

                FindComponent(SelectedItem.Id, DiagramContent.Parts).Status = SelectedItem.Status;
                FindComponent(SelectedItem.Id, DiagramContent.Parts).Title = SelectedNode?.Name;

                if (SelectedNode != null)
                {
                    //Set Status specific values
                    if (SelectedItem.Status is RamsBlockStatus.ReferenceBlock)
                    {
                        SelectedNode.Mtbffunct = SelectedNode.Mtbftechn = 10000000;
                        SelectedNode.ReliabilityFunctional = SelectedNode.ReliabilityTechnical = 100;
                        SelectedNode.AvailabilityInput = SelectedNode.AvailabilityOutput = 100;
                        SelectedNode.Pfd = 0;

                        SelectedItem.Results.MtbfFunctional = SelectedItem.Results.MtbfTechnical = 10000000;
                        SelectedItem.Results.ReliabilityFunctional = SelectedItem.Results.ReliabilityTechnical = 1;
                        SelectedItem.Results.AvailFunctional = SelectedItem.Results.AvailTechnical = 1;
                        SelectedNode.Pfd = 0;
                    }
                    else if (SelectedItem.Status is RamsBlockStatus.NotInstalled)
                    {
                        SelectedItem.Results.MtbfFunctional = SelectedItem.Results.MtbfTechnical = 10000000;
                        SelectedItem.Results.ReliabilityFunctional = SelectedItem.Results.ReliabilityTechnical = 1;
                        SelectedItem.Results.AvailFunctional = SelectedItem.Results.AvailTechnical = 1;
                        SelectedNode.Pfd = 0;
                    }

                    //Copy values of first block to other blocks
                    if (SelectedItem.GroupId != null)
                    {
                        var groupId = SelectedItem.GroupId.Value;
                        var isGroupIdenticalTurnedOn = RamsManager.GetRamsItem(groupId);

                        //If identical is turned off or false, allow editing of all items
                        if (isGroupIdenticalTurnedOn.Identical == true)
                        {
                            var groupItem = FindComponent(groupId, DiagramContent.Parts);
                            var firstBlock = groupItem.Parts.OrderBy(x => x.Order).ThenBy(x => x.ParallelTrack)
                                .FirstOrDefault();

                            //If current item being edited is first item then change values of other blocks to these new values
                            if (SelectedItem.Id == firstBlock?.Id)
                            {
                                var rams = RamsComponents.Where(x => x.PartOf == isGroupIdenticalTurnedOn.Id);

                                foreach (var part in groupItem.Parts.Where(x => x.Id != firstBlock.Id))
                                {
                                    part.Results.MtbfTechnical = SelectedItem.Results.MtbfTechnical;
                                    part.Results.MtbfFunctional = SelectedItem.Results.MtbfFunctional;

                                    part.Results.ReliabilityFunctional = SelectedItem.Results.ReliabilityFunctional;
                                    part.Results.ReliabilityTechnical = SelectedItem.Results.ReliabilityTechnical;

                                    part.Results.AvailFunctional = SelectedItem.Results.AvailFunctional;
                                    part.Results.AvailTechnical = SelectedItem.Results.AvailTechnical;

                                    part.Results.Mttr = SelectedItem.Results.Mttr;

                                    part.Classification = SelectedItem.Classification;
                                    part.Dcd = SelectedItem.Dcd;
                                }

                                foreach (var ram in rams.Where(x => x.Id != SelectedNode.Id))
                                {
                                    ram.Mtbffunct = SelectedNode.Mtbffunct;
                                    ram.Mtbftechn = SelectedNode.Mtbftechn;
                                    ram.AvailabilityInput = SelectedNode.AvailabilityInput;
                                    ram.AvailabilityOutput = SelectedNode.AvailabilityOutput;
                                    ram.Pfd = SelectedNode.Pfd;
                                    ram.ClassDc = SelectedNode.ClassDc;
                                    ram.Beta = SelectedNode.Beta;
                                    ram.TestInterval = SelectedNode.TestInterval;
                                    ram.Dcd = SelectedNode.Dcd;
                                    ram.Mttr = SelectedNode.Mttr;
                                }
                            }
                        }
                    }

                    SaveRamsComponent(SelectedNode);
                }
            }
            else
            {
                SelectedItem.XooN = SelectedGroup?.XooN;
                SelectedItem.Title = SelectedGroup?.Name;
                SelectedItem.Status = SelectedGroup?.Status != null
                    ? (RamsBlockStatus) SelectedGroup.Status.Value
                    : RamsBlockStatus.Undecided;
                SelectedItem.Classification = SelectedGroup?.ClassDc;

                FindComponent(SelectedItem.Id, DiagramContent.Parts).Status = SelectedItem.Status;

                if (SelectedGroup != null)
                {
                    //If group is set identical to first we must set the MTBF of the child nodes identical to first node
                    if (SelectedGroup.Identical == true)
                    {
                        //Get start node to determine what the other nodes should have as values
                        var startNode = SelectedItem.Parts.OrderBy(x => x.Order)
                            .ThenBy(x => x.ParallelTrack)
                            .FirstOrDefault();

                        if (startNode != null)
                        {
                            var startBlock = RamsComponents.FirstOrDefault(x => x.NodeId == startNode?.Id);

                            if (startBlock != null)
                            {
                                var rams = RamsComponents.Where(x => x.PartOf == SelectedGroup.Id);

                                foreach (var part in SelectedItem.Parts.Where(x => x.Id != startNode.Id))
                                {
                                    part.Results.MtbfTechnical = startNode.Results.MtbfTechnical;
                                    part.Results.MtbfFunctional = startNode.Results.MtbfFunctional;

                                    part.Results.ReliabilityFunctional = startNode.Results.ReliabilityFunctional;
                                    part.Results.ReliabilityTechnical = startNode.Results.ReliabilityTechnical;

                                    part.Results.AvailFunctional = startNode.Results.AvailFunctional;
                                    part.Results.AvailTechnical = startNode.Results.AvailTechnical;

                                    part.Results.Mttr = startNode.Results.Mttr;

                                    part.Classification = startNode.Classification;
                                    part.Dcd = startNode.Dcd;
                                }

                                foreach (var ram in rams.Where(x => x.Id != startBlock.Id))
                                {
                                    ram.Mtbffunct = startBlock.Mtbffunct;
                                    ram.Mtbftechn = startBlock.Mtbftechn;
                                    ram.AvailabilityInput = startBlock.AvailabilityInput;
                                    ram.AvailabilityOutput = startBlock.AvailabilityOutput;
                                    ram.Pfd = startBlock.Pfd;
                                    ram.ClassDc = startBlock.ClassDc;
                                    ram.Beta = startBlock.Beta;
                                    ram.TestInterval = startBlock.TestInterval;
                                    ram.Dcd = startBlock.Dcd;
                                    ram.Mttr = startBlock.Mttr;
                                }
                            }
                        }
                    }

                    SaveRamsComponent(SelectedGroup);
                }
            }

            return Task.CompletedTask;
        }

        public async Task GenerateDiagramFromRiskObject()
        {
            if (!CurrentDiagram.RiskObject.HasValue || !SelectedDiagram.HasValue) return;

            ShowLoadingDialog();

            CurrentDiagram = await RamsManager.GenerateDiagramFromRiskObject(CurrentDiagram,
                new CalculationParameters(CurrentDiagram),
                new CalculationParameters(CurrentDiagram),
                DisplayMode, GlobalDataService.Language);

            using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(CurrentDiagram.Serialized)))
            using (var reader = new StreamReader(stream))
            using (var jsonReader = new JsonTextReader(reader))
            {
                var serializer = JsonSerializer.Create(new JsonSerializerSettings
                {
                    Culture = new CultureInfo(GlobalDataService.Language)
                });
                DiagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
            }

            await Load();

            TreeTab?.Reload();
            EditorTab?.Reload();
            BlockContentTab?.Reload();
            ContainerContentTab?.Reload();

            await CleanDiagram();

            ShowCompleteDialog("Finished loading riskobject.");
        }

        private void ShowLoadingDialog()
        {
            DialogService.Open<RamsLoadResultDialog>("Loading",
                new Dictionary<string, object>
                {
                    {nameof(RamsLoadResultDialog.Loading), true},
                    {nameof(RamsLoadResultDialog.Text), "Please wait"}
                });
        }

        private void ShowCompleteDialog(string text)
        {
            DialogService.Close();
            DialogService.Open<RamsLoadResultDialog>("Completed",
                new Dictionary<string, object>
                {
                    {nameof(RamsLoadResultDialog.Loading), false},
                    {nameof(RamsLoadResultDialog.Text), text}
                });
        }

        private RamsModel SaveRamsComponent(RamsModel rams, bool processDiagram = true, bool reOrder = false)
        {
            if (rams.Id == 0)
                rams.DiagramId = CurrentDiagram.Id;

            var result = RamsManager.CreateOrEditRams(rams);

            if (rams.Id == 0 && result.Id != 0) CurrentDiagram.Rams.Add(result);

            if (processDiagram)
                if (reOrder)
                    SaveAndReorderRamsDiagram(null);
                else
                    SaveRamsDiagram();

            return result;
        }

        private bool DisableBlockEditingCheck()
        {
            if (SelectedItem?.GroupId == null)
                return true;

            var groupId = SelectedItem.GroupId.Value;
            var isGroupIdenticalTurnedOn = RamsManager.GetRamsItem(groupId);

            //If identical is turned off or false, allow editing of all items
            if (isGroupIdenticalTurnedOn.Identical != true)
                return false;

            var groupItem = FindComponent(groupId, DiagramContent.Parts);
            var firstBlock = groupItem.Parts.OrderBy(x => x.Order).ThenBy(x => x.ParallelTrack).FirstOrDefault();

            return SelectedNode.NodeId != firstBlock?.Id;
        }

        #endregion

        #region Button bar

        public async Task AddBlock(RadzenSplitButtonItem item)
        {
            DisableAddButton = true;
            var before = false;

            if (item != null)
                before = item.Value == 0.ToString();

            //If before button is selected we are gonna subtract from the order field
            var addition = before ? -1 : 1;

            //Determine groupId for new item, 3 scenarios:
            //1. No item selected, then root container
            //2. Item selected and this is a group, then get the ID
            //3. Item selected and this is a block, get container where the selected item is in
            Guid? groupId;
            if (SelectedItem != null && SelectedItem.Id != DiagramContent.Parts[0].Id)
                groupId = SelectedItem.GroupId;
            else
                groupId = DiagramContent.Parts[0].Id;

            var parent = CurrentDiagram.Rams.Find(x => x.NodeId == groupId);

            var newBlock = new RamsComponentModel
            {
                Id = Guid.NewGuid(),
                Title = "[New Block]",
                Order = SelectedItem?.Order + addition ??
                        (before
                            ? DiagramContent.Parts.Find(x => x.Type == RamsComponentType.Container)?.Parts
                                .MinBy(x => x.Order)?.Order ?? 0
                            : DiagramContent.Parts.Find(x => x.Type == RamsComponentType.Container)?.Parts
                                .MaxBy(x => x.Order)?.Order ?? 0) +
                        addition, // If item is selected change order based on this if not selected get highest order in root container
                Type = RamsComponentType.Block,
                Changed = DateTime.Now,
                ParallelTrack = SelectedItem?.ParallelTrack ?? DiagramContent.Parts
                        .Find(x => x.Type == RamsComponentType.Container)?.Parts.MaxBy(x => x.Order)
                        ?.ParallelTrack ??
                    1, // If item is selected get parallel track else get root container and get highest parallel track
                Dept = SelectedItem?.Dept ?? 1,
                GroupId = groupId
            };

            newBlock.GroupId = groupId;

            //Fix if it is placed before first block
            if (newBlock.Order < 1)
                newBlock.Order = 1;

            if (groupId != null)
                FindComponent((Guid) groupId, DiagramContent.Parts)?.Parts.Add(newBlock);

            var newRamsBlock = new RamsModel
            {
                NodeId = newBlock.Id,
                Container = false,
                Descr = newBlock.Title,
                Name = newBlock.Title,
                Mtbftechn = 1,
                Mtbffunct = 1,
                LabdaFunctional = 1,
                LabdaTechnical = 1,
                WeibullShape = 1,
                CharacteristicLife = 1,
                TestInterval = 8760,
                PartOf = parent.Id,
                DiagramId = CurrentDiagram.Id
            };

            var serializedDiagram = DeepCopier.Copy(CurrentDiagram.Serialized);

            //Does the reorder
            var result = SaveRamsComponent(newRamsBlock, true, true);

            if (!RamsComponents.Any(x => x.Id == result.Id))
            {
                RamsComponents.Add(result);
                RamsGrid?.Reload();
            }

            //Add entry to undo list; Item has been added so needs to be removed when undoing
            UndoList.Add(new UndoEntry
            {
                ChangedItems =
                [
                    new ChangedRamsModel
                    {
                        Item = result,
                        Change = UndoAction.Delete
                    }
                ],
                SerializedDiagram = serializedDiagram
            });

            if (SelectedItem != null) newBlock.Order = SelectedItem.Order + addition;

            await CleanDiagram();
            DisableAddButton = false;

            //Check if configuration limit has been reached
            if (RamsTotalAllowedBlocksSetting?.IntValue != null)
                DisableAddButton = CountComponents(DiagramContent.Parts) >= RamsTotalAllowedBlocksSetting.IntValue;
        }

        public async Task AddDiagram(RadzenSplitButtonItem item)
        {
            if (string.IsNullOrWhiteSpace(item?.Value))
                return;

            DisableAddButton = true;

            //Determine groupId for new item, 3 scenarios:
            //1. No item selected, then root container
            //2. Item selected and this is a group, then get the ID
            //3. Item selected and this is a block, get container where the selected item is in
            Guid? groupId;
            if (SelectedItem != null)
                groupId = SelectedItem.Type == RamsComponentType.Container ? SelectedItem.Id : SelectedItem.GroupId;
            else
                groupId = DiagramContent.Parts.Find(x => x.Type == RamsComponentType.Container)
                    ?.Id;

            var parent = CurrentDiagram.Rams.Find(x => x.NodeId == groupId);

            var newBlock = new RamsComponentModel
            {
                Id = Guid.NewGuid(),
                Title = item.Text,
                Order = SelectedItem?.Order + 1 ?? DiagramContent.Parts.Find(x => x.Type == RamsComponentType.Container)
                        ?.Parts
                        .MaxBy(x => x.Order)?.Order + 1 ??
                    0 + 1, // If item is selected change order based on this if not selected get highest order in root container
                Type = RamsComponentType.Block,
                Changed = DateTime.Now,
                ParallelTrack = SelectedItem?.ParallelTrack ?? DiagramContent.Parts
                        .Find(x => x.Type == RamsComponentType.Container)?.Parts.MaxBy(x => x.Order)
                        ?.ParallelTrack ??
                    1, // If item is selected get parallel track else get root container and get highest parallel track
                Dept = SelectedItem?.Dept ?? 1,
                GroupId = groupId,
                DiagramId = int.Parse(item.Value)
            };

            newBlock.GroupId = groupId;

            //Fix if it is placed before first block
            if (newBlock.Order < 1)
                newBlock.Order = 1;

            if (groupId != null)
                FindComponent((Guid) groupId, DiagramContent.Parts)?.Parts.Add(newBlock);

            var diagram = RamsManager.GetRamsDiagram(int.Parse(item.Value));
            RamsDiagramContentModel diagramContent;

            using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(diagram.Serialized)))
            using (var reader = new StreamReader(stream))
            using (var jsonReader = new JsonTextReader(reader))
            {
                var serializer = JsonSerializer.Create(new JsonSerializerSettings
                {
                    Culture = new CultureInfo(GlobalDataService.Language)
                });
                diagramContent = serializer.Deserialize<RamsDiagramContentModel>(jsonReader);
            }

            var rootNode = diagramContent.Parts[0];

            var newRamsBlock = new RamsModel
            {
                NodeId = newBlock.Id,
                Container = false,
                DiagramRefId = int.Parse(item.Value),
                DiagramId = CurrentDiagram.Id,
                Descr = newBlock.Title,
                Name = newBlock.Title,
                Mtbftechn = rootNode.Results.MtbfTechnical,
                Mtbffunct = rootNode.Results.MtbfFunctional,
                Mttr = rootNode.Results.MttrTechnical,
                LabdaFunctional = rootNode.Results.LabdaFunctional,
                LabdaTechnical = rootNode.Results.LabdaTechnical,
                ReliabilityFunctional = rootNode.Results.ReliabilityFunctional,
                ReliabilityTechnical = rootNode.Results.ReliabilityTechnical,
                AvailabilityInput = rootNode.Results.AvailFunctional,
                AvailabilityOutput = rootNode.Results.AvailTechnical,
                WeibullShape = 1,
                CharacteristicLife = 1,
                TestInterval = 8760,
                PartOf = parent.Id
            };

            //Does the reorder
            SaveRamsComponent(newRamsBlock, true, true);

            //TODO: implement undo

            if (SelectedItem != null) newBlock.Order = SelectedItem.Order + 1;

            await CleanDiagram();
            DisableAddButton = false;

            //Check if configuration limit has been reached
            if (RamsTotalAllowedBlocksSetting?.IntValue != null)
                DisableAddButton = CountComponents(DiagramContent.Parts) >= RamsTotalAllowedBlocksSetting.IntValue;
        }

        public async Task DownloadImage()
        {
            // Invoke JavaScript function to convert HTML to image
            await JavaScriptHelper.InvokeAsync<object>("convertToImage", "rams-diagram-container", CurrentDiagram.Name);
        }

        public void ProcessUpload(FileUpload fileUpload)
        {
            if (string.IsNullOrWhiteSpace(fileUpload.Base64Content))
                return;

            ShowLoadingDialog();

            var uploadedItems = fileUpload.Base64Content
                .GetExcelData<RamsFlatModel>(headerIndex: 1)
                .Select(x => x.Value).ToList();

            if (RamsManager.ImportFileUpload(CurrentDiagram.Id, GlobalDataService.Language, uploadedItems))
            {
                DialogService.Close();
                ShowDialog(
                    string.Format("Success, updated {0} {1}.", uploadedItems.Count,
                        uploadedItems.Count > 1 ? "items" : "item"), false);
                UpdateTreeAndDiagram(SelectedDiagram);
            }
            else
            {
                DialogService.Close();
                ShowDialog("Error!", true);
            }
        }

        private void ShowDialog(string text, bool isError)
        {
            // Implementation for showing dialog
            DialogService.Open<RamsLoadResultDialog>(isError ? "Error" : "Success",
                new Dictionary<string, object>
                {
                    {nameof(RamsLoadResultDialog.Loading), false},
                    {nameof(RamsLoadResultDialog.Text), text}
                });
        }

        public void LinkSelected()
        {
            LinkSelectionMethodActive = true;
        }

        public async Task RemoveComponent()
        {
            if (SelectedComponent != null)
            {
                var undoEntry = new UndoEntry
                {
                    ChangedItems = [],
                    SerializedDiagram = DeepCopier.Copy(CurrentDiagram.Serialized)
                };

                undoEntry.ChangedItems.Add(new ChangedRamsModel
                {
                    Change = UndoAction.Add,
                    Item = RamsComponents.FirstOrDefault(x => x.NodeId == SelectedItem.Id)
                });

                RamsComponents.RemoveAll(x => x.NodeId == SelectedItem.Id);

                //If container remove all items below as well
                if (SelectedItem.Type == RamsComponentType.Container)
                {
                    var container = FindComponent(SelectedItem.Id, DiagramContent.Parts);
                    RemoveNested(container.Parts, undoEntry);
                }

                RamsManager.DeleteRams(SelectedItem.Id);

                RemoveFromDiagramByGuid(SelectedComponent.Value);

                SelectedNode = null;
                SelectedGroup = null;
                SelectedItem = null;
                SelectedComponent = null;
                SelectedTabIndex = 0;

                UndoList.Add(undoEntry);

                await SaveAndReorderRamsDiagram(null);
                await Load();

                await CleanDiagram();
            }
        }

        private void RemoveNested(List<RamsComponentModel> parts, UndoEntry undoEntry)
        {
            foreach (var part in parts)
            {
                if (part.Type == RamsComponentType.Container) RemoveNested(part.Parts, undoEntry);

                undoEntry.ChangedItems.Add(new ChangedRamsModel
                {
                    Change = UndoAction.Add,
                    Item = RamsComponents.FirstOrDefault(x => x.NodeId == part.Id)
                });

                RemoveFromDiagramByGuid(SelectedComponent.Value);
            }
        }

        public async Task CopyComponent()
        {
            if (SelectedComponent != null)
            {
                CopiedItem = SelectedItem;
                var selectedId = SelectedNode?.Id ?? SelectedGroup?.Id ?? 0;

                CopiedRamsItem = CurrentDiagram.Rams.Find(x => x.Id == selectedId);
            }
        }

        public async Task PasteComponent()
        {
            if (CopiedItem != null)
            {
                bool isRoot = false;

                if (SelectedItem == null)
                {
                    //Put at end of children of rootcontainer
                    if (DiagramContent?.Parts?.FirstOrDefault()?.Parts.Any() == true)
                    {
                        SelectedItem = DiagramContent?.Parts?.FirstOrDefault()?.Parts?.OrderBy(x => x.Order)
                            .LastOrDefault();
                    }
                    else
                    {
                        SelectedItem = DiagramContent?.Parts?.FirstOrDefault();
                        isRoot = true;
                    }
                }
                else if (SelectedItem.GroupId == null)
                {
                    isRoot = true;
                }

                var parent =
                    CurrentDiagram.Rams.Find(x => x.NodeId == (isRoot ? SelectedItem.Id : SelectedItem.GroupId));

                if (CopiedItem.Type == RamsComponentType.Block)
                {
                    var newBlock = DeepCopier.Copy(CopiedItem);
                    newBlock.Id = Guid.NewGuid();
                    newBlock.GroupId = isRoot ? SelectedItem.Id : SelectedItem.GroupId;
                    newBlock.Order = isRoot ? 1 : SelectedItem.Order + 1;
                    newBlock.ParallelTrack = isRoot ? 1 : SelectedItem.ParallelTrack;
                    newBlock.Dept = isRoot ? 1 : SelectedItem.Dept;
                    newBlock.Changed = DateTime.Now;

                    if (CopiedItem.GroupId != null)
                        if (isRoot)
                            DiagramContent.Parts?.FirstOrDefault()?.Parts.Add(newBlock);
                        else
                            FindComponent((Guid) SelectedItem.GroupId, DiagramContent.Parts)?.Parts.Add(newBlock);

                    var ramsBlock = RamsManager.GetRamsItem(CopiedRamsItem.Id);
                    var newRamsBlock = DeepCopier.Copy(ramsBlock);
                    newRamsBlock.Id = 0;
                    newRamsBlock.NodeId = newBlock.Id;
                    newRamsBlock.PartOf = parent.Id;
                    newRamsBlock.DiagramId = parent.DiagramId;

                    //Does the reorder
                    SaveRamsComponent(newRamsBlock, true, true);
                }
                else
                {
                    var newContainer = DeepCopier.Copy(CopiedItem);
                    newContainer.Id = Guid.NewGuid();
                    newContainer.GroupId = isRoot ? SelectedItem.Id : SelectedItem.GroupId;
                    newContainer.Order = isRoot ? 1 : SelectedItem.Order + 1;
                    newContainer.ParallelTrack = isRoot ? 1 : SelectedItem.ParallelTrack;
                    newContainer.Dept = isRoot ? 1 : SelectedItem.Dept;
                    newContainer.Changed = DateTime.Now;
                    newContainer.DiagramId = SelectedItem.DiagramId;

                    if (isRoot)
                        DiagramContent.Parts?.FirstOrDefault()?.Parts.Add(newContainer);
                    else
                        FindComponent((Guid) SelectedItem.GroupId, DiagramContent.Parts)?.Parts.Add(newContainer);

                    var existingContainer = RamsManager.GetRamsItem(CopiedRamsItem.Id);

                    if (existingContainer != null)
                    {
                        var newRamsContainer = DeepCopier.Copy(existingContainer);
                        newRamsContainer.Id = 0;
                        newRamsContainer.NodeId = newContainer.Id;
                        newRamsContainer.PartOf = parent.Id;
                        newRamsContainer.DiagramId = parent.DiagramId;

                        var newParent = RamsManager.CreateOrEditRams(newRamsContainer);
                        RamsComponents.Add(newParent);

                        TraverseAndCreate(newContainer, newParent);
                    }

                    await SaveAndReorderRamsDiagram(null);
                }

                CopiedItem = null;

                await CleanDiagram();
            }
        }

        public async Task UndoChanges()
        {
            var undoEntry = UndoList.GetLast();

            if (undoEntry != null)
            {
                foreach (var changedItem in undoEntry.ChangedItems)
                {
                    switch (changedItem.Change)
                    {
                        case UndoAction.Delete:
                            RamsManager.DeleteRams(changedItem.Item.NodeId);
                            RamsComponents.RemoveAll(x => x.NodeId == changedItem.Item.NodeId);
                            break;
                        case UndoAction.Add:
                            if (changedItem.Item != null)
                            {
                                changedItem.Item.Id = 0;
                                RamsManager.CreateOrEditRams(changedItem.Item);
                            }

                            break;
                        case UndoAction.Modify:
                            RamsManager.CreateOrEditRams(changedItem.Item);
                            break;
                    }
                }

                await SaveRamsDiagram(true, true, true, undoEntry.SerializedDiagram);

                Load();
                CleanDiagram();
            }

            UndoList.RemoveLast();
        }

        private void TraverseAndCreate(RamsComponentModel newContainer, RamsModel newRamsContainer)
        {
            foreach (var part in newContainer.Parts)
            {
                var sourceItem = RamsManager.GetRamsItem(part.Id);

                if (sourceItem != null)
                {
                    part.Id = Guid.NewGuid();
                    part.GroupId = newContainer.Id;
                    part.Dept = newContainer.Dept + 1;

                    var newItem = DeepCopier.Copy(sourceItem);
                    newItem.Id = 0;
                    newItem.NodeId = part.Id;
                    newItem.PartOf = newRamsContainer.Id;
                    newItem.DiagramId = newRamsContainer.DiagramId;

                    newItem = RamsManager.CreateOrEditRams(newItem);
                    RamsComponents.Add(newItem);

                    if (part.Type == RamsComponentType.Container) TraverseAndCreate(part, newItem);
                }
            }
        }

        private void RemoveFromDiagramByGuid(Guid toBeRemoved)
        {
            if (DiagramContent.Parts.RemoveAll(x => x.Id == toBeRemoved) > 0)
                return;

            foreach (var part in DiagramContent.Parts) RemoveFromPartsCollection(part, toBeRemoved);
        }

        private static void RemoveFromPartsCollection(RamsComponentModel partModel, Guid toBeRemoved)
        {
            if (partModel.Parts.RemoveAll(x => x.Id == toBeRemoved) > 0)
                return;

            foreach (var part in partModel.Parts)
            {
                if (part.Parts.RemoveAll(x => x.Id == toBeRemoved) > 0)
                    return;

                RemoveFromPartsCollection(part, toBeRemoved);
            }
        }

        public void DeleteDiagram()
        {
            ShowAreYouSurePopup(CurrentDiagram, Localizer["RamsDeleteDiagramWarning"],
                EventCallback.Factory.Create<RamsDiagramModel>(this, ConfirmedDeleteDiagram));
        }

        public void ShowAreYouSurePopup<T>(T item, string title, EventCallback<T> YesCallback)
        {
            DialogService.Open<AreYouSureDialog<T>>(title,
                new Dictionary<string, object>
                {
                    {
                        nameof(AreYouSureDialog<T>.Item),
                        item
                    },
                    {
                        nameof(AreYouSureDialog<T>.YesCallback),
                        EventCallback.Factory.Create(this, YesCallback)
                    }
                });
        }

        public void ConfirmedDeleteDiagram(RamsDiagramModel currentDiagram)
        {
            RamsManager.DeleteRamsDiagram(currentDiagram);
            DiagramContent = new RamsDiagramContentModel();
            UpdateTreeAndDiagram();
            CleanDiagram();
        }

        public void SwitchDisplayMode()
        {
            ShowDisplayMode = !ShowDisplayMode;
        }

        public void HidePanelMode()
        {
            ShowHidePanelMode = !ShowHidePanelMode;
        }

        public Task UpdateDisplayMode()
        {
            DisplayMode = new RamsDisplayModel
            {
                ScientificNotation = ShownNotation == 2,
                ShowFunctional = ShownValues == 2,
                ShowPfd = ShownCalculation == 1,
                ShowSil = ShownCalculation == 2,
                ShowMtbf = ShownFunctions == 1,
                ShowLambda = ShownFunctions == 2,
                ShowYears = ShownReliability == 2
            };

            return RecalculateRamsDiagram();
        }

        public async Task SwitchMode()
        {
            ShowDiagram = !ShowDiagram;

            switch (ShowDiagram)
            {
                case false:
                    RamsComponents = CurrentDiagram?.Rams;
                    RamsGrid?.Reload();
                    break;
                case true:
                    await CleanDiagram();
                    break;
            }
        }

        public Task SaveRamsModel(RamsModel model)
        {
            SelectedItem = FindComponent(model.NodeId, DiagramContent.Parts);

            switch (SelectedItem.Type)
            {
                case RamsComponentType.Block:
                    SelectedNode = model;
                    break;
                case RamsComponentType.Container:
                    SelectedGroup = model;
                    break;
            }

            return SaveRamsComponent(true);
        }

        #endregion

        #region Draw connections

        private async Task CleanDiagram()
        {
            await RemoveLines();
            await RecalculateDiagramSize();
            await DrawConnectionLines();
        }

        public async Task RecalculateDiagramSize()
        {
            await JavaScriptHelper.InvokeAsync<object>("resizeTableCells");
            await JavaScriptHelper.InvokeAsync<object>("setWidthOfContainer");
        }

        public Task DrawConnectionLines()
        {
            return ExecuteDrawConnectorsAsync(DiagramContent.Parts);
        }

        private async Task ExecuteDrawConnectorsAsync(List<RamsComponentModel> parts)
        {
            await _semaphore.WaitAsync();

            try
            {
                await DrawConnectors(parts);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task RemoveLines()
        {
            await JavaScriptHelper.InvokeAsync<object>("removeLines");
        }

        private async Task DrawConnectors(List<RamsComponentModel> parts)
        {
            var tasks = new List<Task>();

            tasks.AddRange(GetDrawConnectorsTasks(parts));
            await Task.WhenAll(tasks);
        }

        private List<Task> GetDrawConnectorsTasks(List<RamsComponentModel> parts)
        {
            var tasks = new List<Task>();

            foreach (var part in parts)
            {
                if (part.Type == RamsComponentType.Container)
                {
                    tasks.AddRange(GetDrawConnectorsTasks(part.Parts));
                }

                tasks.Add(DrawConnector(part));
            }

            return tasks;
        }

        private Task DrawConnector(RamsComponentModel origin)
        {
            var drawingTasks = new List<Task>();

            var allConnectors = origin.Connectors;
            allConnectors.AddRange(origin.CustomConnectors);

            for (var index = 0; index < allConnectors.Count; index++)
            {
                //Check if collection is sufficient
                if (origin.Connectors.Count <= index)
                    continue;

                var connector = origin.Connectors[index];

                //Check if connect is found
                if (connector == null)
                    continue;

                //If the destination of the connector is a container but its not collapsed do not show the connector
                if (origin.GroupId.HasValue)
                {
                    var group = FindComponent(origin.GroupId.Value, DiagramContent.Parts);
                    if (group is {Type: RamsComponentType.Container, Collapsed: true}) continue;
                }

                //Draw line based on functionality which can be found in LineHelper.js
                drawingTasks.Add(JavaScriptHelper.InvokeAsync<object>("drawLine", new Root
                {
                    StartingElement = new StartingElement
                    {
                        Id = origin.Id.ToString(),
                        X = connector.LocationSource.ToString(),
                        Y = "middle"
                    },
                    EndingElement = new EndingElement
                    {
                        Id = connector.Destination.ToString(),
                        X = connector.LocationDestination.ToString(),
                        Y = "middle"
                    },
                    Thickness = "1"
                }));
            }

            return Task.WhenAll(drawingTasks);
        }

        #endregion

        // Helper classes for JavaScript interop
        public class Root
        {
            public StartingElement StartingElement { get; set; }
            public EndingElement EndingElement { get; set; }
            public string Thickness { get; set; }
        }

        public class StartingElement
        {
            public string Id { get; set; }
            public string X { get; set; }
            public string Y { get; set; }
        }

        public class EndingElement
        {
            public string Id { get; set; }
            public string X { get; set; }
            public string Y { get; set; }
        }
    }
}