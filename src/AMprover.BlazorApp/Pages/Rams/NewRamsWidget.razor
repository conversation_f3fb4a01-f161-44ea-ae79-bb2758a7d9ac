﻿@page "/new-rams-widget"

<Radzen.Blazor.RadzenTemplateForm TItem=NewRamsModel
                                  Data=@NewRams
                                  Submit=@ValidTaskSubmitted
                                  OnInvalidSubmit=@InvalidTaskSubmitted>

    <div class="form-group">@Localizer["RamsWHeaderTxt"]</div>

    <div class="form-group">
        <label>@Localizer["RamsWScenarioLbl"]:</label>
        <RadzenDropDown AllowClear="true" AllowFiltering="true" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        TextProperty="Name" ValueProperty="Id" Name="ScenarioId" class="form-control"
                        Data="@Scenarios"
                        @bind-Value="@NewRams.ScenarioId"/>
    </div>

    <div class="form-group">
        <label>@Localizer["RamsWNameLbl"]:</label>
        <RadzenTextBox Name="Name" @bind-Value="NewRams.Name" class="form-control"></RadzenTextBox>
        <RadzenRequiredValidator Component="Name"/>
    </div>
    <RadzenButton IsBusy=@IsBusy Text=@Localizer["RamsWCreateBtn"] ButtonType="ButtonType.Submit"></RadzenButton>
</Radzen.Blazor.RadzenTemplateForm>
