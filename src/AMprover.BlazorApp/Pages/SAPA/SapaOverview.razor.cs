using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BlazorApp.Components.Sapa;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Sapa;
using AMprover.BusinessLogic.Models.Tree;
using AMprover.Data.Constants;
using AMprover.Data.Entities.Identity;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using Radzen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using AMprover.BlazorApp.Components;
using Task = System.Threading.Tasks.Task;

namespace AMprover.BlazorApp.Pages.SAPA
{
    public partial class SapaOverview
    {
        [Inject] private IStringLocalizer<SapaOverview> Localizer { get; set; }
        [Inject] private IGlobalDataService GlobalDataService { get; set; }
        [Inject] private ILookupManager LookupManager { get; set; }
        [Inject] private ISapaOverviewManager SapaOverviewManager { get; set; }
        [Inject] private IRiskOrganizerManager RiskOrganizerManager { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; }
        [Inject] private IDropdownManager DropdownManager { get; set; }
        [Inject] private AuthenticationStateProvider AuthenticationStateProvider { get; set; }
        [Inject] private DialogService DialogService { get; set; }
        [Inject] private LocalizationHelper LocalizationHelper { get; set; }
        [Inject] private SapaOverviewQueryParams QueryParams { get; set; }
        [Inject] private UserManager<UserAccount> UserManager { get; set; }
        [Inject] private IJSRuntime JsRuntime { get; set; }
        [Inject] private IPageNavigationManager PageNavigationManager { get; set; }

        private List<string> SapaWorkPackages { get; set; }
        private SapaModel Sapa { get; set; }
        private SapaCollectionModel SapaCollection { get; set; }
        public UtilityGrid<SapaDetailModel> SapaGrid { get; set; }
        public DepartmentModel Department { get; set; }

        public RadzenTabs SapaTabs { get; set; }
        private RadzenTabs DetailTabs { get; set; }
        private RadzenTabs BottomTabs { get; set; }

        private TreeGeneric<SapaTreeObject> SapaTree { get; set; } = new();
        private SapaTreeObject SelectedNode { get; set; }

        public bool ShowTaskInFuture { get; set; }

        public bool ApproveForAllYears
        {
            get => Sapa.ApproveForAllYears;
            set
            {
                Sapa.ApproveForAllYears = value;
                SaveSapa();
            }
        }

        private int ScenarioId => SelectedNode?.ScenarioId ?? 0;
        private int RiskObjectId => SelectedNode?.RiskObjectId ?? 0;
        private int CollectionId => SelectedNode?.SapaCollectionId ?? 0;
        private int SapaId => SelectedNode?.SapaId ?? 0;

        public Dictionary<int, string> RiskObjectDict { get; set; }
        public Dictionary<int, string> ScenarioDict { get; set; }
        private Dictionary<int?, string> InitiatorDict { get; set; }
        private Dictionary<int?, string> ExecutorDict { get; set; }
        public Dictionary<int?, string> StatusDict { get; set; }
        private Dictionary<int?, string> DepartmentDict { get; set; }
        public Dictionary<int?, string> SheqDict { get; set; }
        private Dictionary<int?, string> SelectedSapaDict { get; set; }
        private Dictionary<Status?, string> ItemStatusDict { get; set; }

        private int? SelectedSapa
        {
            get => SapaOverviewManager.GetSelectedSapaCollection(RiskObjectId);
            set
            {
                SapaOverviewManager.SetSelectedSapaCollection(RiskObjectId, value);
                SapaTree.Initialize(GetSapaTree());

                var existingNode = SapaTree.GetFlattenedNodes()
                    .Find(x => x.Source.SapaTreeNodeType == SapaTreeNodeType.RiskObject
                               && x.Source.RiskObjectId == RiskObjectId);

                ClickTreeNode(existingNode);
            }
        }

        #region Chart Data

        public class DataItem
        {
            public string Department { get; set; }
            public double SapaCost { get; set; }
            public string CostString { get; init; }

            public string BudgetCriteria { get; init; }
            public double SapaPercentage { get; set; }
        }

        private DataItem[] ChartDataAssignedBudget { get; set; }
        private DataItem[] ChartDataApprovedCost { get; set; }
        private DataItem[] ChartDataDeclinedCost { get; set; }
        private DataItem[] ChartDataPercOfBudget { get; set; }
        private DataItem[] ChartDataSapaIndex { get; set; }

        private string GraphTitle { get; set; }
        private string BudgetYear { get; set; }
        private string AssignedBudgetYear { get; set; }
        private string TotalApprovedYear { get; set; }
        private string TotalDeclinedYear { get; set; }

        private decimal? BudgetSpent { get; set; }
        private decimal? AverageSapaIndex { get; set; }
        private decimal? MinSapaIndex { get; set; }
        private decimal? MaxSapaIndex { get; set; }
        private decimal? MinBorderSapaIndex { get; set; }
        private decimal? MaxBorderSapaIndex { get; set; }
        decimal? Budget { get; set; }
        decimal? BudgetApproved { get; set; }
        decimal? BudgetDeclined { get; set; }

        private string SheqDonutTitle { get; set; } = "SHEQ";
        private string SheqToggleButton { get; set; }
        private string WpToggleButton { get; set; }
        private string YearToggleButton { get; set; }
        private bool WpButtonVisible { get; set; } = true;
        private decimal TotalBudgetNeed { get; set; }
        private string[] SheqItem { get; set; } = new string[8];
        private decimal[] SumSheqItemApproved { get; set; } = new decimal[8];
        private decimal[] SumSheqItemDeclined { get; set; } = new decimal[8];
        private int[] SumNoSheqItemApproved { get; set; } = new int[8];
        private int[] SumNoSheqItemDeclined { get; set; } = new int[8];
        private int NoSheqItems { get; set; }

        private decimal[,] DepBudgetAssigned { get; set; } = new decimal[6, 6];
        private decimal[,] DepBudgetApproved { get; set; } = new decimal[6, 6];
        private decimal[,] DepBudgetDeclined { get; set; } = new decimal[6, 6];
        private string[,] BudgetDepartment { get; set; } = new string[6, 6];
        private int NoDep { get; set; }

        private List<LookupSettingModel> LookupSettings { get; set; }
        private LookupSettingModel ShowSheqView { get; set; }
        private bool SheqView { get; set; }
        private LookupSettingModel ShowWpView { get; set; }
        private bool WpView { get; set; }
        private LookupSettingModel ShowYearView { get; set; }
        private bool YearView { get; set; }
        public List<string> TempSelectedWorkPackages { get; set; }

        #endregion

        private IEnumerable<string> UserRoles { get; set; }
        private bool IsAdminUser { get; set; }
        public bool IsMainEngineeringUser => UserRoles?.Contains(RoleConstants.MaintenanceEngineering) == true;
        private bool IsFinancialControlUser => UserRoles?.Contains(RoleConstants.FinancialControl) == true;

        // Fix for Duplicate Key issue. This will not be needed when Radzen gets upgraded
        private string ChartKey { get; set; } = Guid.NewGuid().ToString();

        protected override void OnInitialized()
        {
            RiskObjectDict = DropdownManager.GetSapaRiskObjectDict();
            ScenarioDict = DropdownManager.GetSapaScenarioDict();
            InitiatorDict = DropdownManager.GetInitiatorDict().ToNullableDictionary();
            ExecutorDict = DropdownManager.GetExecutorDict().ToNullableDictionary();
            StatusDict = DropdownManager.GetStatusDict().ToNullableDictionary();
            DepartmentDict = DropdownManager.GetDepartmentDict().ToNullableDictionary();
            SheqDict = DropdownManager.GetDepartmentDict().ToNullableDictionary();

            ItemStatusDict = new Dictionary<Status?, string>
            {
                {Status.Budgeting, "Budgeting"},
                {Status.Need_review, "Need Review"},
                {Status.Complete, "Complete"},
            };

            SapaWorkPackages = SapaOverviewManager.GetSapaWorkPackages();
            SapaTree.Initialize(GetSapaTree());
            TempSelectedWorkPackages = [..QueryParams.WorkPackages ?? []];

            TreeNodeGeneric<SapaTreeObject> nodeToSelect = null;
            if (QueryParams.NodeType != null)
            {
                nodeToSelect = SapaTree.GetFlattenedNodes()
                    .Find(x => x.Source.SapaTreeNodeType == QueryParams.NodeType
                               && x.Source.GetNodeId() == QueryParams.NodeId);
            }

            nodeToSelect ??= SapaTree.Node.Nodes.FirstOrDefault();

            if (nodeToSelect != null)
            {
                SapaTree.SelectNode(nodeToSelect);
                ClickTreeNode(nodeToSelect);
                SapaTree.ExpandTreeTillNode(nodeToSelect.Id);
            }

            LookupSettings = LookupManager.GetLookupSettings();
            ShowSheqView =
                LookupSettings.FirstOrDefault(x =>
                    x.Property.Equals(PropertyNames.ShowSheqView, StringComparison.OrdinalIgnoreCase)) ??
                new LookupSettingModel {Property = PropertyNames.ShowSheqView};
            SheqView = ShowSheqView.TextValue == "true";
            ShowYearView =
                LookupSettings.FirstOrDefault(x =>
                    x.Property.Equals(PropertyNames.ShowYearView, StringComparison.OrdinalIgnoreCase)) ??
                new LookupSettingModel {Property = PropertyNames.ShowYearView};
            YearView = ShowYearView.TextValue == "true";
            ShowWpView =
                LookupSettings.FirstOrDefault(x =>
                    x.Property.Equals(PropertyNames.ShowWpView, StringComparison.OrdinalIgnoreCase)) ??
                new LookupSettingModel {Property = PropertyNames.ShowWpView};
            WpView = ShowWpView.TextValue == "true";
        }

        private void SavePageNavigation()
        {
            var uri = new Uri(NavigationManager.Uri);
            PageNavigationManager.SavePageQueryString(uri.AbsolutePath, QueryParams.ToQueryString());
        }

        protected override async Task OnInitializedAsync()
        {
            await SetupUserAccess();
        }

        private bool ApplySelectedWorkPackagesBtnEnabled()
        {
            return TempSelectedWorkPackages?.SequenceEqual(QueryParams.WorkPackages ?? []) == true;
        }

        private async Task UpdateSelectedWorkPackages()
        {
            QueryParams.WorkPackages = [..TempSelectedWorkPackages];
            SapaTree.Initialize(GetSapaTree());

            var nodeToSelect = SapaTree.GetFlattenedNodes()
                .Find(x => x.Source.SapaTreeNodeType == QueryParams.NodeType
                           && x.Source.GetNodeId() == QueryParams.NodeId);

            nodeToSelect ??= SapaTree.Node.Nodes.FirstOrDefault();
            if (nodeToSelect != null)
            {
                SapaTree.SelectNode(nodeToSelect);
                ClickTreeNode(nodeToSelect);
            }

            // Force Refresh for bottomTabs Components
            await Task.Delay(1);
            OnChangeBottomTabsComponent(QueryParams.BottomTabs);
            SavePageNavigation();
        }

        private async Task SetupUserAccess()
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity.IsAuthenticated)
            {
                var userAccount = await UserManager.FindByNameAsync(user.Identity.Name);
                UserRoles = await UserManager.GetRolesAsync(userAccount);
                IsAdminUser = UserRoles?.Contains(RoleConstants.Administrators) == true ||
                              UserRoles?.Contains(RoleConstants.PortfolioAdministrators) == true;
            }
        }

        private SapaTreeNodeType? GetNodeType()
        {
            return SelectedNode?.SapaTreeNodeType;
        }

        public void OnChangeTopTabsComponent(int index)
        {
            CalculateSapa();

            QueryParams.TopTabs = index;
            if (index == 1)
            {
                SetChartData();
                if (SheqView)
                    SetSheqData();
                else
                    SetBudgetData();

                SetToggleBtn();
            }

            SavePageNavigation();
        }

        public void OnChangeBottomTabsComponent(int index)
        {
            CalculateSapa();

            QueryParams.BottomTabs = index;
            if (QueryParams.TopTabs == 1)
            {
                SetChartData();
                if (SheqView)
                    SetSheqData();
                else
                    SetBudgetData();
            }

            SavePageNavigation();
        }

        private bool GetDisabled()
        {
            if (IsFinancialControlUser || IsAdminUser) return false;

            if (Sapa?.Status != Status.Budgeting)
                return true;

            return SelectedNode?.SapaTreeNodeType != SapaTreeNodeType.WorkPackage;
        }

        private bool GetCollectionOrWorkPackageDisabled()
        {
            if (Sapa?.Status != Status.Budgeting)
                return true;

            return SelectedNode?.SapaTreeNodeType != SapaTreeNodeType.WorkPackage
                   && SelectedNode?.SapaTreeNodeType != SapaTreeNodeType.SapaCollection;
        }

        private void ClickTreeNode(TreeNodeGeneric<SapaTreeObject> node)
        {
            SelectedNode = node.Source;
            SapaTree.SelectNode(node);
            QueryParams.NodeType = node.Source.SapaTreeNodeType;
            QueryParams.NodeId = node.Source.GetNodeId();

            if (GetNodeType() == SapaTreeNodeType.WorkPackage)
                GetSapa();
            else
            {
                GetCombinedSapa();

                if (GetNodeType() == SapaTreeNodeType.SapaCollection)
                    SapaCollection = SapaOverviewManager.GetSapaCollection(node.Source.SapaCollectionId ?? 0);
                else if (GetNodeType() == SapaTreeNodeType.RiskObject)
                    SelectedSapaDict = SapaOverviewManager.GetSelectedSapaDict(RiskObjectId);
            }

            SavePageNavigation();
        }

        private void GetCombinedSapa()
        {
            if (RiskObjectId > 0)
                Sapa = SelectedNode.SapaCollectionId > 0
                    ? SapaOverviewManager.GetCombinedSapaModelByCollection(CollectionId, QueryParams.WorkPackages)
                    : SapaOverviewManager.GetCombinedSapaModelByRiskObject(RiskObjectId, QueryParams.WorkPackages);
            else if (ScenarioId > 0)
                Sapa = SapaOverviewManager.GetCombinedSapaModelByScenario(ScenarioId, QueryParams.WorkPackages);

            if (Sapa?.Years != null && QueryParams.BottomTabs > Sapa.Years.Count() - 1)
                QueryParams.BottomTabs = Sapa.Years.Count() - 1;

            OrderDetails();
            RefreshPage();
        }

        private void GetSapa()
        {
            Sapa = SapaOverviewManager.GetSapaModelBySapaId(SapaId);

            if (QueryParams.BottomTabs > Sapa.Years.Count() - 1)
                QueryParams.BottomTabs = Sapa.Years.Count() - 1;

            OrderDetails();
            RefreshPage();
        }

        private void GenerateSapa()
        {
            if (RiskObjectId > 0)
            {
                if (!RiskObjectCanBeGenerated(RiskObjectId))
                    return;
                
                SapaOverviewManager.GenerateSapaByRiskObject(RiskObjectId, Localizer["SapaNoWorkpackage"]);
                SelectedSapaDict = SapaOverviewManager.GetSelectedSapaDict(RiskObjectId);
            }
            else
            {
                throw new ArgumentException($"RiskObjId or ScenarioId has to be set");
            }

            SapaTree.Initialize(GetSapaTree());

            var existingNode = SapaTree.GetFlattenedNodes()
                .Find(x => x.Source.SapaTreeNodeType == SapaTreeNodeType.RiskObject
                           && x.Source.RiskObjectId == RiskObjectId);

            ClickTreeNode(existingNode);
        }
        
        private bool RiskObjectCanBeGenerated(int riskObjectId)
        {
            var validation = SapaOverviewManager.ValidateGenerateSapa(riskObjectId);
            if (validation.LastYear - validation.FirstYear > validation.MaxYears)
            {
                DialogService.Open<InformationDialog>("Validation Failed",
                    new Dictionary<string, object>
                    {
                        { "DialogContent", string.Format(Localizer["SapaMaxYearsError"], validation.FirstYear, validation.LastYear, validation.MaxYears) }
                    },
                    new DialogOptions { Width = "700px", Resizable = true, Draggable = true });

                return false;
            }
            return true;
        }

        private void OrderDetails()
        {
            foreach (var year in Sapa?.Years ?? new List<SapaYearModel>())
            {
                year.Details = year.Details.OrderByDescending(x => x.Approved).ThenByDescending(x => x.RiskSapaIndex);
            }
        }

        public void UpdateSapa()
        {
            switch (SelectedNode.SapaTreeNodeType)
            {
                case SapaTreeNodeType.SapaCollection:
                    if (!RiskObjectCanBeGenerated(RiskObjectId))
                        return;
                    
                    SapaOverviewManager.UpdateSapaCollection(SapaCollection.Id, Localizer["SapaNoWorkpackage"]);
                    SapaCollection = SapaOverviewManager.GetSapaCollection(CollectionId);
                    SapaTree.Initialize(GetSapaTree());
                    var existingNode = SapaTree.GetFlattenedNodes()
                        .Find(x => x.Source.SapaTreeNodeType == SapaTreeNodeType.SapaCollection
                                   && x.Source.SapaCollectionId == CollectionId);
                    ClickTreeNode(existingNode);
                    break;

                case SapaTreeNodeType.RiskObject:
                    if (!RiskObjectCanBeGenerated(RiskObjectId))
                        return;
                    
                    SapaOverviewManager.UpdateSapaByRiskObject(RiskObjectId, Localizer["SapaNoWorkpackage"]);
                    Sapa = SapaOverviewManager.GetCombinedSapaModelByRiskObject(RiskObjectId, QueryParams.WorkPackages);
                    SapaTree.Initialize(GetSapaTree());
                    var existingNodeRo = SapaTree.GetFlattenedNodes()
                        .Find(x => x.Source.SapaTreeNodeType == SapaTreeNodeType.RiskObject
                                   && x.Source.RiskObjectId == RiskObjectId);
                    ClickTreeNode(existingNodeRo);
                    break;

                default:
                    throw new NotImplementedException($"{SelectedNode.SapaTreeNodeType} has not been implemented");
            }

            RefreshPage();
        }

        public void DeleteSapa()
        {
            TreeNodeGeneric<SapaTreeObject> nodeToSelect;

            switch (GetNodeType())
            {
                case SapaTreeNodeType.SapaCollection:
                    nodeToSelect = SapaTree.SelectedNode.Parent;
                    nodeToSelect.Nodes.Remove(SapaTree.SelectedNode);
                    if (nodeToSelect.Nodes.Count > 0)
                        nodeToSelect = nodeToSelect.Nodes.FirstOrDefault();

                    SapaOverviewManager.DeleteBySapaCollection(CollectionId);
                    break;

                case SapaTreeNodeType.RiskObject:
                    nodeToSelect = SapaTree.SelectedNode;
                    nodeToSelect.Nodes.Clear();
                    SapaOverviewManager.DeleteSapaByRiskObject(RiskObjectId);
                    nodeToSelect.Nodes = nodeToSelect.Nodes
                        .Where(x => x.Source.SapaTreeNodeType != SapaTreeNodeType.WorkPackage).ToList();
                    break;

                default:
                    throw new ArgumentException($"{GetNodeType()} was not implemented");
            }

            Sapa = null;
            QueryParams.BottomTabs = 0;

            ClickTreeNode(nodeToSelect);
            RefreshPage();
        }

        private void AcceptAllCallBack(List<SapaDetailModel> sapaOrderedDetails)
        {
            var year = Sapa.Years.Skip(QueryParams.BottomTabs).FirstOrDefault();
            if (year == null) return;

            var budgetApproved = 0m;
            var detailsThisYear = sapaOrderedDetails.Where(x => x.CostYear1 > 0).ToList();
            detailsThisYear.ForEach(x => x.Approved = false);
            foreach (var detail in detailsThisYear)
            {
                if (budgetApproved + detail.CostYear1 > year.Budget)
                    continue;

                budgetApproved += detail.CostYear1;

                SetSapaDetailAccepted(detail, true);
            }

            SaveSapa();
        }

        private void SaveSapaCollection()
        {
            SapaCollection = SapaOverviewManager.SaveSapaCollection(SapaCollection);
            SapaTree.SelectedNode.Name = SapaCollection.Name;
        }

        private void SaveSapa()
        {
            if (GetDisabled())
                return;

            SapaTree.SelectedNode.Name = Sapa.Name;
            Sapa = SapaOverviewManager.SaveSapa(Sapa);
            OrderDetails();
            RefreshPage();
        }

        public void UpdateSapaStatus()
        {
            if (SelectedNode?.RiskObjectId == null)
                return;

            if (Sapa.Status == Status.Need_review)
                SapaOverviewManager.NeedReviewSapa(SelectedNode.RiskObjectId.Value);
            else
                SapaOverviewManager.SetSapaStatus(SelectedNode.RiskObjectId.Value, Sapa.Status);
        }

        public void RefreshPage()
        {
            DetailTabs?.Reload();
            BottomTabs?.Reload();
            CalculateSapa();

            if (QueryParams.TopTabs == 1)
            {
                ChartKey = Guid.NewGuid().ToString();
                SetChartData();
                if (SheqView)
                    SetSheqData();
                else
                    SetBudgetData();
            }
        }

        public void ToggleSheqView()
        {
            ChartKey = Guid.NewGuid().ToString();
            ShowSheqView.TextValue = ShowSheqView.TextValue == "true" ? "false" : "true";
            LookupManager.SaveLookupSettings(ShowSheqView);
            SheqView = ShowSheqView.TextValue == "true";
            SetToggleBtn();
            CalculateSapa();

            if (SheqView)
                SetSheqData();
            else
                SetBudgetData();
        }

        public void ToggleWpView()
        {
            ChartKey = Guid.NewGuid().ToString();
            ShowWpView.TextValue = ShowWpView.TextValue == "true" ? "false" : "true";
            LookupManager.SaveLookupSettings(ShowWpView);
            WpView = ShowWpView.TextValue == "true";
            SetToggleBtn();
            CalculateSapa();
            SetChartData();
        }

        public void ToggleYearView()
        {
            ChartKey = Guid.NewGuid().ToString();
            ShowYearView.TextValue = ShowYearView.TextValue == "true" ? "false" : "true";
            LookupManager.SaveLookupSettings(ShowYearView);
            YearView = ShowYearView.TextValue == "true";
            SetToggleBtn();
            CalculateSapa();
            SetChartData();
        }

        public void SetToggleBtn()
        {
            ChartKey = Guid.NewGuid().ToString();
            SheqToggleButton = SheqView ? SheqDonutTitle + " view" : "Budget view";
            WpToggleButton = WpView ? "Budget type view" : "General view";
            YearToggleButton = YearView ? "Year overview" : "Current year";

            if (YearView || GetNodeType() == SapaTreeNodeType.WorkPackage ||
                GetNodeType() == SapaTreeNodeType.SapaCollection)
                WpButtonVisible = false;
            else
                WpButtonVisible = true;
        }

        public void AcceptSapaDetail(SapaDetailModel sapaDetail)
        {
            SetSapaDetailAccepted(sapaDetail, true);
            SaveSapa();
        }

        public void DeclineSapaDetail(SapaDetailModel sapaDetail)
        {
            SetSapaDetailAccepted(sapaDetail, false);
            SaveSapa();
        }

        private void SetSapaDetailAccepted(SapaDetailModel sapaDetail, bool value)
        {
            if (sapaDetail == null)
                return;

            sapaDetail.Approved = value;
            if (Sapa.ApproveForAllYears)
            {
                var tasksInOtherYears = Sapa.Years.SelectMany(x => x.Details).Where(x => x.TaskId == sapaDetail.TaskId);
                foreach (var t in tasksInOtherYears)
                {
                    t.Approved = value;
                }
            }
        }

        public void CalculateSapa()
        {
            if (Sapa == null) return;

            //reset eventual graph data
            Budget = 0;
            BudgetSpent = 1;

            AverageSapaIndex = 0;
            MinSapaIndex = 0;
            MaxSapaIndex = 0;

            NoDep = 0;

            foreach (var year in Sapa.Years)
            {
                year.BudgetRequest = year.Details.Sum(x => x.CostYear1);
                year.BudgetApproved = year.Details.Where(x => x.Approved).Sum(x => x.CostYear1);
                //Left and Right Donut
                if (GetCurrentYear().Year == year.Year)
                {
                    NoDep = 0; //Reset data (from previous selections)
                    BudgetYear = year.Year.ToString(CultureInfo.InvariantCulture);

                    //Budget Utilization -> Left Donut (%)
                    Budget = year.Budget;
                    BudgetApproved = year.BudgetApproved;
                    BudgetDeclined = year.BudgetRequest - year.BudgetApproved;

                    BudgetSpent = Budget != 0 && BudgetApproved != 0
                        ? BudgetApproved / (Budget ?? (BudgetApproved ?? 1))
                        : 1; // when no budget is given, budget spent = 100%

                    //Sheq Data -> Left Donut (%)
                    if (SheqView)
                    {
                        TotalBudgetNeed = (decimal) BudgetApproved + (decimal) BudgetDeclined;
                        int riskObjId;
                        if (SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.Scenario)
                            riskObjId = (int) SapaOverviewManager.GetRiskObjId((int) SapaTree.SelectedNode?.Source
                                .ScenarioId);
                        else
                            riskObjId = SapaTree.SelectedNode?.Source.RiskObjectId ?? 0;

                        var sheqDict = RiskOrganizerManager.GetSheqItems(riskObjId);

                        NoSheqItems = 0;
                        if (sheqDict != null)
                        {
                            foreach (var item in sheqDict)
                            {
                                if (item.Key == 0) // That's the title of the SHEQ dict
                                {
                                    SheqDonutTitle = item.Value;
                                }
                                else if (NoSheqItems < 8)
                                {
                                    SheqItem[NoSheqItems] = item.Value;
                                    if (TotalBudgetNeed != 0)
                                    {
                                        SumSheqItemApproved[NoSheqItems] =
                                            year.Details.Where(x => x.Approved)
                                                .Where(x => x.RiskSafetyBefore == item.Value).Sum(x => x.CostYear1) /
                                            TotalBudgetNeed;
                                        SumSheqItemDeclined[NoSheqItems] =
                                            year.Details.Where(x => !x.Approved)
                                                .Where(x => x.RiskSafetyBefore == item.Value).Sum(x => x.CostYear1) /
                                            TotalBudgetNeed;
                                    }

                                    SumNoSheqItemApproved[NoSheqItems] = year.Details.Where(x => x.Approved)
                                        .Count(x => x.RiskSafetyBefore == item.Value);
                                    SumNoSheqItemDeclined[NoSheqItems] = year.Details.Where(x => !x.Approved)
                                        .Count(x => x.RiskSafetyBefore == item.Value);
                                    NoSheqItems++;
                                }
                            }
                        }
                    }

                    //Average Sapa Index -> Right Donut
                    AverageSapaIndex = year.BudgetApproved != 0
                        ? year.Details.Where(x => x.Approved).Sum(x => x.CostYear1 * x.RiskSapaIndex) /
                          year.BudgetApproved
                        : 0;

                    MinSapaIndex = (year.Details.Where(x => x.Approved).Min(x => x.RiskSapaIndex) ?? 0);
                    MaxSapaIndex = (year.Details.Where(x => x.Approved).Max(x => x.RiskSapaIndex) ?? 0);

                    MinBorderSapaIndex = MinSapaIndex < 0 ? Math.Round((MinSapaIndex - 10 ?? 0) / 10) * 10 : 0;
                    MaxBorderSapaIndex =
                        MaxSapaIndex < 0
                            ? 0
                            : Math.Round((MaxSapaIndex + 149 ?? 0) / 100) *
                              100; //Make sure there is some grey in the donut to see the values
                }

                //Budget Overview -> Main Graph
                if (GetCurrentYear().Year == year.Year || YearView)
                {
                    var n = 0;
                    GraphTitle = "Overview " + SapaTree.SelectedNode?.Source.Name;
                    int yr = year.Year - GetCurrentYear().Year;

                    if (YearView)
                    {
                        //Show Years
                        if (yr is >= 0 and < 6) //ivm array grootte, six years from chosen year
                        {
                            DepBudgetAssigned[0, yr] = 0; //reset eventual data
                            var sapaList = new List<SapaModel>();
                            if (SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.Scenario)
                                sapaList = SapaOverviewManager.GetSapasOnScenario((int) SapaTree.SelectedNode?.Source
                                    .ScenarioId);
                            else
                                sapaList = SapaOverviewManager.GetSapas(SapaTree.SelectedNode?.Source.RiskObjectId ?? 0);

                            BudgetDepartment[0, yr] = year.Year.ToString();
                            foreach (var item in sapaList)
                            {
                                DepBudgetAssigned[0, yr] += SapaOverviewManager.GetAssignedBudget(item.Id, year.Year);
                                n++;
                                NoDep = n;
                                if (n >= 6) break; //To Do; set up array for number of years to look ahead
                            }

                            DepBudgetApproved[0, yr] = year.Details.Where(x => x.Approved).Sum(x => x.CostYear1);
                            DepBudgetDeclined[0, yr] = year.Details.Sum(x => x.CostYear1) - DepBudgetApproved[0, yr];
                        }
                    }
                    else if ((WpView && SelectedNode?.SapaTreeNodeType != SapaTreeNodeType.WorkPackage) ||
                             (!WpView && SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.SapaCollection))
                    {
                        //Show WorkPackages / Budget types
                        List<SapaModel> sapaList;
                        if (SelectedNode?.SapaTreeNodeType == SapaTreeNodeType.Scenario)
                            sapaList = SapaOverviewManager.GetSapasOnScenario((int) SapaTree.SelectedNode?.Source
                                .ScenarioId);
                        else
                            sapaList = SapaOverviewManager.GetSapas((int) SapaTree.SelectedNode?.Source.RiskObjectId);
                        n = 0;
                        foreach (var item in sapaList)
                        {
                            BudgetDepartment[n, 0] = item.Name;
                            DepBudgetAssigned[n, 0] = SapaOverviewManager.GetAssignedBudget(item.Id, year.Year);
                            DepBudgetApproved[n, 0] = year.Details.Where(x => x.Approved)
                                .Where(x => x.SapaWorkpackageId == item.SapaWorkpackageId).Sum(x => x.CostYear1);
                            DepBudgetDeclined[n, 0] =
                                year.Details.Where(x => x.SapaWorkpackageId == item.SapaWorkpackageId)
                                    .Sum(x => x.CostYear1) - DepBudgetApproved[n, 0];

                            if (DepBudgetApproved[n, 0] != 0 || DepBudgetDeclined[n, 0] != 0) // skip empty value
                            {
                                n++;
                                NoDep = n;
                                if (n >= 6) break; //To Do; set up array for number of used departments
                            }
                        }
                    }
                    else
                    {
                        //Show Departments
                        n = 0;
                        if (DepartmentDict == null) return;

                        foreach (var department in DepartmentDict.ToList())
                        {
                            BudgetDepartment[n, 0] = department.Value;
                            DepBudgetAssigned[n, 0] = (decimal) Budget;
                            DepBudgetApproved[n, 0] = year.Details.Where(x => x.Approved)
                                .Where(x => x.DepDescription == department.Value).Sum(x => x.CostYear1);
                            DepBudgetDeclined[n, 0] =
                                year.Details.Where(x => x.DepDescription == department.Value).Sum(x => x.CostYear1) -
                                DepBudgetApproved[n, 0];

                            if (DepBudgetApproved[n, 0] != 0 || DepBudgetDeclined[n, 0] != 0) // skip empty value
                            {
                                n++;
                                NoDep = n;
                                if (n > 6) break; //To Do; set up array for number of used departments
                            }
                        }
                    }
                }

                if (!YearView)
                {
                    AssignedBudgetYear = Localizer["SapaBudgetAssigned"] + ": " + FormatAsSelectedCurrency(Budget);
                    TotalApprovedYear = Localizer["SapaApproved"] + ": " + FormatAsSelectedCurrency(BudgetApproved);
                    TotalDeclinedYear = Localizer["SapaDeclined"] + ": " + FormatAsSelectedCurrency(BudgetDeclined);
                }
                else
                {
                    AssignedBudgetYear = Localizer["SapaBudgetAssigned"];
                    TotalApprovedYear = Localizer["SapaApproved"];
                    TotalDeclinedYear = Localizer["SapaDeclined"];
                }
            }

            Sapa.Budget = Sapa.Years.Sum(x => x.Budget);
            Sapa.BudgetRequest = Sapa.Years.Sum(x => x.BudgetRequest);
            Sapa.BudgetApproved = Sapa.Years.Sum(x => x.BudgetApproved);
        }

        private TreeNodeGeneric<SapaTreeObject> GetSapaTree() =>
            SapaOverviewManager.GetSapaTree(QueryParams.WorkPackages);

        private string GetFilterTabText()
        {
            return TempSelectedWorkPackages.Count == 0 || TempSelectedWorkPackages.Count == SapaWorkPackages.Count
                ? Localizer["SapaFilterTxt"]
                : $"{Localizer["SapaFilterTxt"]} ({TempSelectedWorkPackages.Count})";
        }

        private SapaYearModel GetCurrentYear()
        {
            return Sapa?.Years.Skip(QueryParams.BottomTabs).FirstOrDefault();
        }

        private string FormatAsSelectedCurrency(decimal? value)
        {
            return value == null
                ? string.Empty
                : value.Value.ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
        }

        public string FormatAsSelectedPercentage(decimal? value)
        {
            return value == null
                ? string.Empty
                : value.Value.ToString("P2", CultureInfo.CreateSpecificCulture(Currency));
        }

        public string FormatAsSelectedNumber(decimal? value)
        {
            return value == null
                ? string.Empty
                : value.Value.ToString("N0", CultureInfo.CreateSpecificCulture(Currency));
        }

        public static string FormatAsEur(object value) =>
            ((double) value).ToString("C0", CultureInfo.CreateSpecificCulture("nl-NL"));

        /// <summary>
        /// Open RiskEdit Page
        /// </summary>
        public void OpenRisksPage(SapaDetailModel model) => OpenRisksPage(model.RiskObjId, model.RiskId);

        public void OpenRisksPage(int riskObjectId, int riskId) =>
            NavigationManager.NavigateTo($"/value-risk-analysis/{riskObjectId}/risks/{riskId}");

        /// <summary>
        /// Open SapaDetail Page
        /// </summary>
        public void OpenSapaDetailsPopup(SapaDetailModel model)
        {
            DialogService.Open<SapaDetailPrint>(Localizer["SapaOverviewTxt"],
                new Dictionary<string, object>
                {
                    {"SapaDetailId", model.Id},
                    {"Callback", EventCallback.Factory.Create<SapaDetailModel>(this, SaveTask)}
                },
                new DialogOptions {Width = "1400px", Resizable = true, Draggable = true});
        }

        public void SaveTask(SapaDetailModel task)
        {
            DialogService.Open<AreYouSureDialog<SapaDetailModel>>(Localizer["RaOverrideCostsTitle"],
                new Dictionary<string, object>
                {
                    {nameof(AreYouSureDialog<SapaDetailModel>.Item), task}
                },
                new DialogOptions {CloseDialogOnEsc = false, ShowClose = false, Resizable = false, Draggable = true});
        }

        public List<string> SheqColorScheme { get; set; }
            =
            [
                "#008000", "#009900", "#00B000", "#33CC33", "#6FDB6F", "#66FF33", "#FFFF99", "#CCFF66", "#FFFF66",
                "#FFFF00", "#F69200", "#FF0000", "#CC0000", "#990033"
            ];

        private void SetChartData()
        {
            if (Sapa == null) return;
            // Create data items for each budget type using loops
            var assignedBudgetItems = new List<DataItem>();
            var approvedCostItems = new List<DataItem>();
            var declinedCostItems = new List<DataItem>();

            if (!YearView)
            {
                for (int i = 0; i < NoDep; i++)
                    // Create data items for each department (0 to NoDep-1)
                {
                    assignedBudgetItems.Add(new DataItem
                    {
                        Department = BudgetDepartment[i, 0],
                        SapaCost = (double) DepBudgetAssigned[i, 0],
                        CostString = FormatAsSelectedCurrency(DepBudgetAssigned[i, 0])
                    });

                    approvedCostItems.Add(new DataItem
                    {
                        Department = BudgetDepartment[i, 0],
                        SapaCost = (double) DepBudgetApproved[i, 0],
                        CostString = FormatAsSelectedCurrency(DepBudgetApproved[i, 0])
                    });

                    declinedCostItems.Add(new DataItem
                    {
                        Department = BudgetDepartment[i, 0],
                        SapaCost = (double) DepBudgetDeclined[i, 0],
                        CostString = FormatAsSelectedCurrency(DepBudgetDeclined[i, 0])
                    });
                }
            }
            else
            {
                int? maxYrs = Sapa.Years.Count() >= 5 ? 5 : Sapa.Years.Count();

                for (int yr = 0; yr < maxYrs; yr++)
                {
                    assignedBudgetItems.Add(new DataItem
                    {
                        Department = BudgetDepartment[0, yr],
                        SapaCost = (double) DepBudgetAssigned[0, yr],
                        CostString = FormatAsSelectedCurrency(DepBudgetAssigned[0, yr])
                    });

                    approvedCostItems.Add(new DataItem
                    {
                        Department = BudgetDepartment[0, yr],
                        SapaCost = (double) DepBudgetApproved[0, yr],
                        CostString = FormatAsSelectedCurrency(DepBudgetApproved[0, yr])
                    });

                    declinedCostItems.Add(new DataItem
                    {
                        Department = BudgetDepartment[0, yr],
                        SapaCost = (double) DepBudgetDeclined[0, yr],
                        CostString = FormatAsSelectedCurrency(DepBudgetDeclined[0, yr])
                    });
                }
            }

            // Assign the created lists to their respective chart data properties
            ChartDataAssignedBudget = assignedBudgetItems.ToArray();
            ChartDataApprovedCost = approvedCostItems.ToArray();
            ChartDataDeclinedCost = declinedCostItems.ToArray();

            ChartDataSapaIndex =
            [
                new DataItem
                {
                    BudgetCriteria = "Minimum Sapa Index",
                    SapaPercentage = (double) (MinSapaIndex ?? 0),
                    CostString = FormatAsSelectedNumber(MinSapaIndex ?? 0)
                },
                new DataItem
                {
                    BudgetCriteria = "Under Average",
                    SapaPercentage = (double) ((AverageSapaIndex ?? 0) - (MinSapaIndex ?? 0)),
                    CostString = FormatAsSelectedNumber(AverageSapaIndex ?? 0)
                },
                new DataItem //NB. Needs to be taken into account twice, so a line for the average is drawn
                {
                    BudgetCriteria = "Average Sapa Index",
                    SapaPercentage = 1,
                    CostString = FormatAsSelectedNumber(AverageSapaIndex ?? 0)
                },
                new DataItem
                {
                    BudgetCriteria = "Over Average",
                    SapaPercentage = (double) ((MaxSapaIndex ?? 0) - (AverageSapaIndex ?? 0)),
                    CostString = FormatAsSelectedNumber(MaxSapaIndex ?? 0)
                },
                new DataItem
                {
                    BudgetCriteria = "Maximum Value",
                    SapaPercentage =
                        (double) ((MaxBorderSapaIndex ?? 0) - (MinBorderSapaIndex ?? 0) - (MaxSapaIndex ?? 0)),
                    CostString = FormatAsSelectedNumber((MaxBorderSapaIndex ?? 0) - (MinBorderSapaIndex ?? 0))
                },
                new DataItem
                {
                    BudgetCriteria = "Negative Sapa Index",
                    SapaPercentage = (double) (MinBorderSapaIndex ?? 0),
                    CostString = FormatAsSelectedNumber(MinBorderSapaIndex ?? 0)
                }
            ];

            // Handle the view-specific data
            if (SheqView) // Left Donut - SHEQ view
                SetSheqData();
            else
                SetBudgetData();
        }

        private void SetBudgetData()
        {
            if (BudgetSpent <= 1)
            {
                ChartDataPercOfBudget =
                [
                    new DataItem //Approved Budget
                    {
                        BudgetCriteria = "Approved",
                        SapaCost = (double) (BudgetSpent ?? 0),
                        CostString = FormatAsSelectedPercentage(BudgetSpent ?? 0)
                    },
                    new DataItem //Rest Budget
                    {
                        BudgetCriteria = "Rest Budget",
                        SapaCost = (double) (1 - (BudgetSpent ?? 0)),
                        CostString = FormatAsSelectedPercentage(1 - (BudgetSpent ?? 0))
                    }
                ];
            }
            else if (BudgetSpent > 1)
            {
                ChartDataPercOfBudget =
                [
                    new DataItem //Overrun
                    {
                        BudgetCriteria = "Budget Overrrun",
                        SapaCost = (double) ((BudgetSpent ?? 0) - 1),
                        CostString = FormatAsSelectedPercentage((BudgetSpent ?? 0) - 1)
                    },
                    new DataItem //Approved Budget
                    {
                        BudgetCriteria = "Approved",
                        SapaCost = (double) (2 - (BudgetSpent ?? 0)),
                        CostString = FormatAsSelectedPercentage(1)
                    }
                ];
            }
        }

        private void SetSheqData()
        {
            var dataItems = new List<DataItem>();

            // Determine the highest index based on NoSheqItems
            var highestIndex = NoSheqItems;

            // Add approved items in reverse order (highest to lowest index)
            for (var i = highestIndex; i >= 0; i--)
            {
                dataItems.Add(new DataItem
                {
                    BudgetCriteria = SheqItem[i],
                    SapaCost = (double) SumSheqItemApproved[i],
                    CostString = FormatAsSelectedPercentage(SumSheqItemApproved[i]) + ", " + SumNoSheqItemApproved[i] +
                                 " items approved"
                });
            }

            // Add declined items in reverse order (highest to lowest index)
            for (var i = highestIndex; i >= 0; i--)
            {
                dataItems.Add(new DataItem
                {
                    BudgetCriteria = SheqItem[i],
                    SapaCost = (double) SumSheqItemDeclined[i],
                    CostString = FormatAsSelectedPercentage(SumSheqItemDeclined[i]) + ", " + SumNoSheqItemDeclined[i] +
                                 " items declined"
                });
            }

            ChartDataPercOfBudget = dataItems.ToArray();
        }

        public string Currency => "nl-NL";

        public void PrintPage()
        {
            // Implement print functionality
        }
    }
}