﻿@page "/value-risks-on-abs/assign-assets"

<h2>@Localizer["AaHeaderTxt"]</h2>

<div class="row header-navigation">
    <div class="col-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
                    Home
                </NavLink>
                <NavLink class="breadcrumb-item" href="value-risks-on-abs" Match="NavLinkMatch.All">
                    Value Risks on ABS
                </NavLink>
                <NavLink class="breadcrumb-item" aria-current="page" Match="NavLinkMatch.All">
                    Assign Assets
                </NavLink>
            </ol>
        </nav>
    </div>
    <div class="col-8 text-right ">
        <Information class="float-right my-2 mr-2" DialogTitle=@Localizer["AaMenuTitle"] DialogContent=@Localizer["AaMenuTxt"]/>
        <RadzenButton class="float-right my-2 mx-2" Icon="refresh" Text=@Localizer["AaRebindAssetsBtn"] ButtonStyle=ButtonStyle.Secondary Click=@RebindAssets Disabled=!GlobalDataService.CanEdit />
    </div>
</div>

<p>@((MarkupString)Localizer["AaSubHeaderTxt"].Value)</p>

<div class="row">
    <div class="col-3">

        <div class="row">
            <div class="col-12">
                <AMDropdown AllowFiltering="true"
                            Data="@RiskScenarios.ToDictionary(x => x.Id, x => x.Name)"
                            @bind-value=SelectedScenario
                            Change=@(args => SelectScenario(args))
                            Label="Scenario"
                            ContainerClass="mb-2"/>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div style="overflow: auto;">
                    <TreeComponent TItem=RiskTreeObject
                                   Treeview=RiskTree
                                   NodeClickCallback=ClickTreeNode
                                   Height="270" />
                </div>
            </div>
        </div>

    </div>
    <div class="col-xl-9 col-md-8">
        <p class="bold">@RiskTree.SelectedNode?.Source.Name - @GetSelectedNodeObjectName() - @FilteredAssets.Count() Assets</p>
        <div class="row">
            <div class="col-1 pr-0">
                <AMDropdown Data=@AndOrOptions.ToDictionary(x => x, x => x)
                                                                                                                @bind-Value="NewFilterQueryRow.AndOr"
                            Change=@(() => RefreshPreviewFilter())
                            Label="@nameof(FilterQueryRow.AndOr)" />
            </div>

            <div class="col-2 pr-0">
                <AMDropdown Data=@FieldNames.ToDictionary(x => x.Key, x => x.Value.Name)
                                                                                                                @bind-Value="NewFilterQueryRow.FieldName"
                            Change=@(() => ChangeFieldName())
                            Label="@nameof(FilterQueryRow.FieldName)" 
                            AllowFiltering=true />
            </div>

            <div class="col-1 pr-0">
                <AMDropdown Data=@CriteriumOptions.ToDictionary(x => x, x => x.ToString())
                                                                                                                @bind-Value="NewFilterQueryRow.Criterium"
                            Change=@RefreshPreviewFilter
                            Label="@nameof(FilterQueryRow.Criterium)" />
            </div>

            <div class="col-2 pr-0">

                <div class="form-group">
                    <div class="neg-margin-small">
                        <label class="mb-0">Query:</label>
                    </div>

                    <input type=@GetFieldTypeForInputField()
                                                                                                                                                                                                                                @bind-value="PreviewText"
                           @bind-value:event="oninput"
                           class="margin-right-5 form-control" />
                </div>
            </div>

            <div class="col-6 pr-0">

                <div class="float-left mt-3 mr-3">
                    <RadzenButton Icon="add" Size="ButtonSize.Medium"
                                  Click="@(_ => AddFilter())"
                                  Disabled=@(RiskTree.SelectedNode == null || string.IsNullOrWhiteSpace(PreviewText) || !GlobalDataService.CanEdit)
                                  class="margin-right-5" />
                </div>
                
                <div class="float-left mr-3 mt-4">
                    @Localizer["AaShowTxt"]&nbsp;
                    <Radzen.Blazor.RadzenCheckBox TValue="bool"
                                                  @bind-Value=@OnlyUsePreviewFilter
                                                  Change=@RefreshPreviewFilter />
                </div>

                <div class="float-left mt-3">
                    <RadzenButton Icon="refresh" Text="Reset dropdowns"
                                  ButtonStyle=ButtonStyle.Primary
                                  Click=@ResetDropdown
                                  Disabled=!GlobalDataService.CanEdit />
                </div>

            </div>
        </div>
        <div class="mt-6">
            <RadzenDataGrid style="background-color: lightgoldenrodyellow" @ref=FilterQueryRowGrid AllowFiltering="false" AllowPaging="false" AllowSorting="false" EditMode="DataGridEditMode.Single"
                            Data="@AllSelectionFilters" TItem="FilterQueryRow">
                <Columns>

                    <!-- Edit Options -->
                    <RadzenDataGridColumn TItem="FilterQueryRow" Context="sampleBlazorModelsSampleOrder" Filterable="false" Sortable="false" TextAlign="TextAlign.Center" Width="95px">

                        <Template Context="filterQueryRow">
                            @if (filterQueryRow.Inherited == false && UserCanEdit)
                            {
                                <AMproverSplitButton Text="Edit" class="float-right m-0 p-0" Click=@(args => SplitButtonItemClicked(args, filterQueryRow)) >
                                    <ChildContent>
                                        <AMproverSplitButtonItem Text="Edit" Value="Edit" Image="/svg/pencil.svg" />
                                        <AMproverSplitButtonItem Text="Delete" Value="Delete" Image="/svg/trash-can.svg" />
                                    </ChildContent>
                                </AMproverSplitButton>
                            }
                        </Template>

                        <EditTemplate Context="filterQueryRow">
                            <div class="rz-splitbutton rz-buttonset float-right m-0 p-0">

                                <!-- Save Inline Changes-->
                                <RadzenButton Click=@(_ => SaveRowChanges(filterQueryRow))
                                              class="amprover-splitbutton ampover-combined-button"
                                              Text="Save" type="button"/>

                                <!-- Cancel Inline Changes-->
                                <RadzenButton Click=@(_ => CancelEditRow(filterQueryRow)) type="button"
                                              class="rz-splitbutton-menubutton rz-button-icon-only ampover-combined-button">
                                    <i class="fas fa-undo amprover-split-button-right-content"></i>
                                </RadzenButton>

                            </div>
                        </EditTemplate>

                    </RadzenDataGridColumn>

                    <!-- AndOr -->
                    <RadzenDataGridColumn TItem="FilterQueryRow" Property="@nameof(FilterQueryRow.AndOr)" Title="@nameof(FilterQueryRow.AndOr)">
                        <Template Context="filterQueryRow">
                            <span class=@(filterQueryRow.Inherited ? "font-italic" : "")>
                                <!-- The first filter in a filter list ignores the And/Or property. This is not shown in the database but we will show it in the UI -->
                                @(filterQueryRow == AllSelectionFilters.FirstOrDefault() ? "-" : filterQueryRow.AndOr)
                            </span>
                        </Template>
                        <EditTemplate Context="filterQueryRow">
                            <AMDropdown Data=@AndOrOptions.ToDictionary(x => x, x => x) 
                                                                                                                                                                @bind-Value="filterQueryRow.AndOr" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <!-- FieldName -->
                    <RadzenDataGridColumn TItem="FilterQueryRow" Property="@nameof(FilterQueryRow.FieldName)" Title="@nameof(FilterQueryRow.FieldName)">
                        <Template Context="filterQueryRow">
                            <span class=@(filterQueryRow.Inherited ? "font-italic" : "")>@FieldNames[filterQueryRow.FieldName].Name</span>
                        </Template>
                        <EditTemplate Context="filterQueryRow">
                            <AMDropdown Data=@FieldNames.ToDictionary(x => x.Key, x => x.Value.Name)
                                                                                                                                                                    @bind-Value="filterQueryRow.FieldName"
                                        Label="@nameof(FilterQueryRow.FieldName)" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <!-- Criteria -->
                    <RadzenDataGridColumn TItem="FilterQueryRow" Property="@nameof(FilterQueryRow.Criterium)" Title="@nameof(FilterQueryRow.Criterium)">
                        <Template Context="filterQueryRow">
                            <span class=@(filterQueryRow.Inherited ? "font-italic" : "")>@filterQueryRow.Criterium</span>
                        </Template>
                        <EditTemplate Context="filterQueryRow">
                            <AMDropdown Data=@CriteriumOptions.ToDictionary(x => x, x => x.ToString()) 
                                                                                                                                                                @bind-Value="filterQueryRow.Criterium" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <!-- Selection -->
                    <RadzenDataGridColumn TItem="FilterQueryRow" Property="@nameof(FilterQueryRow.Selection)" Title="@nameof(FilterQueryRow.Selection)">
                        <Template Context="filterQueryRow">
                            <span class=@(filterQueryRow.Inherited ? "font-italic" : "")>@filterQueryRow.Selection</span>
                        </Template>
                        <EditTemplate Context="filterQueryRow">
                            <RadzenTextBox @bind-Value="filterQueryRow.Selection" Name="@nameof(FilterQueryRow.Selection)" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                </Columns>
            </RadzenDataGrid>
        </div>
    </div>
</div>

<!-- Preview Selection Grid -->
<div class="abs-result-preview-container">
    <div class="row spacer">
        <div class="col-12">
            <UtilityGrid TItem=AssetModel
                         @ref=AssetGrid
                         Data=FilteredAssets
                         FileName=@GridNames.AssignAbs.Assets
                         MaxRows=10
                         AllowXlsExport=true
                         AllowFiltering=false />
        </div>
    </div>
</div>
