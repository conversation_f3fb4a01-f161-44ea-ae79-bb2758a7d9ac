using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.ABS;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using Radzen.Blazor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components.ABS;
using FieldInfo = AMprover.BusinessLogic.Models.ABS.FieldInfo;
using Microsoft.Extensions.Localization;
using AMprover.BlazorApp.Components.SplitButton;

namespace AMprover.BlazorApp.Pages.AssignABS;

public partial class AssignAssets
{
    [Inject] private ILogger<AssignAssets> Logger { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IAssetBreakdownManager AssetBreakdownManager { get; set; }
    [Inject] private IAssignAssetManager AssignAssetManager { get; set; }
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private IScenarioManager ScenarioManager { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; }
    [Inject] private IPageNavigationManager PageNavigationManager { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IGlobalDataService GlobalDataService { get; set; }
    [Inject] private IStringLocalizer<AssignAssets> Localizer { get; set; }

    private bool UserCanEdit => GlobalDataService.CanEdit;

    private Dictionary<ObjectLevel, string> ObjectLevels { get; set; } = new();

    public int SelectedScenario { get; set; }

    private string _previewText = string.Empty;
    private string PreviewText
    {
        get => _previewText;
        set
        {
            _previewText = value;
            RefreshPreviewFilter();
        }
    }

    private bool OnlyUsePreviewFilter { get; set; }

    private List<AssetModel> _allAssets = [];

    private List<ScenarioModel> RiskScenarios { get; set; }

    public UtilityGrid<AssetModel> AssetGrid { get; set; }

    private List<GridColumnModel> AssetTableColumns { get; set; } = [];

    public TreeGeneric<RiskTreeObject> RiskTree { get; set; } = new();

    private List<List<FilterQueryRow>> InheritedSelectionFiltersGroups { get; set; } = [];

    private List<FilterQueryRow> AllSelectionFilters { get; set; } = [];

    private RadzenDataGrid<FilterQueryRow> FilterQueryRowGrid { get; set; }

    private readonly List<DatabaseSelectionCriterium> _allCriteriumOptions =
        Enum.GetValues(typeof(DatabaseSelectionCriterium)).Cast<DatabaseSelectionCriterium>().ToList();

    private List<DatabaseSelectionCriterium> CriteriumOptions { get; set; } = [];

    private IEnumerable<string> AndOrOptions { get; } = ["And", "Or"];

    private Dictionary<string, FieldInfo> FieldNames { get; set; } = new();

    public List<AssetModel> FilteredAssets { get; set; } = [];

    private List<string> AssetColumns { get; } = [];

    public FilterQueryRow NewFilterQueryRow { get; set; } = new()
    {
        FieldName = "Code",
        Criterium = DatabaseSelectionCriterium.Contains,
        FieldType = DatabaseFieldType.String
    };

    protected override void OnInitialized()
    {
        ObjectLevels = ObjectManager.GetObjectLevelNames();
        AssetTableColumns = ForceGetAssetColumns();
        FieldNames = GetFieldNamesDropdown(AssetTableColumns);
        RiskScenarios = ScenarioManager.GetAllScenarios().OrderBy(x => x.Name).ToList();
        SelectedScenario = PageNavigationManager.GetSelectedScenario() ?? RiskScenarios.FirstOrDefault()?.Id ?? 0;
        _allAssets = AssetBreakdownManager.GetAllAssets();
        CriteriumOptions = [.. _allCriteriumOptions];
        SelectScenario(SelectedScenario);
    }

    private static Dictionary<string, FieldInfo> GetFieldNamesDropdown(List<GridColumnModel> grid)
    {
        var result = new Dictionary<string, FieldInfo>();
        foreach (var column in grid)
        {
            var type = column.FieldType == FieldType.Text
                ? DatabaseFieldType.String
                : DatabaseFieldType.Int;

            result.Add(column.FieldName, new FieldInfo { Name = column.FieldName, Type = type });
        }

        return result;
    }

    private List<GridColumnModel> ForceGetAssetColumns()
    {
        var result = LookupManager.GetColumns("assignAbsPreviewControl", true).ToList();
        return result.Any() ? result : LookupManager.SeedGridColumns<AssetModel>("assignAbsPreviewControl");
    }

    private void EditRow(FilterQueryRow queryRow)
    {
        FilterQueryRowGrid.EditRow(queryRow);
    }

    private void CancelEditRow(FilterQueryRow row)
    {
        // Undo Changes
        RiskTree.SelectedNode.Source.Filters =
            AssignAssetManager.GetFilterSelectionListFromTreeNode(RiskTree.SelectedNode.Source);
        UpdateFilterGridAndResultView();

        // Stop Edit
        FilterQueryRowGrid.CancelEditRow(row);
    }

    private void SaveRowChanges(FilterQueryRow row)
    {
        // Set fieldType based on FieldName
        row.FieldType = FieldNames[row.FieldName].Type;

        // Prevent incompatible Criteria with fieldType
        if (row.FieldType == DatabaseFieldType.Int &&
            row.Criterium is DatabaseSelectionCriterium.Contains
                or DatabaseSelectionCriterium.StartsWith
                or DatabaseSelectionCriterium.EndsWith)
            row.Criterium = DatabaseSelectionCriterium.Equals;

        // Push changes to DB
        AssignAssetManager.UpdateFilter(row);

        // Stop Edit
        FilterQueryRowGrid.CancelEditRow(row);

        // Update result
        UpdateFilterGridAndResultView();
    }

    private void RefreshPreviewFilter()
    {
        var previewFilter = new FilterQueryRow
        {
            AndOr = NewFilterQueryRow.AndOr,
            FieldName = NewFilterQueryRow.FieldName,
            Criterium = NewFilterQueryRow.Criterium,
            FieldType = FieldNames[NewFilterQueryRow.FieldName].Type,
            Selection = PreviewText
        };

        UpdateFilterGridAndResultView(previewFilter);
    }

    private void ChangeFieldName()
    {
        // Set fieldType based on FieldName
        NewFilterQueryRow.FieldType = FieldNames[NewFilterQueryRow.FieldName].Type;

        // Prevent incompatible Criteria with fieldType
        if (NewFilterQueryRow.FieldType == DatabaseFieldType.Int &&
            NewFilterQueryRow.Criterium is DatabaseSelectionCriterium.Contains
                or DatabaseSelectionCriterium.StartsWith
                or DatabaseSelectionCriterium.EndsWith)
            NewFilterQueryRow.Criterium = DatabaseSelectionCriterium.Equals;

        RefreshPreviewFilter();
    }

    private void AddFilter()
    {
        if (string.IsNullOrWhiteSpace(_previewText))
            return;

        NewFilterQueryRow.Selection = _previewText;

        RiskTree.SelectedNode.Source.Filters = AssignAssetManager
            .AddFilter(RiskTree.SelectedNode.Source, NewFilterQueryRow, SelectedScenario);

        SetFiltered(RiskTree.SelectedNode);

        PreviewText = string.Empty;

        UpdateFilterGridAndResultView();
    }

    private static void SetFiltered(TreeNodeGeneric<RiskTreeObject> node)
    {
        node.FilteredState = FilteredState.Self;
        SetFilteredRecursive(node.Nodes);
    }

    private static void SetFilteredRecursive(List<TreeNodeGeneric<RiskTreeObject>> risks)
    {
        foreach (var node in risks)
        {
            if (node.FilteredState == FilteredState.None)
                node.FilteredState = FilteredState.Inherited;

            SetFilteredRecursive(node.Nodes);
        }
    }

    private void ResetDropdown()
    {
        NewFilterQueryRow = new FilterQueryRow
        {
            FieldName = "Code",
            Criterium = DatabaseSelectionCriterium.Contains,
            FieldType = DatabaseFieldType.String
        };
        PreviewText = string.Empty;
    }

    private void DeleteRow(FilterQueryRow row)
    {
        // Remove Filter and refresh Grid
        RiskTree.SelectedNode.Source.Filters = AssignAssetManager.DeleteFilter(row);
        if (!RiskTree.SelectedNode.Source.HasFilters())
            SetNotFiltered(RiskTree.SelectedNode);

        UpdateFilterGridAndResultView();
    }

    private void SplitButtonItemClicked(AMproverSplitButtonItem args, FilterQueryRow item)
    {
        if (args?.Value == "Delete")
        {
            DeleteRow(item);
        }
        else
        {
            EditRow(item);
        }
    }

    private static void SetNotFiltered(TreeNodeGeneric<RiskTreeObject> node)
    {
        if (node.Parent != null && node.Parent.FilteredState != FilteredState.None)
        {
            node.FilteredState = FilteredState.Inherited;
        }
        else
        {
            node.FilteredState = FilteredState.None;
            SetNotFilteredRecursive(node.Nodes);
        }
    }

    private static void SetNotFilteredRecursive(IEnumerable<TreeNodeGeneric<RiskTreeObject>> risks)
    {
        foreach (var node in risks.Where(node => !node.Source.HasFilters()))
        {
            node.FilteredState = FilteredState.None;
            SetNotFilteredRecursive(node.Nodes);
        }
    }

    private void SetColumns(IReadOnlyCollection<AssetModel> assets)
    {
        AssetColumns.Clear();
        foreach (var field in typeof(AssetModel).GetFields(BindingFlags.NonPublic | BindingFlags.Instance))
        {
            foreach (var assetName in from asset in assets
                     where field.GetValue(asset) != null
                     select field.Name.Split('<')[1].Split('>')[0] into assetName
                     where assetName.ToLower() != "id" && assetName.ToLower() != "parentid" &&
                           assetName.ToLower() != "pickassets"
                     select assetName)
            {
                AssetColumns.Add(assetName);
                break;
            }
        }
    }

    private void SelectScenario(object args)
    {
        SelectedScenario = (int)args;
        RiskTree.Initialize(RiskAnalysisManager.GetRisksTreeByScenario(SelectedScenario));

        var filters = AssignAssetManager.GetFiltersForScenario(SelectedScenario);
        //Filter out the filters that have a none existing field
        filters = filters.Where(x => x.FilterRows.Any(y => FieldNames.ContainsKey(y.FieldName))).ToList();

        AttachFiltersToNodes(filters, RiskTree.Node);

        InheritedSelectionFiltersGroups.Clear();
        UpdateFilterGridAndResultView();

        if (RiskTree.Node?.Nodes?.Any() == true)
        {
            RiskTree.SelectNode(RiskTree.Node.Nodes.First());
            PreviewText = ""; // Force a re-render
        }

        PageNavigationManager.SetSelectedScenario(SelectedScenario);
    }

    private void AttachFiltersToNodes(List<FilterGroup> filterGroups, TreeNodeGeneric<RiskTreeObject> tree)
    {
        if (filterGroups == null) throw new ArgumentNullException(nameof(filterGroups));
        var flattenedTree = tree.Nodes.Flatten(n => n.Nodes).ToList();

        foreach (var node in flattenedTree)
        {
            var filter = filterGroups.FirstOrDefault(
                x => x.ScenarioId == SelectedScenario
                     && x.RiskObjectId == node.Source.RiskObjectId
                     && x.CollectionId == node.Source.CollectionId
                     && x.InstallationId == node.Source.InstallationId
                     && x.SystemId == node.Source.SystemId
                     && x.AssemblyId == node.Source.AssemblyId
                     && x.ComponentId == node.Source.ComponentId
                     && x.RiskId == node.Source.RiskId);

            if (filter == null) continue;
            filterGroups.Remove(filter);
            node.Source.Filters = filter;
            node.FilteredState = FilteredState.Self;
            SetFilteredRecursive(node.Nodes);

            if (!filterGroups.Any())
                break;
        }
    }

    public void ClickTreeNode(TreeNodeGeneric<RiskTreeObject> node)
    {
        RiskTree.SelectedNode = node;
        UpdateFilterGridAndResultView();
    }

    private void UpdateFilterGridAndResultView(FilterQueryRow previewFilter = null)
    {
        // All Filters Combined
        AllSelectionFilters.Clear();
        InheritedSelectionFiltersGroups.ForEach(objLevel => objLevel.ForEach(filter => filter.Inherited = true));
        AllSelectionFilters.AddRange(InheritedSelectionFiltersGroups.SelectMany(x => x));

        RiskTree.SelectedNode?.Source.GetFilterRows()?.ForEach(x => x.Inherited = false);
        AllSelectionFilters.AddRange(RiskTree.SelectedNode?.Source.GetFilterRows() ?? []);

        // include previewFilter
        var allFiltersPlusPreviewFilter = new List<FilterQueryRow>(AllSelectionFilters);
        if (!string.IsNullOrWhiteSpace(previewFilter?.Selection) && FieldNames.ContainsKey(previewFilter.FieldName))
            allFiltersPlusPreviewFilter.Add(previewFilter);

        // Radzen Grid doesn't automatically update
        FilterQueryRowGrid?.Reload();

        // Get Filtered Assets
        if (OnlyUsePreviewFilter)
        {
            FilteredAssets = previewFilter != null
                ? AssetBreakdownManager.GetFilteredAssets(_allAssets, [previewFilter])
                : AssetBreakdownManager.GetFilteredAssets(_allAssets, []);
        }
        else
        {
            FilteredAssets = AssetBreakdownManager.GetFilteredAssets(_allAssets, allFiltersPlusPreviewFilter);
        }

        // Update Result grid, which columns to show
        SetColumns(FilteredAssets);
    }

    private string GetSelectedNodeObjectName()
    {
        var nodeType = RiskTree.SelectedNode?.Source.NodeType;

        if (Enum.TryParse<ObjectLevel>(nodeType?.ToString(), true, out var objectLevel))
            return GetObjectLevel(objectLevel);

        return nodeType?.ToString() ?? Strings.NotAvailable;
    }

    private string GetObjectLevel(ObjectLevel objectLevel)
    {
        if (ObjectLevels.TryGetValue(objectLevel, out var level))
            return level;

        Logger.LogError($"{nameof(GetObjectLevel)} was called but {nameof(ObjectLevels)} has not been filled yet");
        return objectLevel.ToString();
    }

    private string GetFieldTypeForInputField()
    {
        if (NewFilterQueryRow?.FieldName == null)
            return "text";

        return FieldNames[NewFilterQueryRow.FieldName].Type == DatabaseFieldType.String
            ? "text"
            : "number";
    }

    private async Task RebindAssets()
    {
        ShowLoadingDialog();

        // Force loading dialog to pop up
        await Task.Delay(1);

        AssignAssetManager.LinkAssets(RiskScenarios.Select(x => x.Id).ToList(), _allAssets);
        DialogService.Close();
        ShowCompleteDialog("Successfully linked assets to Risks");
    }

    private void ShowLoadingDialog()
    {
        DialogService.Open<RebindAssetResultDialog>("Loading",
            new Dictionary<string, object>
            {
                {nameof(RebindAssetResultDialog.Loading), true},
                {nameof(RebindAssetResultDialog.Text), "Please wait"}
            });
    }

    private void ShowCompleteDialog(string text)
    {
        DialogService.Open<RebindAssetResultDialog>("Completed",
            new Dictionary<string, object>
            {
                {nameof(RebindAssetResultDialog.Loading), false},
                {nameof(RebindAssetResultDialog.Text), text}
            });
    }
}