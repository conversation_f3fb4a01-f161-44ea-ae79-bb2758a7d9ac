﻿@page "/"
@inject IStringLocalizer<Index> _localizer

<div class="landing-page-container">
    <div class="row margin-top-70">
        <div class="col-sm-2">
        </div>
        <div class="col-sm-2">
            <div class="header-item align-items-center text-center">
                <p>@_localizer["IndexFocusTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            <div class="link-item" @ref="DataPreparation"
                 @onclick=@(() => Navigate("/data-preparation"))>
                <div class="bg-data-preparation"></div>
                <p>@_localizer["IndexDataPrepTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            <div class="link-item" @ref="RiskMatrix"
                 @onclick=@(() => Navigate("/value-risk-matrix"))>
                <div class="bg-matrix"></div>
                <p>@_localizer["IndexValueRiskMatrixTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2 empty">
            <div class="link-item" @ref="CriticalityRanking"
                 @onclick=@(() => Navigate("/criticality-ranking"))>
                <div class="bg-criticality-ranking"></div>
                <p>@_localizer["IndexCritRankTxt"]</p>
            </div>
        </div>
    </div>
    <div class="row align-items-center">
        <div class="col-sm-2">
        </div>
        <div class="col-sm-2">
            <div class="header-item align-items-center text-center">
                <p>@_localizer["IndexAnalyseTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            <div class="link-item" @ref="RcmFmecaStudy"
                 @onclick=@(() => Navigate("/value-risk-organizer"))>
                <div class="bg-rcm-fmeca-study"></div>
                <p>@_localizer["IndexRcmFmecaTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            <div class="link-item" @ref="PmoStudy"
                 @onclick=@(() => Navigate("/value-risk-organizer?AnalysisType=pmo"))>
                <div class="bg-prev-maint-optimization"></div>
                <p>@_localizer["IndexPmoTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            @*@if (FeatureTurnedOn("Rams"))
            {*@
                <div class="link-item" @ref="RamsStudy"
                    @onclick=@(() => Navigate("/rams"))>
                    <div class="bg-rams-analysis"></div>
                    <p>@_localizer["IndexRAMSTxt"]</p>
                </div>
           @* }
            else
            {
                <div class="link-item" @ref=RamsStudy
                     @onmouseover=@(() => ShowTooltip(RamsStudy, _localizer["IndexRamsToolTip"]))
                         @onmouseleave=@(() => HideTooltip(RamsStudy))>
                    <div class="bg-rams-analysis"></div>
                    <p>@_localizer["IndexRAMSTxt"]</p>
                </div>
            }*@
        </div>
    </div>
    <div class="row align-items-center">
        <div class="col-sm-2">
        </div>
        <div class="col-sm-2">
            <div class="header-item align-items-center text-center">
                <p>@_localizer["IndexOptTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2">
            <div class="link-item" @ref="LccStudy"
                 @onclick=@(() => Navigate("/lcc"))>
                <div class="bg-life-cycle-costing"></div>
                <p>@_localizer["IndexLccTxt"]</p>
            </div>
        </div>
        @*<div class="col-sm-2">
            <div class="link-item" @ref=SparePartStudy
                 @onclick=@(() => Navigate("/reports"))
                 @onmouseover=@(() => ShowTooltip(SparePartStudy, _localizer["IndexSparePartToolTip"]))
                 @onmouseleave=@(() => HideTooltip(SparePartStudy))>
                <div class="bg-define-spare-parts"></div>
                <p>@_localizer["IndexSparePartTxt"]</p>
            </div>
        </div>*@
        <div class="col-sm-2">
            <div class="link-item" @ref="BuildWorkPackages"
                 @onclick=@(() => Navigate("/cluster"))>
                <div class="bg-build-work-packages"></div>
                <p>@_localizer["IndexWorkPackagesTxt"]</p>
            </div>
        </div>
        @*<div class="col-sm-2">
            <div class="link-item" @ref=LinkPmToAbs
            @onclick=@(() => Navigate("/assign-assets"))>
                <div class="bg-link-risks-to-abs"></div>
                <p>@_localizer["IndexLinkPmAbsTxt"] </p>
            </div>
        </div>*@
        <div class="col-sm-2">
            <div class="link-item" @ref="RiskOnAbs"
                 @onclick=@(() => Navigate("/value-risks-on-abs"))>
                <div class="bg-rabs"></div>
                <p>@_localizer["IndexLinkVrOmAbsTxt"]</p>
            </div>
        </div>
    </div>
    <div class="row align-items-center">
        <div class="col-sm-2">
        </div>
        <div class="col-sm-2">
            <div class="header-item align-items-center text-center">
                <p>@_localizer["IndexDeployTxt"] </p>
            </div>
        </div>
        <div class="col-sm-2 empty">
            <div class="link-item" @ref="Summary"
                @onclick=@(() => Navigate("/reports"))>
                <div class="bg-reporting"></div>
                <p>@_localizer["IndexSummaryTxt"] </p>
            </div>
        </div>
        @* <div class="col-sm-2">
            <div class="link-item" @ref=LTAP
                 @onmouseover="@(args => ShowTooltip(LTAP, _localizer["IndexLtapToolTip"]))"
                 @onmouseleave=@(() => HideTooltip(LTAP))>
                 <div class="bg-long-term-asset-plan"></div>
                <p>@_localizer["IndexLtapTxt"]</p>
            </div>
        </div>*@
        <div class="col-sm-2">
            <div class="link-item" @ref="ValueForecast"
                @onclick=@(() => Navigate("/VDMXL-view"))>
                <div class="bg-value-forecast"></div>
                <p>@_localizer["IndexValForecastTxt"]</p>
            </div>
        </div>
        <div class="col-sm-2 empty">
            <div class="link-item" @ref="EamUpload"
                 @onclick=@(() => Navigate("/value-risk-exports"))>
                <div class="bg-eam-upload"></div>
                <p>@_localizer["IndexEamUploadTxt"]</p>
            </div>
        </div>
    </div>
</div>
