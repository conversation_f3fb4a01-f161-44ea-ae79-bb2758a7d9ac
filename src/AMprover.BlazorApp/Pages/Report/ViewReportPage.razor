﻿@page "/reports/{SelectedReportType}"
@using AMprover.BusinessLogic.Enums.Reports

<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <NavLink class="breadcrumb-item" href="/" Match="NavLinkMatch.All">
            Home
        </NavLink>
        <NavLink class="breadcrumb-item" href="/reports" Match="NavLinkMatch.All">
            Reports
        </NavLink>
        <NavLink class="breadcrumb-item" aria-current="page" Match="NavLinkMatch.All">
            @ReportName
        </NavLink>
    </ol>
</nav>

<div class="row">
    <div class="col-md-12">

        <h2>@ReportName</h2>

        <RadzenTabs @bind-SelectedIndex=@TabIndex>
            <Tabs>
                <RadzenTabsItem Text="Filters">

                    @if (SelectedReport != ReportType.CommonActions)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@Localizer["RpFilterLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                    @bind-Value=@SelectedFilter 
                                    Data=@Filters.ToDictionary(x => x, x => x.Name)
                                    Change=@(args => ApplyFilter(args))/>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-1">
                                <label>@Localizer["RpFilterNameLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMproverTextBox @bind-Value=@SelectedFilterName/>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-1">
                                <div class="form-group">
                                    <RadzenButton Text="Save filter" Click=@SaveFilter Disabled=@(Busy || string.IsNullOrWhiteSpace(SelectedFilterName)) />
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="form-group">
                                    <RadzenButton Text="Delete filter" Click=@DeleteFilter Disabled=!FilterSelected />
                                </div>
                            </div>
                        </div>

                        @if (ShowFilterNameError)
                        {
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="alert alert-danger">@Localizer["RpFilterSaveErrorLbl"]</div>
                                </div>
                            </div>
                        }

                        <hr/>
                    }

                    <h3>@(SelectedReport is ReportType.CommonActions ? " " : Localizer["RpFilterTextLbl"])</h3>

                    @if (ScenarioList.Any() && SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis or ReportType.PmoRiskAnalysis
                    or ReportType.TaskPlanExtended or ReportType.PmoPreventiveActions or ReportType.PmoRisk or ReportType.Cluster or ReportType.Spares or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@Localizer["RpScenarioLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                                Data=@ScenarioList.ToDictionary(x => (int?)x.Id, y => y.Name)
                                                @bind-Value=@QueryParams.ScenarioFilter
                                                Change=ChangeSelection />
                            </div>
                        </div>
                    }
                    
                    @if (CollectionList.Count > 0 && SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis
                    or ReportType.PmoRiskAnalysis or ReportType.TaskPlanExtended or ReportType.PmoPreventiveActions or ReportType.PmoRisk or ReportType.Cluster or ReportType.Spares or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@GetObjectLevel(ObjectLevel.Collection):</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@CollectionList.ToDictionary(x => (int?)x.Id, x => x.Name)
                                            @bind-Value=@QueryParams.CollectionFilter
                                            Change=ChangeSelection />
                            </div>
                        </div>
                    }
                    
                    @if (InstallationList.Count > 0 && SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis
                    or ReportType.PmoRiskAnalysis or ReportType.TaskPlanExtended or ReportType.PmoRisk or ReportType.PmoPreventiveActions or ReportType.Cluster or ReportType.Spares or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@GetObjectLevel(ObjectLevel.Installation):</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@InstallationList.ToDictionary(x => (int?)x.Id, x => x.Name)
                                            @bind-Value=@QueryParams.InstallationFilter
                                            Change=ChangeSelection />
                            </div>
                        </div>
                    }
                    
                    @if (RiskObjectList.Any() && SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis or ReportType.Cluster or ReportType.PmoRiskAnalysis or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@Localizer["RpRiskObjectLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@RiskObjectList.ToDictionary(x => (int?)x.Id, x => x.Name)
                                            @bind-Value=@QueryParams.RiskObjectFilter
                                            Change=ChangeSelection />
                            </div>
                        </div>
                    }
                    
                    @if (SystemList.Count > 0 && SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis
                    or ReportType.PmoRiskAnalysis or ReportType.TaskPlanExtended or ReportType.PmoRisk or ReportType.PmoPreventiveActions or ReportType.Spares)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>@GetObjectLevel(ObjectLevel.System):</label>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@SystemList.ToDictionary(x => (int?)x.Id, x => x.Name) 
                                            @bind-Value=@QueryParams.SystemFilter
                                            Change=ClearReport />
                            </div>
                        </div>
                    }
                    
                    @if (SiCategories.Any() && SelectedReport is ReportType.SignificantItems)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@Localizer["RpSiCategoriesLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@SiCategories.ToNullableDictionary()
                                            @bind-Value=@QueryParams.SiCategoryFilter 
                                            Change=ClearReport />
                            </div>
                        </div>
                    }

                    @if (SelectedReport is ReportType.Cluster or ReportType.RETTaskExport)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <label>@Localizer["RpStatusLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown AllowClear="true" AllowFiltering="true"
                                            Data=@StatusList.ToNullableDictionary()
                                            @bind-Value=@QueryParams.StatusFilter />
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-1">
                                <label>@Localizer["RpClusterNameLbl"]:</label>
                            </div>
                            <div class="col-md-2">
                                <AMproverTextBox AllowClear="true" AllowFiltering="true" @bind-Value=@QueryParams.ClusterNameFilter />
                            </div>
                        </div>
                    }

                    @if (SiCategories.Any() && SelectedReport is ReportType.RiskAnalysis or ReportType.SapaAnalysis or ReportType.PmoRiskAnalysis or ReportType.Cluster or ReportType.FunctionalTree)
                    {
                        <div class="row">
                            <div class="col-md-1">
                                <div class="form-group">
                                    <RadzenButton Text="Generate" Click=@LoadReport BusyText="Generating..." IsBusy=Busy />
                                </div>
                            </div>
                        </div>
                    }

                    @if (ScenarioList.Any() && 
                         SelectedReport is ReportType.TaskPlanExtended or 
                             ReportType.CommonActions or 
                             ReportType.Cluster or 
                             ReportType.PmoRisk or 
                             ReportType.PmoPreventiveActions or 
                             ReportType.Spares or
                             ReportType.RETTaskExport)
                    {
                        <hr/>

                        <div class="row">
                            <div class="col-md-1">
                                <div class="form-group">
                                    <label>FileType:</label>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <AMDropdown Data=@RenderTypes
                                            @bind-Value=@SelectedRenderType/>
                            </div>
                        </div>
                    
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <RadzenButton Text="Download" Click=@DownloadReport ButtonStyle=ButtonStyle.Secondary Disabled=Busy BusyText="Downloading..." IsBusy=Busy />
                                </div>
                            </div>
                        </div>
                    }

                </RadzenTabsItem>

                <RadzenTabsItem Text="Result" Disabled=@string.IsNullOrWhiteSpace(Report)>
                    <iframe style="max-width:100%;max-height:75vh" src=@Report></iframe>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
</div>