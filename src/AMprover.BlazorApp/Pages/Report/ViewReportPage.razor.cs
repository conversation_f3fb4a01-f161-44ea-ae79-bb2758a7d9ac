using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums.Reports;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AMprover.BlazorApp.Helpers;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.Reports;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Task = System.Threading.Tasks.Task;
using Microsoft.AspNetCore.Hosting;
using BlazorDownloadFile;
using Microsoft.Extensions.Logging;

namespace AMprover.BlazorApp.Pages.Report;

public partial class ViewReportPage
{
    [Parameter]
    public string SelectedReportType { get; set; }

    [Inject] private IStringLocalizer<ReportPage> Localizer { get; set; }
    [Inject] private IJSRuntime JSRuntime { get; set; }
    [Inject] private IReportManager ReportManager { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; }
    [Inject] private IWebHostEnvironment WebHostEnvironment { get; set; }
    [Inject]private IDropdownManager DropDownManager { get; set; }
    [Inject]private IReportFilterManager ReportFilterManager { get; set; }
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private IBlazorDownloadFileService DownloadFileService { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private ILogger<ViewReportPage> Logger { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }
    [Inject] private ReportViewQueryParams QueryParams { get; set; }

    private ReportType SelectedReport { get; set; }
    private string ReportName { get; set; }
    private int TabIndex { get; set; }
    private ReportFilterModel SelectedFilter { get; set; }
    private string SelectedFilterName { get; set; }

    private List<ObjectModel> Objects { get; set; } = [];

    private List<ReportFilterModel> Filters { get;  set; } = [];

    private Dictionary<int, string> SiCategories { get;  set; } = new();

    private List<ScenarioModel> ScenarioList { get; set; }

    private Dictionary<int, string> ClusterList { get; set; } = new();

    private Dictionary<int, string> StatusList { get; set; } = new();

    private List<RiskObjectModel> RiskObjectList { get; set; }

    private List<ObjectModel> CollectionList { get; set; }

    private List<ObjectModel> InstallationList { get; set; }

    private List<ObjectModel> SystemList { get; set; }
    protected Dictionary<ObjectLevel, string> ObjectLevels { get; set; } = new();
    private Dictionary<string, string> RenderTypes { get; set; }
    private string Report { get; set; }
    public bool Busy { get; private set; }
    private bool ShowFilterNameError { get; set; }
    private string SelectedRenderType { get; set; } = "Excel";
    private bool FilterSelected => SelectedFilter != null;
    private string Currency => LookupManager.GetCurrency();

    protected override void OnInitialized()
    {
        ScenarioList = ReportFilterManager.GetScenariosForReports();
            ClusterList = DropDownManager.GetClusterDict();
            RiskObjectList = ScenarioList.SelectMany(x => x.RiskObject).ToList();
            CollectionList = GetCollections(RiskObjectList);
            InstallationList = GetInstallations(RiskObjectList);
            StatusList = DropDownManager.GetStatusDict();

            Objects = RiskAnalysisManager.GetAllObjects();
            SiCategories = DropDownManager.GetSiCategoriesDict();
            ObjectLevels = ObjectManager.GetObjectLevelNames();

            SystemList = Objects.Where(x => x.Level == 2).ToList();

            if (SelectedReportType == ReportType.RETTaskExport.ToString())
                RenderTypes = EnumHelper.GetEnumSelectList<RenderType>().Where(x => x.Value == "Xml")
                    .ToDictionary(x => x.Value, x => x.Value);
            else
                RenderTypes = EnumHelper.GetEnumSelectList<RenderType>().ToDictionary(x => x.Value, x => x.Value);

            if (!Enum.TryParse<ReportType>(SelectedReportType, out var reportType)) return;

            SelectedReport = reportType;
            ReportName = SelectedReport.GetReportName(false);

            Filters = ReportFilterManager.GetFiltersByReport(SelectedReport.ToString());

            TabIndex = 0;
        }

    #region Report methods

    private async Task LoadReport()
    {
            Busy = true;
            var report = await GetReportAsBase64(SelectedReport == ReportType.RETTaskExport ? RenderType.Xml : RenderType.Pdf).ConfigureAwait(false);
            Report = await JSRuntime.InvokeAsync<string>("CreateUrl", report).ConfigureAwait(false);
            TabIndex = 1;
            Busy = false;
        }

    public void ChangeSelection()
    {
            ChangeSelectionInternal();
            ClearReport();
            ChangeSelectionInternal();
        }

    private void ChangeSelectionInternal()
    {
            RiskObjectList = GetScenarios().SelectMany(x => x.RiskObject).ToList();
            CollectionList = GetCollections(RiskObjectList);

            if (QueryParams.CollectionFilter != null)
                RiskObjectList = RiskObjectList.Where(x => x.ParentObjectId == QueryParams.CollectionFilter).ToList();

            InstallationList = GetInstallations(RiskObjectList);

            if (QueryParams.InstallationFilter != null)
                RiskObjectList = RiskObjectList.Where(x => x.ObjectId == QueryParams.InstallationFilter).ToList();
            SystemList = QueryParams.RiskObjectFilter != null
                ? ObjectManager.GetAllSystemsFromRiskObject(QueryParams.RiskObjectFilter.Value)
                : Objects.Where(x => x.Level == 2).ToList();
        }

    private IEnumerable<ScenarioModel> GetScenarios()
    {
            if (QueryParams.ScenarioFilter == null)
                return ScenarioList;

            var selectedScenario = ScenarioList.FirstOrDefault(x => x.Id == QueryParams.ScenarioFilter);

            return selectedScenario != null
                ? [selectedScenario]
                : ScenarioList;
        }

    private static List<ObjectModel> GetCollections(IEnumerable<RiskObjectModel> riskObjects)
    {
            return riskObjects.Where(x => x.ParentObject != null)
                .Select(x => x.ParentObject)
                .DistinctBy(x => x.Id).ToList();
        }

    private static List<ObjectModel> GetInstallations(IEnumerable<RiskObjectModel> riskObjects)
    {
            return riskObjects.Where(x => x.ObjectId != 0)
                .Select(x => x.Object)
                .DistinctBy(x => x.Id).ToList();
        }

    public void ClearReport()
    {
            if (QueryParams.CollectionFilter != null)
            {
                var collection = CollectionList.FirstOrDefault(x => x.Id == QueryParams.CollectionFilter);
                QueryParams.CollectionFilter = collection?.Id;
            }

            if (QueryParams.InstallationFilter != null)
            {
                var installation = InstallationList.FirstOrDefault(x => x.Id == QueryParams.InstallationFilter);
                QueryParams.InstallationFilter = installation?.Id;
            }

            if (QueryParams.RiskObjectFilter != null)
            {
                var riskObject = RiskObjectList.FirstOrDefault(x => x.Id == QueryParams.RiskObjectFilter);
                QueryParams.RiskObjectFilter = riskObject?.Id;
            }

            Report = string.Empty;
        }

    private async Task DownloadReport()
    {
            Busy = true;
            try
            {
                if (SelectedReportType == ReportType.RETTaskExport.ToString())
                    SelectedRenderType = RenderType.Xml.ToString();

                if (Enum.TryParse(SelectedRenderType, out RenderType selectedRenderType))
                    await DownloadFileService.DownloadFile(
                        SelectedReport.GetReportName(true),
                        await GetReportAsBase64(selectedRenderType),
                        selectedRenderType.ToContentType());
            }
            catch (Exception e)
            {
                Logger.LogError(e, "Cannot download report");
            }

            Busy = false;
        }

    private async Task<string> GetReportAsBase64(RenderType renderType)
    {
            return Convert.ToBase64String(
                await ReportManager.GetReport(WebHostEnvironment.WebRootPath, SelectedReport, renderType, Currency,
                    QueryParams.CollectionFilter, QueryParams.InstallationFilter, QueryParams.SystemFilter, null, null,
                    QueryParams.ScenarioFilter, QueryParams.RiskObjectFilter, QueryParams.SiCategoryFilter,
                    GetObjectLevel(ObjectLevel.Collection),
                    GetObjectLevel(ObjectLevel.Installation),
                    GetObjectLevel(ObjectLevel.System),
                    GetObjectLevel(ObjectLevel.Component),
                    GetObjectLevel(ObjectLevel.Assembly),
                    QueryParams.StatusFilter,
                    QueryParams.ClusterNameFilter)
            );
        }

    #endregion

    #region Filter methods

    private void ApplyFilter(object selectedFilter)
    {
            if (selectedFilter == null) return;
            var filter = (ReportFilterModel)selectedFilter;

            SelectedFilter = filter;
            SelectedFilterName = filter.Name;

            var selectedFilters = filter.SelectedIds.Split(";");

            QueryParams.ScenarioFilter = GetValue(selectedFilters, 0);
            QueryParams.CollectionFilter = GetValue(selectedFilters, 1);
            QueryParams.InstallationFilter = GetValue(selectedFilters, 2);
            QueryParams.SystemFilter = GetValue(selectedFilters, 3);
            QueryParams.SiCategoryFilter = GetValue(selectedFilters, 4);
            QueryParams.StatusFilter = GetValue(selectedFilters, 5);

            QueryParams.ClusterNameFilter = selectedFilters.Skip(6).FirstOrDefault();
        }

    private static int? GetValue(IEnumerable<string> selectedIds, int index)
    {
            return int.TryParse(selectedIds.Skip(index).FirstOrDefault(), out var result)
                ? result
                : null;
        }

    private void SaveFilter()
    {
            if (Filters.Any(x => x.Name == SelectedFilterName && x.SelectedIds == QueryParams.ToFilterString()))
            {
                ShowFilterNameError = true;
                return;
            }

            ShowFilterNameError = false;

            SelectedFilter ??= new ReportFilterModel
            {
                ReportName = SelectedReport.ToString()
            };

            SelectedFilter.Name = SelectedFilterName;
            SelectedFilter.SelectedIds = QueryParams.ToFilterString();

            var result = ReportFilterManager.SaveFilterForReport(SelectedFilter);
            Filters.Add(result);
        }

    private void DeleteFilter()
    {
            ReportFilterManager.DeleteFilterForReport(SelectedFilter);
            Filters.Remove(SelectedFilter);
            SelectedFilter = null;
            SelectedFilterName = string.Empty;
        }

    #endregion

    private string GetObjectLevel(ObjectLevel objectLevel)
    {
            if (ObjectLevels.TryGetValue(objectLevel, out var level))
                return level;

            Logger.LogError($"{nameof(GetObjectLevel)} was called but {nameof(ObjectLevels)} has not been filled yet");
            return objectLevel.ToString();
        }
}