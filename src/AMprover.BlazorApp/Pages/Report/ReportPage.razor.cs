using System.Collections.Generic;
using System.Linq;
using AMprover.BlazorApp.Configuration;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.Data.Infrastructure;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Pages.Report;

public partial class ReportPage
{
    [Inject] private IStringLocalizer<ReportPage> Localizer { get; set; } = default!;
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; } = default!;
    [Inject] private IDropdownManager DropdownManager { get; set; } = default!;
    [Inject] private IObjectManager ObjectManager { get; set; } = default!;
    [Inject] private IAssetManagementPortfolioResolver AssetManagementPortfolioResolver { get; set; } = default!;
    [Inject] private IConfiguration Configuration { get; set; } = default!;

    protected Dictionary<ObjectLevel, string> ObjectLevels { get; set; } = new();

    public List<ObjectModel> Objects { get; set; } = [];

    public Dictionary<int, string> Scenarios { get; set; } = new();

    public Dictionary<int, string> SiCategories { get; set; } = new();

    public ReportViewQueryParams ReportViewQueryParams { get; set; }
    private bool ShowRETTaskExport { get; set; }

    protected override void OnInitialized()
    {
        Objects = RiskAnalysisManager.GetAllObjects();
        Scenarios = DropdownManager.GetScenarioDict();
        SiCategories = DropdownManager.GetSiCategoriesDict();
        ObjectLevels = ObjectManager.GetObjectLevelNames();

        var reports = GetReports();

        if (reports.Any() && reports.Any(x => x.Report == "RETTaskExport"))
            ShowRETTaskExport = true;
    }

    private List<ReportSetting> GetReports()
    {
        var currentPortfolio = AssetManagementPortfolioResolver.GetCurrentPortfolio();

        return Configuration.GetSection("Reports").Get<List<ReportSetting>>()
            .Where(x => x.Database == currentPortfolio.Name)
            .ToList();
    }
}