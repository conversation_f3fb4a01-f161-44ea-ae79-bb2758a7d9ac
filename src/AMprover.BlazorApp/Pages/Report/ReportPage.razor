﻿@page "/reports"
@using AMprover.BusinessLogic.Enums.Reports

<div class="row header-navigation">
    <div class="col 6">
        <Breadcrumbs Category="Reports" />
    </div>
    <div class="col-6 text-right">
        <Information class="float-right my-2 mx-2" DialogTitle=@Localizer["RpHeaderTxt"] DialogContent=@Localizer["RpMenuTxt"] />
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <h2>@Localizer["RpHeaderTxt"]</h2>
    </div>
</div>

<div class="row card-row">
    <div class="col-sm-2"><h5>@Localizer["RpDataPreparationTxt"]</h5></div>
    <ReportCard CardTitle=@Localizer["RpCommonActionsRep"] CardText=@Localizer["RpCommonActionsRepTxt"]
                Category="Masterdata" ReportType=@ReportType.CommonActions Icon="fas fa-file-alt" />
</div>
<div class="row card-row">
    <div class="col-sm-2"><h5>@Localizer["RpAssessmentTxt"]</h5></div>
    <ReportCard CardTitle=@Localizer["RpValueRiskRep"] CardText=@Localizer["RpValueRiskRepTxt"]
                QueryParams=@ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.RiskAnalysis Icon="fas fa-puzzle-piece" />

    <ReportCard CardTitle=@Localizer["RpSapaRep"] CardText=@Localizer["RpSapaRepTxt"]
                QueryParams=@ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.SapaAnalysis Icon="fas fa-puzzle-piece" />

    <ReportCard CardTitle=@Localizer["RpPmoValueRiskRep"] CardText=@Localizer["RpPmoValueRiskRepTxt"]
                QueryParams=@ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.PmoRiskAnalysis Icon="fas fa-puzzle-piece" />
</div>
<div class="row card-row">
    <div class="col-sm-2"></div>
    <ReportCard CardTitle=@Localizer["RpClusterRep"] CardText=@Localizer["RpClusterRepTxt"]
                QueryParams=@ReportViewQueryParams
                Category="Cluster" ReportType=@ReportType.Cluster Icon="fas fa-tasks" />

    @if (ShowRETTaskExport)
    {
        <ReportCard CardTitle=@Localizer["RpRETTaskExportRep"] CardText=@Localizer["RpRETTaskExportRepTxt"]
                    QueryParams=@ReportViewQueryParams
                    Category="Cluster" ReportType=@ReportType.RETTaskExport Icon="fas fa-th-list" />
    }
</div>
<div class="row card-row">
    <div class="col-sm-2"><h5>@Localizer["RpTabularTxt"]</h5></div>
    <ReportCard CardTitle=@Localizer["RpValuePmoRiskRep"] CardText=@Localizer["RpValuePmoRiskRepTxt"]
                QueryParams=@ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.PmoRisk Icon="fas fa-th-list" />

    <ReportCard CardTitle=@Localizer["RpValuePmoPrevActRep"] CardText=@Localizer["RpValuePmoPrevActRepTxt"]
                QueryParams=@ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.PmoPreventiveActions Icon="fas fa-th-list" />

    <ReportCard CardTitle=@Localizer["RpSparesRep"] CardText=@Localizer["RpSparesRepTxt"]
                QueryParams=@ReportViewQueryParams
                Category="Value Risk analysis" ReportType=@ReportType.Spares Icon="fas fa-th-list" />
</div>
<div class="row card-row">
    <div class="col-sm-2"></div>
    <ReportCard CardTitle=@Localizer["RpExtTaskPlanRep"] CardText=@Localizer["RpExtTaskPlanRepTxt"]
                QueryParams=@ReportViewQueryParams
                Category="Cluster" ReportType=@ReportType.TaskPlanExtended Icon="fas fa-box" />

</div>
