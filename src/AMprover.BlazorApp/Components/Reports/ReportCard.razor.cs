using AMprover.BlazorApp.Pages.Report;
using AMprover.BusinessLogic.Enums.Reports;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Components.Reports;

public partial class ReportCard
{
    [Inject] private IStringLocalizer<ReportCard> Localizer { get; set; }
    [Inject] private NavigationManager NavigationManager { get; set; }

    [Parameter]
    public string CardTitle { get; set; }

    [Parameter]
    public string CardText { get; set; }

    [Parameter]
    public string Category { get; set; }

    [Parameter]
    public string Icon { get; set; }

    [Parameter]
    public ReportViewQueryParams QueryParams { get; set; }

    [Parameter]
    public ReportType ReportType { get; set; }

    private void ViewReport(ReportType reportType)
    {
        var url = $"/reports/{reportType}";

        if (QueryParams?.CollectionFilter.HasValue == true)
            url = AddQueryString(url, "CollectionFilter", QueryParams.CollectionFilter.ToString());

        if (QueryParams?.InstallationFilter.HasValue == true)
            url = AddQueryString(url, "InstallationFilter", QueryParams.InstallationFilter.ToString());

        if (QueryParams?.SystemFilter.HasValue == true)
            url = AddQueryString(url, "SystemFilter", QueryParams.SystemFilter.ToString());

        if (QueryParams?.ScenarioFilter.HasValue == true)
            url = AddQueryString(url, "ScenarioFilter", QueryParams.ScenarioFilter.ToString());

        if (QueryParams?.SiCategoryFilter.HasValue == true)
            url = AddQueryString(url, "SiCategoryFilter", QueryParams.SiCategoryFilter.ToString());

        if (QueryParams?.RiskObjectFilter.HasValue == true)
            url = AddQueryString(url, "RiskObjectFilter", QueryParams.RiskObjectFilter.ToString());

        if (QueryParams?.StatusFilter.HasValue == true)
            url = AddQueryString(url, "StatusFilter", QueryParams.StatusFilter.ToString());

        if (!string.IsNullOrEmpty(QueryParams?.ClusterNameFilter))
            url = AddQueryString(url, "ClusterNameFilter", QueryParams.ClusterNameFilter);

        NavigationManager.NavigateTo(url);
    }

    private string AddQueryString(string url, string key, string value)
    {
        return url.Contains("?")
            ? $"{url}&{key}={value}"
            : $"{url}?{key}={value}";
    }
}