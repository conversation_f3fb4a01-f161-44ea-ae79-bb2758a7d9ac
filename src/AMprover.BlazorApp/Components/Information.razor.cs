﻿using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;

namespace AMprover.BlazorApp.Components;

public partial class Information
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private TooltipService TooltipService { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private ILogger<Information> Logger { get; set; }

    [Parameter] public string DialogTitle { get; set; }
    [Parameter] public string DialogContent { get; set; }
    [Parameter] public string Class { get; set; }

    public void OpenInformationDialog()
    {
        DialogService.Open<InformationDialog>(DialogTitle, 
            new Dictionary<string, object> { { "DialogContent", DialogContent } },
            new DialogOptions { Width = "800px", Resizable = false, Draggable = true });
    }
}