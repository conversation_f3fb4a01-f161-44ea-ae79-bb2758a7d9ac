﻿using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using <PERSON><PERSON>zen;

namespace AMprover.BlazorApp.Components;

public partial class InformationDialog
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private TooltipService TooltipService { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private ILogger<InformationDialog> Logger { get; set; }

    [Parameter] public string DialogTitle { get; set; }
    [Parameter] public string DialogContent { get; set; }
}