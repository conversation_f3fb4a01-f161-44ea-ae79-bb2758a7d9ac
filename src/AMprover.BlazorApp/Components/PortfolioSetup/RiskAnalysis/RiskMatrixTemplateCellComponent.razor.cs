using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Logging;
using Radzen;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Components.PortfolioSetup.RiskAnalysis;

public partial class RiskMatrixTemplateCellComponent
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IStringLocalizer<RiskMatrixTemplateCellComponent> Localizer { get; set; }
    [Inject] private IGlobalDataService GlobalDataService { get; set; }

    [Parameter] public int MatrixId { get; set; }
    [Parameter] public int Row { get; set; }
    [Parameter] public int Column { get; set; }
    [Parameter] public Action CallBack { get; set; }

    private List<LookupSettingModel> LookupSettings { get; set; }
    private LookupSettingModel EnableColumnsForTypes { get; set; }

    private bool IsEffectColumn { get; set; }
    private bool SetTypesToColumn { get; set; }

    private RiskMatrixTemplateModel RiskMatrixTemplate { get; set; }
    private RiskMatrixTemplateCell SelectedCell { get; set; }
    private RiskMatrixTemplateColumn SelectedColumn { get; set; }

    public Dictionary<int, string> CorrectiveCostOptions { get; set; }
        = new()
        {
            {1, "Technical cost"},
            {2, "Circuit affected cost"},
            {3, "Normal"}
        };

    public Dictionary<int, string> ColumnType { get; set; }
        = new()
        {
            {1, "Percentage - Sub Column Max."}, //Only Main Column
            {2, "Percentage - Sub Column Prod."}, //Only Main Column
            {3, "Labda (%) - Sub Column Max."}, //Only Main Column
            //{4, "SHE Column"}, // NB. Only available for sub columns
            {5, "CBI (%) - Sub Column Prod."} //Only Main Column
        };

    protected override void OnInitialized()
    {
        RiskMatrixTemplate = RiskAnalysisSetupManager.GetTemplate(MatrixId);
        SelectedCell = RiskMatrixTemplate.MainGrid.TableContent[Row].Cells[Column];

        // Decimals are not allowed in the matrices. 1 Exception is the MTBF
        // allow setting to decimals only for header rows to enable this
        if (Row == 0)
            SelectedCell.AllowDecimals = true;

        var column = RiskMatrixTemplate.MainGrid.TableColumns[Column];
        IsEffectColumn = column.IsEffectColumn;

        //To Enable the SAPA module it's necessary to use percentages in the Matrix. Herfore the boolean is used
        LookupSettings = LookupManager.GetLookupSettings();
        EnableColumnsForTypes = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.EnableColumnsForTypes, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.EnableColumnsForTypes };
        SetTypesToColumn = EnableColumnsForTypes.IntValue == 1;
        SelectedColumn = column;
    }

    private void ValidFormSubmitted(EditContext editContext)
    {
        var model = (RiskMatrixTemplateModel)editContext.Model;

        //Remove subgrids
        if (!SelectedColumn.HasSubColumns)
        {
            model.SubGrids.RemoveAll(x => x.ColumnId == Column);
        }

        RiskAnalysisSetupManager.UpdateTemplate(model);
        CallBack?.Invoke();
        DialogService.Close();
    }

    private static void InvalidFormSubmitted(EditContext editContext)
    {
        //Throw error message.
    }
}