using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Logging;
using Radzen;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Components.PortfolioSetup.RiskAnalysis
{
    public partial class RiskMatrixTemplateSubCellComponent
    {
        [Inject] private ILoggerFactory LoggerFactory { get; set; }
        [Inject] private DialogService DialogService { get; set; }
        [Inject] private IRiskAnalysisSetupManager RiskAnalysisSetupManager { get; set; }
        [Inject] private ILookupManager LookupManager { get; set; }
        [Inject] private IStringLocalizer<RiskMatrixTemplateSubCellComponent> Localizer { get; set; }
        [Inject] private IGlobalDataService GlobalDataService { get; set; }

        [Parameter] public int MatrixId { get; set; }
        [Parameter] public int Row { get; set; }
        [Parameter] public int Column { get; set; }
        [Parameter] public int SubRow { get; set; }
        [Parameter] public int SubColumn { get; set; }
        [Parameter] public Action CallBack { get; set; }

        public List<LookupSettingModel> LookupSettings { get; set; }
        public LookupSettingModel EnableColumnsForTypes { get; set; }

        public bool IsEffectColumn { get; set; }
        public bool IsPercentage { get; set; }
        public bool SetTypesToColumn { get; set; }

        public RiskMatrixTemplateModel RiskMatrixTemplate { get; private set; }
        public RiskMatrixTemplateGrid RiskMatrixSubGrid { get; private set; }
        public RiskMatrixTemplateCell SelectedCell { get; private set; }
        public RiskMatrixTemplateColumn SelectedColumn { get; private set; }

        public Dictionary<int, string> CorrectiveCostOptions { get; set; }
            = new()
            {
                {1, "Technical cost"},
                {2, "Circuit affected cost"},
                {3, "Normal"}
            };

        public Dictionary<int, string> ColumnType { get; set; }
            = new()
            {
                {4, "SHE Column"}
            };

        protected override void OnInitialized()
        {
            RiskMatrixTemplate = RiskAnalysisSetupManager.GetTemplate(MatrixId);
            RiskMatrixSubGrid = RiskMatrixTemplate.SubGrids.FirstOrDefault(x => x.ColumnId == Column);

            if (RiskMatrixSubGrid == null)
                DialogService.Close();
            else
            {
                SelectedCell = RiskMatrixSubGrid.TableContent[SubRow].Cells[SubColumn];
                var column = RiskMatrixSubGrid.TableColumns[SubColumn];
                IsEffectColumn = true;//Subcolumns are always effect columns.

                var mainColumn = RiskMatrixTemplate.MainGrid.TableColumns[Column];
                IsPercentage = mainColumn.IsPercentage;
                LookupSettings = LookupManager.GetLookupSettings();
                EnableColumnsForTypes = LookupSettings.FirstOrDefault(x => x.Property.Equals(PropertyNames.EnableColumnsForTypes, StringComparison.OrdinalIgnoreCase)) ?? new LookupSettingModel { Property = PropertyNames.EnableColumnsForTypes };
                SetTypesToColumn = EnableColumnsForTypes.IntValue == 1;

                if (Row == 0)
                {
                    SelectedColumn = column;
                }
            }
        }

        public void ValidFormSubmitted(EditContext editContext)
        {
            RiskAnalysisSetupManager.UpdateTemplate((RiskMatrixTemplateModel)editContext.Model);
            CallBack?.Invoke();
            DialogService.Close();
        }

        public void InvalidFormSubmitted(EditContext editContext)
        {
            //Throw error message.
        }
    }
}