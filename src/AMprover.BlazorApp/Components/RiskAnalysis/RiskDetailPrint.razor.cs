﻿using AMprover.BlazorApp.Components.GridTypes;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AutoMapper;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Radzen;
using System.Collections.Generic;
using System.Globalization;

namespace AMprover.BlazorApp.Components.RiskAnalysis;

public partial class RiskDetailPrint
{
    [Inject] private ILoggerFactory LoggerFactory { get; set; }
    [Inject] private ISapaOverviewManager SapaOverviewManager { get; set; }
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; }
    [Inject] private IMapper Mapper { get; set; }
    [Inject] private LocalizationHelper LocalizationHelper { get; set; }
    [Inject] private IStringLocalizer<RiskDetailPrint> Localizer { get; set; }
    [Inject] private ILogger<RiskDetailPrint> Logger { get; set; }

    [Parameter] public int RiskId { get; set; }
    [Parameter] public EventCallback<RiskModel> Callback { get; set; }

    private RiskModel Risk { get; set; }
    public UtilityGrid<TaskModel> PreventiveActionsGrid { get; set; }
    public UtilityGrid<SpareModel> SparesGrid { get; set; }
    private Dictionary<string, Dictionary<int, string>> TaskModelDropDownOverrides { get; set; }
        
    private string Currency => LookupManager.GetCurrency();

    protected override void OnInitialized()
    {
        Risk = RiskAnalysisManager.GetRisk(RiskId);
        TaskModelDropDownOverrides = DropdownManager.GetTaskModelDropDownOverrides();
        base.OnInitialized();
    }

    private string FormatAsSelectedCurrency(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("C0", CultureInfo.CreateSpecificCulture(Currency));
    }

    private string FormatAsNumber(decimal? value)
    {
        return value == null ? string.Empty : value.Value.ToString("N0", CultureInfo.CreateSpecificCulture(Currency));
    }

    private static void UpdateCurrentRisk()
    {
        //read only, no updates
    }
}