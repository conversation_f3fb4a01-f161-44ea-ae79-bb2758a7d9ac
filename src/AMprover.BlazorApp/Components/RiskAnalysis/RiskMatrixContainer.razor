@{
    double.TryParse(Risk?.Template.Data.FmecaMainGrid.EffectColumns, out var effectColumns);
}

<div class="row px-4">
    <div class="row px-3">
        <table class="table table-bordered" style="table-layout: fixed;">
            <tbody>
                @for (var index = 0; index < Risk?.Template?.MainGrid.TableContent.Count; index++)
                {
                    var tempIndex = index;
                    var row = Risk.Template.MainGrid.TableContent[index];
                    var tab = MatrixTypeToGridType().ToString();

                    <tr @key="row">
                        @for (var cellIndex = 0; cellIndex < row.Cells.Count; cellIndex++)
                        {
                            var tempCellIndex = cellIndex;
                            var cell = row.Cells[tempCellIndex];
                            var selectedColumn = GetColumn(tempCellIndex);

                            if (tempCellIndex < effectColumns)
                            {
                                if (tempIndex > 0)
                                {
                                    if (selectedColumn.HasSubColumns)
                                    {
                                        <td class="risk-matrix-cell" @key="cell"
                                            id=@cell.UniqueId(tab)
                                            @ref=@cell.CellReference
                                            @onmouseover="@(() => ShowTooltipRiskMatrix(cell.CellReference, cell.Description))"
                                            @onmouseleave="@(() => HideTooltip(cell.CellReference))"
                                            style="@cell.CellStyling(tempIndex, tempCellIndex, Risk, tab, Language, IsSapaView, IsRiskAnalysis); position: relative;">

                                            @cell.GetValue(tempIndex, tempCellIndex, Risk, tab, DisplayVersion, Currency)
                                        </td>
                                    }
                                    else
                                    {
                                        <td class="risk-matrix-cell" @key="cell"
                                            @onclick="() => ProcessEffectChanges(tempCellIndex, tempIndex)"
                                            id=@cell.UniqueId(tab)
                                            @ref=@cell.CellReference
                                            @onmouseover="@(() => ShowTooltipRiskMatrix(cell.CellReference, cell.Description))"
                                            @onmouseleave="@(() => HideTooltip(cell.CellReference))"
                                            style="cursor:pointer;@cell.CellStyling(tempIndex, tempCellIndex, Risk, tab, Language, IsSapaView, IsRiskAnalysis); position: relative;">

                                            @cell.GetValue(tempIndex, tempCellIndex, Risk, tab, DisplayVersion, Currency)
                                        </td>
                                    }
                                }
                                else
                                {
                                    <td class="risk-matrix-cell" @key="cell" id="@cell.UniqueId(tab)" style="@cell.CellStyling(tempIndex, tempCellIndex, Risk, tab, Language, IsSapaView, IsRiskAnalysis); position: relative;">
                                        @if (selectedColumn.HasSubColumns)
                                        {
                                            if (ActivatedSubgrids.Contains(tempCellIndex))
                                            {
                                                <a style="position:absolute;left:10px;bottom: 1px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => RemoveActiveSubgrid(tempCellIndex))">
                                                    <i class="fa-solid fa-arrow-circle-left"></i>
                                                </a>
                                            }
                                            else
                                            {
                                                <a style="position:absolute;left:10px;bottom: 1px;cursor:pointer;z-index:100" class="icon" @onclick="@(_ => AddActiveSubgrid(tempCellIndex))">
                                                    <i class="fa-solid fa-arrow-circle-right"></i>
                                                </a>
                                            }
                                        }
                                        @cell.GetValue(tempIndex, tempCellIndex, Risk, tab, DisplayVersion, Currency)
                                    </td>
                                }
                            }
                            else
                            {
                                <td @key="cell" @onclick="() => ProcessDataChanges(tempCellIndex, tempIndex)" id="@cell.UniqueId(tab)" style="@cell.CellStyling(tempIndex, tempCellIndex, Risk, tab, Language, IsSapaView, IsRiskAnalysis); position: relative">
                                    @cell.GetValue(tempIndex, tempCellIndex, Risk, tab, DisplayVersion, Currency)
                                </td>
                            }

                            @if (selectedColumn.HasSubColumns && ActivatedSubgrids.Contains(tempCellIndex))
                            {
                                var subgrid = Risk.SubDataGrids.FirstOrDefault(x => x.ColumnId == tempCellIndex);
                                if (subgrid == null) continue;

                                var isPercentage = selectedColumn.IsPercentage;

                                for (var subgridColumnIndex = 0; subgridColumnIndex < subgrid.TableColumns.Count; subgridColumnIndex++)
                                {
                                    var tempSubgridColumIndex = subgridColumnIndex;
                                    var templateSubGrid = Risk.Template.SubGrids.FirstOrDefault(x => x.ColumnId == tempCellIndex);

                                    if (tempIndex < 0 || tempIndex >= templateSubGrid.TableContent.Count)
                                        continue;

                                    var subgridRow = templateSubGrid?.TableContent[index];

                                    if (subgridRow == null)
                                        continue;

                                    var noOfSubColumns = templateSubGrid?.TableColumns.Count; //when a sub column is deleted, the index can be too high -> continue (next line)

                                    if (subgridRow == null || tempSubgridColumIndex >= noOfSubColumns)
                                        continue;

                                    var subGridCell = subgridRow.Cells[tempSubgridColumIndex];

                                    if (subGridCell == null)
                                        continue;

                                    if (tempIndex == 0)
                                    {
                                        <td class="risk-matrix-subgrid-cell" id="@subGridCell.UniqueId(tab, true)" style="@cell.CellStyling(tempIndex, tempSubgridColumIndex, subgrid, templateSubGrid, tab, IsSapaView, IsRiskAnalysis);position: relative;">
                                            @cell.GetSubgridValue(tempIndex, tempSubgridColumIndex, templateSubGrid, subgrid, tab, DisplayVersion, Currency)
                                        </td>
                                    }
                                    else
                                    {
                                        <td @onclick="() => ProcessEffectChanges(tempSubgridColumIndex, tempIndex, tempCellIndex, isPercentage)"
                                            class="risk-matrix-subgrid-cell" id="@subGridCell.UniqueId(tab, true)"
                                            style="cursor:pointer;@cell.CellStyling(tempIndex, tempSubgridColumIndex, subgrid, templateSubGrid, tab, IsSapaView, IsRiskAnalysis);position: relative;">
                                            @cell.GetSubgridValue(tempIndex, tempSubgridColumIndex, templateSubGrid, subgrid, tab, DisplayVersion, Currency)
                                        </td>
                                    }
                                }
                            }
                        }
                    </tr>
                }
            </tbody>
            <tfoot>
                @if (DisplayVersion == 1)
                {
                    <tr>
                        @for (var index = 0; index < effectColumns; index++)
                        {
                            if (Risk?.MainDataGrid.TableColumns.Count > index)
                            {
                                var column = Risk.MainDataGrid.TableColumns[index];
                                var columnTemplate = Risk.Template?.MainGrid.TableColumns[index];
                                var numberType = columnTemplate.IsPercentage ? "P2" : "C0";
                                var bindValueBefore = columnTemplate.IsPercentage ? column.ValueBeforeAsDecimal : column.ValueBefore;

                                <td>
                                    @if (columnTemplate.IsPercentage)
                                    {
                                        @switch (MatrixType)
                                        {
                                            case MatrixTypes.Before:
                                                <AMproverNumberInput ReadOnly=IsReadOnly ForceInvariantCulture=columnTemplate.IsPercentage HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@column.ValueBefore Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                                break;
                                            case MatrixTypes.After:
                                                <AMproverNumberInput ReadOnly=IsReadOnly ForceInvariantCulture=columnTemplate.IsPercentage HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@column.ValueAfter Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                                break;
                                            case MatrixTypes.Pmo:
                                                <AMproverNumberInput ReadOnly=IsReadOnly ForceInvariantCulture=columnTemplate.IsPercentage HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@column.ValuePmo Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                                break;
                                        }
                                    }
                                    else
                                    {
                                        @switch (MatrixType)
                                        {
                                            case MatrixTypes.Before:
                                                <AMproverNumberInput HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@column.ValueBefore Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                                break;
                                            case MatrixTypes.After:
                                                <AMproverNumberInput HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@column.ValueAfter Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                                break;
                                            case MatrixTypes.Pmo:
                                                <AMproverNumberInput HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@column.ValuePmo Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                                break;
                                        }

                                    }
                                </td>

                                @if (columnTemplate?.HasSubColumns == true && ActivatedSubgrids.Contains(index))
                                {
                                    var subGrid = Risk.SubDataGrids.FirstOrDefault(x => x.ColumnId == index);

                                    var templateSubGrid = Risk.Template.SubGrids.FirstOrDefault(x => x.ColumnId == index);
                                    var noOfSubColumns = templateSubGrid?.TableColumns.Count; //when a sub column is deleted, the index can be too high -> continue (next line)

                                    for (var subIndex = 0; subIndex < noOfSubColumns; subIndex++)
                                    {
                                        var subColumn = subGrid.TableColumns[subIndex];

                                        <td>
                                            @if (IsSapaView && columnTemplate.IsPercentage)
                                            {
                                                @switch (MatrixType)
                                                {
                                                    case MatrixTypes.Before:
                                                        <AMproverNumberInput ReadOnly=IsReadOnly ForceInvariantCulture="true" HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@subColumn.ValueBeforeAsDecimal OnBlur=@UpdateRiskWithCustomValues />
                                                        break;
                                                    case MatrixTypes.After:
                                                        <AMproverNumberInput ReadOnly=IsReadOnly ForceInvariantCulture="true" HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@subColumn.ValueAfterAsDecimal OnBlur=@UpdateRiskWithCustomValues />
                                                        break;
                                                    case MatrixTypes.Pmo:
                                                        <AMproverNumberInput ReadOnly=IsReadOnly ForceInvariantCulture="true" HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@subColumn.ValuePmoAsDecimal OnBlur=@UpdateRiskWithCustomValues />
                                                        break;
                                                }
                                            }
                                            else
                                            {
                                                @switch (MatrixType)
                                                {
                                                    case MatrixTypes.Before:
                                                        <AMproverNumberInput ReadOnly=IsReadOnly HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@subColumn.ValueBefore OnBlur=@UpdateRiskWithCustomValues />
                                                        break;
                                                    case MatrixTypes.After:
                                                        <AMproverNumberInput ReadOnly=IsReadOnly HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@subColumn.ValueAfter OnBlur=@UpdateRiskWithCustomValues />
                                                        break;
                                                    case MatrixTypes.Pmo:
                                                        <AMproverNumberInput ReadOnly=IsReadOnly HideStyling="@true" UsedInGrid="@true" Format=@numberType @bind-Value=@subColumn.ValuePmo OnBlur=@UpdateRiskWithCustomValues />
                                                        break;
                                                }
                                            }
                                        </td>
                                    }
                                }
                            }
                        }
                    </tr>
                }
                @if (DisplayVersion == 2)
                {
                    <tr>
                        @for (var index = 0; index < effectColumns; index++)
                        {
                            @if (Risk?.MainDataGrid.TableColumns.Count > index)
                            {
                                var column = Risk.MainDataGrid.TableColumns[index];
                                var columnTemplate = Risk.Template?.MainGrid.TableColumns[index];

                                <td>
                                    @switch (MatrixType)
                                    {
                                        case MatrixTypes.Before:
                                            <AMproverNumberInput ReadOnly=IsReadOnly HideStyling="@true" UsedInGrid="@true" Format="F0" @bind-Value=@column.CustomBefore Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                            break;
                                        case MatrixTypes.After:
                                            <AMproverNumberInput ReadOnly=IsReadOnly HideStyling="@true" UsedInGrid="@true" Format="F0" @bind-Value=@column.CustomAfter Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                            break;
                                        case MatrixTypes.Pmo:
                                            <AMproverNumberInput ReadOnly=IsReadOnly HideStyling="@true" UsedInGrid="@true" Format="F0" @bind-Value=@column.CustomPmo Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                            break;
                                    }
                                </td>

                                @if (columnTemplate?.HasSubColumns == true && ActivatedSubgrids.Contains(index))
                                {
                                    var subGrid = Risk.SubDataGrids.FirstOrDefault(x => x.ColumnId == index);
                                    var templateSubGrid = Risk.Template.SubGrids.FirstOrDefault(x => x.ColumnId == index);
                                    var noOfSubColumns = templateSubGrid?.TableColumns.Count; //when a sub column is deleted, the index can be too high -> continue (next line)

                                    for (var subIndex = 0; subIndex < noOfSubColumns; subIndex++)
                                    {
                                        var subColumn = subGrid.TableColumns[subIndex];

                                        <td>
                                            @switch (MatrixType)
                                            {
                                                case MatrixTypes.Before:
                                                    <AMproverNumberInput ReadOnly=IsReadOnly HideStyling="@true" UsedInGrid="@true" Format="F0" @bind-Value=@subColumn.CustomBefore OnBlur=@UpdateRiskWithCustomValues />
                                                    break;
                                                case MatrixTypes.After:
                                                    <AMproverNumberInput ReadOnly=IsReadOnly HideStyling="@true" UsedInGrid="@true" Format="F0" @bind-Value=@subColumn.CustomAfter OnBlur=@UpdateRiskWithCustomValues />
                                                    break;
                                                case MatrixTypes.Pmo:
                                                    <AMproverNumberInput ReadOnly=IsReadOnly HideStyling="@true" UsedInGrid="@true" Format="F0" @bind-Value=@subColumn.CustomPmo OnBlur=@UpdateRiskWithCustomValues />
                                                    break;
                                            }
                                        </td>
                                    }
                                }
                            }
                        }
                    </tr>
                }
                @if (DisplayVersion == 3)
                {
                    <tr>
                        @for (var index = 0; index < effectColumns; index++)
                        {
                            @if (Risk?.MainDataGrid.TableColumns.Count > index)
                            {
                                var column = Risk.MainDataGrid.TableColumns[index];
                                var columnTemplate = Risk.Template?.MainGrid.TableColumns[index];

                                <td>
                                    @switch (MatrixType)
                                    {
                                        case MatrixTypes.Before:
                                            <AMproverNumberInput ReadOnly=IsReadOnly Min=0 HideStyling="@true" UsedInGrid="@true" AllowEmpty=true Format="F0" @bind-Value=@column.PointsBefore Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                            break;
                                        case MatrixTypes.After:
                                            <AMproverNumberInput ReadOnly=IsReadOnly Min=0 HideStyling="@true" UsedInGrid="@true" AllowEmpty=true Format="F0" @bind-Value=@column.PointsAfter Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                            break;
                                        case MatrixTypes.Pmo:
                                            <AMproverNumberInput ReadOnly=IsReadOnly Min=0 HideStyling="@true" UsedInGrid="@true" AllowEmpty=true Format="F0" @bind-Value=@column.PointsPmo Disabled="@(columnTemplate?.HasSubColumns ?? false)" OnBlur=@UpdateRiskWithCustomValues />
                                            break;
                                    }
                                </td>

                                @if (columnTemplate?.HasSubColumns == true && ActivatedSubgrids.Contains(index))
                                {
                                    var subGrid = Risk.SubDataGrids.FirstOrDefault(x => x.ColumnId == index);
                                    var templateSubGrid = Risk.Template.SubGrids.FirstOrDefault(x => x.ColumnId == index);
                                    var noOfSubColumns = templateSubGrid?.TableColumns.Count; //when a sub column is deleted, the index can be too high -> continue (next line)

                                    for (var subIndex = 0; subIndex < noOfSubColumns; subIndex++)
                                    {
                                        var subColumn = subGrid.TableColumns[subIndex];

                                        <td>
                                            @switch (MatrixType)
                                            {
                                                case MatrixTypes.Before:
                                                    <AMproverNumberInput ReadOnly=IsReadOnly Min=0 HideStyling="@true" UsedInGrid="@true" AllowEmpty=true Format="F0" @bind-Value=@subColumn.PointsBefore OnBlur=@UpdateRiskWithCustomValues />
                                                    break;
                                                case MatrixTypes.After:
                                                    <AMproverNumberInput ReadOnly=IsReadOnly Min=0 HideStyling="@true" UsedInGrid="@true" AllowEmpty=true Format="F0" @bind-Value=@subColumn.PointsAfter OnBlur=@UpdateRiskWithCustomValues />
                                                    break;
                                                case MatrixTypes.Pmo:
                                                    <AMproverNumberInput ReadOnly=IsReadOnly Min=0 HideStyling="@true" UsedInGrid="@true" AllowEmpty=true Format="F0" @bind-Value=@subColumn.PointsPmo OnBlur=@UpdateRiskWithCustomValues />
                                                    break;
                                            }
                                        </td>
                                    }
                                }
                            }
                        }
                    </tr>
                }
            </tfoot>
        </table>
    </div>
    <div class="row px-3 matrix-checks ml-auto">
        @if (!IsCsirView && !IsSapaView)
        {
            <div class="col-sm-6">
                <div class="neg-margin-small">
                    <RadzenDropDown TValue="int" @bind-Value="@DisplayVersion" Data="@DropdownItems" TextProperty="Text" ValueProperty="Value" Style="width: 100%;">
                    </RadzenDropDown>
                </div>
            </div>
        }
        @if (!TypesSetToColumn)
        {
            <div class="col-sm-6">
                <div class="row">
                    <label class="col-sm-3 textbox-height mtbf-label">@MtbfLabel</label>
                    <div class="col-sm-5 mtbf-input">
                        @switch (MatrixType)
                        {
                            case MatrixTypes.Before:
                                <AMproverNumberInput class="form-control"
                                                     @bind-Value=@Risk.MainDataGrid.CustomMtbfBefore
                                                     OnBlur=@ChangeCustomMtbfValue
                                                     ForceInvariantCulture=true
                                                     ReadOnly=IsReadOnly />
                                break;
                            case MatrixTypes.After:
                                <AMproverNumberInput class="form-control"
                                                     @bind-Value=@Risk.MainDataGrid.CustomMtbfAfter
                                                     OnBlur=@ChangeCustomMtbfValue
                                                     ForceInvariantCulture=true
                                                     ReadOnly=IsReadOnly />
                                break;
                            case MatrixTypes.Pmo:
                                <AMproverNumberInput class="form-control"
                                                     @bind-Value=@Risk.MainDataGrid.CustomMtbfPmo
                                                     OnBlur=@ChangeCustomMtbfValue
                                                     ForceInvariantCulture=true
                                                     ReadOnly=IsReadOnly />
                                break;
                        }
                    </div>
                    <span class="col-sm-3 textbox-height year-label">@YearLabel</span>
                </div>
            </div>
        }
    </div>
</div>