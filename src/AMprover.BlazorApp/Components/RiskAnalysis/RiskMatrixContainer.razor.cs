using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using System;
using System.Collections.Generic;
using AMprover.BlazorApp.Pages.RiskOrganize.Risks;

namespace AMprover.BlazorApp.Components.RiskAnalysis;

public partial class RiskMatrixContainer
{
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private IJSRuntime JsRuntime { get; set; }
    [Inject] private IStringLocalizer<RiskEdit> Localizer { get; set; }
    [Inject] private IGlobalDataService GlobalDataService { get; set; }

    [Parameter] public RiskModel Risk { get; set; }
    [Parameter] public EventCallback<RiskModel> OnChangeCallback { get; set; }
    [Parameter] public string Language { get; set; }
    [Parameter] public MatrixTypes MatrixType { get; set; }
    [Parameter] public string MtbfLabel { get; set; }
    [Parameter] public string YearLabel { get; set; }
    [Parameter] public string ShowValuesLabel { get; set; }
    [Parameter] public string ShowCustomValuesLabel { get; set; }
    [Parameter] public string ShowPointsLabel { get; set; }
    [Parameter] public bool IsRiskAnalysis { get; set; }
    [Parameter] public bool IsSapaView { get; set; }
    [Parameter] public bool TypesSetToColumn { get; set; }
    [Parameter] public bool IsCsirView { get; set; }
    [Parameter] public bool IsReadOnly { get; set; }
    [Parameter] public string Currency { get; set; }

    private int DisplayVersion { get; set; } = 1;
    private List<int> ActivatedSubgrids { get; set; } = [];
    private bool _canEdit => GlobalDataService.CanEdit;

    private List<DropDownItem> DropdownItems { get; set; } = [];

    protected override void OnInitialized()
    {
        if (IsCsirView) DisplayVersion = 3;

        DropdownItems =
        [
            new() { Value = 1, Text = Localizer["RaShowValuesLbl"] },
            new() { Value = 2, Text = Localizer["RaShowCustValuesLbl"] },
            new() { Value = 3, Text = Localizer["RaShowPointsLbl"] }
        ];
    }

    private bool CanEdit()
    {
        return _canEdit && !IsReadOnly;
    }

    private void UpdateRiskWithCustomValues()
    {
        if (!CanEdit()) return;
        RiskAnalysisManager.UpdateRiskWithSelectedEffect(Risk, MatrixType);

        if (OnChangeCallback.HasDelegate)
            OnChangeCallback.InvokeAsync(Risk);
    }

    private void ProcessEffectChanges(int column, int row)
    {
        if (!CanEdit()) return;
        RiskAnalysisManager.UpdateRiskWithSelectedEffect(Risk, row, column, MatrixType);

        if (OnChangeCallback.HasDelegate)
            OnChangeCallback.InvokeAsync(Risk);
    }

    private void ProcessEffectChanges(int column, int row, int parent, bool isPercentage)
    {
        if (!CanEdit()) return;
        RiskAnalysisManager.UpdateRiskWithSelectedEffect(Risk, row, column, parent, MatrixType, isPercentage);

        if (OnChangeCallback.HasDelegate)
            OnChangeCallback.InvokeAsync(Risk);
    }

    private void ChangeCustomMtbfValue()
    {
        if (!CanEdit())
            return;

        RiskAnalysisManager.UpdateRiskWithCustomMtbf(Risk, MatrixType);

        if (OnChangeCallback.HasDelegate)
            OnChangeCallback.InvokeAsync(Risk);
    }

    private void ProcessDataChanges(int column, int row)
    {
        if (!CanEdit())
            return;

        RiskAnalysisManager.UpdateRiskWithSelectedColorData(Risk, row, column, MatrixType);

        if (OnChangeCallback.HasDelegate)
            OnChangeCallback.InvokeAsync(Risk);
    }

    private GridType MatrixTypeToGridType()
    {
        return MatrixType switch
        {
            MatrixTypes.After => GridType.RiskWithTakenActions,
            MatrixTypes.Before => GridType.RiskWithoutActions,
            MatrixTypes.Pmo => GridType.RiskWithCurrentActions,
            _ => throw new ArgumentOutOfRangeException()
        };
    }

    private void AddActiveSubgrid(int column)
    {
        if (!ActivatedSubgrids.Contains(column))
            ActivatedSubgrids.Add(column);
    }

    private void RemoveActiveSubgrid(int column)
    {
        if (ActivatedSubgrids.Contains(column))
            ActivatedSubgrids.Remove(column);
    }

    private RiskMatrixTemplateColumn GetColumn(int column)
    {
        return Risk.Template.MainGrid.TableColumns[column];
    }

    private void ShowTooltipRiskMatrix(ElementReference elementRef, string text)
    {
        if (!string.IsNullOrEmpty(text))
        {
            JsRuntime.InvokeVoidAsync("showTooltip", elementRef, text);
        }
    }

    private void HideTooltip(ElementReference elementRef)
    {
        JsRuntime.InvokeVoidAsync("hideTooltip", elementRef);
    }

    private class DropDownItem
    {
        public int Value { get; set; }
        public string Text { get; set; }
    }
}