﻿<Radzen.Blazor.RadzenTemplateForm TItem=RiskModel Data=@Risk>
    <DataAnnotationsValidator />
    <div class="row">
        <div class="col-sm-3">
            <div class="sapa-tree-frame my-3 mr-3 pt-4 px-4 pb-3">
                <div>
                    <text class="sapa-large-bold"> @Localizer["SapaDetPortfolioTxt"]:</text><br />
                </div>
                <div>
                    <text class="right"> - @Risk.Installation</text><br />
                </div>
                <div>
                    <text class="right"> &nbsp - @Risk.System</text><br />
                </div>
                <div>
                    <text class="right"> &nbsp &nbsp - @Risk.Component</text>
                </div>
            </div>
        </div>
        <div class="col-sm-5">
            <div class="my-0 mr-3 pt-4 px-4 pb-3">
                <div>
                    <text class="sapa-overview-header"> @Risk.FailureMode</text><br /><br />
                </div>
                <text class="bold"> @Localizer["SapaDetMotivation"]:</text>
                <div>
                    <text class="ml-2"> @Risk.Description</text><br />
                </div>
                <text class="bold"> @Localizer["SapaDetFailureConsequences"]:</text>
                <div>
                    <text class="ml-2"> @Risk.FailureConsequences</text><br />
                </div>
                <text class="bold"> @Localizer["SapaDetFailureCause"]:</text>
                <div>
                    <text class="ml-2"> @Risk.FailureCause</text><br />
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="sapa-overview-frame my-3 mr-3 pt-4 px-4 pb-3">
                <div>
                    <text class="bold"> @Localizer["SapaDetValueRiskBefore"]:</text>
                    <text class="currency-bold"> @FormatAsSelectedCurrency(Risk.RiskBefore)</text>
                </div><br />
                <div>
                    <text class="bold"> @Localizer["SapaDetRequested"]:</text>
                    <text class="currency-bold"> @FormatAsSelectedCurrency(Risk.ActionCosts)</text>
                </div><br />
                <div>
                    <text class="bold"> @Localizer["SapaDetValueRiskAfter"]:</text>
                    <text class="currency-bold"> @FormatAsSelectedCurrency(Risk.RiskAfter)</text>
                </div>
                <br /><br /><br />
                <text class="large-bold-right">
                    @Localizer["SapaDetIndex"]: &nbsp @FormatAsNumber(Risk.SapaIndex)
                </text>
            </div>
        </div>
    </div>

    <RadzenTabs>
        <Tabs>
            <RadzenTabsItem Text=@Localizer["SapaRiskMatricesLbl"]>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="rz-mx-4 sapa-large-bold">
                            @Localizer["SapaDetMatrixWithout"]:
                        </div>
                        <div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="rz-mx-4 sapa-large-bold">
                            @Localizer["SapaDetMatrixWith"]:
                        </div>
                        <div>
                        </div>
                    </div>
                </div>
            </RadzenTabsItem>
            <RadzenTabsItem Text=@Localizer["SapaDetAttachmentTabTxt"]>
                <div class="row riskassessment-top-tabs">
                    <div class="col-sm-8">
                        <div style="max-height:250px;">
                            <AttachmentContainer ReadOnly="true" Data=@Risk.Attachments SaveCallback=@UpdateCurrentRisk ParentItem=@Risk />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <text class="bold"> @Localizer["SapaDetRemark1"]:</text>
                        <div>
                            <text class="ml-2"> @Risk.Remarks1</text><br />
                        </div>
                    </div>
                </div>
            </RadzenTabsItem>
        </Tabs>
    </RadzenTabs>
    <br />
    <UtilityGrid TItem=TaskModel
                 @ref=PreventiveActionsGrid
                 Data=@Risk?.Tasks.ToList()
                 FileName=@GridNames.RiskEdit.PreventiveActions
                 Title=@Localizer["SapaDetDefinedActions"]
                 UseOpenTextInsteadOfEdit=true
                 DropDownOverrides=@TaskModelDropDownOverrides
                 CssClass="neg-margin-large" />

    @if ((@Risk?.Spares.ToList()).Any())
    {
        <br />
        <UtilityGrid TItem=SpareModel
                     @ref=SparesGrid
                     Title=@Localizer["SapaDetDefinedSpares"]
                     Data=@Risk?.Spares.ToList()
                     FileName=@GridNames.RiskEdit.Spares
                     CssClass="neg-margin-large" />
    }

</Radzen.Blazor.RadzenTemplateForm>
