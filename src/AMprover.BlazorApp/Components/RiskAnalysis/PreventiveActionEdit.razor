﻿@if (PreventiveAction != null)
{
    <Radzen.Blazor.RadzenTemplateForm TItem="TaskModel" Data=@PreventiveAction Submit=@ValidTaskSubmitted InvalidSubmit=@InvalidTaskSubmitted>
        <DataAnnotationsValidator/>
        <div class="row">
            <div class="col-sm-4">
                <AMproverTextBox @bind-Value=PreventiveAction.Name 
                    Disabled=@DisableName
                    MaxLength="60"
                    Label=@Localizer["PaActionLbl"] Required=true />
            </div>
            <div class="col-sm-4">
                <AMproverTextBox @bind-Value=PreventiveAction.ReferenceId
                    Disabled=@DisableReferenceId
                    MaxLength="30"
                    Label=@Localizer["PaReferenceLbl"] Required=false />
            </div>
            @if (IsClusterTask)
            {
                <div class="col-sm-4">
                    <AMDropdown AllowClear="true" Label=@Localizer["PaValueRiskLbl"] 
                        Data=@RiskDict @bind-Value=@PreventiveAction.Risk.Id />
                </div>
            }
            else
            {
                <div class="col-sm-4">
                    <AMDropdown AllowClear="true" AllowFiltering="true"
                        Data=@CommonActionDict.ToNullableDictionary()
                        @bind-Value=@PreventiveAction.CommonActionId 
                        Change=@(() => ProcessCommonActionContent(true)) 
                        Label=@Localizer["PaCommonActionLbl"] />
                </div>
            }
        </div>
        <div class="row">
            <div class="col-sm-4">
                <AMDropdown Disabled=@DisableActionType AllowFiltering="true"
                    Data="@TaskDict" @bind-Value="@PreventiveAction.Type" 
                    Label=@Localizer["PaActionTypeLbl"] Required=true ShowValidations=@ShowFailedValidations />
            </div>
            <div class="col-sm-4">
                <AMDropdown Disabled=@DisablePolicy AllowFiltering="true"
                    Data=@PolicyDict.ToNullableDictionary()
                    @bind-Value="@PreventiveAction.MxPolicy"
                    Label=@Localizer["PaPolicyLbl"] 
                    Required=true ShowValidations=@ShowFailedValidations  />
            </div>
            <div class="col-sm-4">
                <AMDropdown AllowFiltering="true" Disabled=@DisableInitiator
                    Data=@InitiatorsDict.ToNullableDictionary()
                    @bind-Value="@PreventiveAction.InitiatorId"
                    Label=@Localizer["PaInitiatorLbl"]
                    Required=true ShowValidations=@ShowFailedValidations />
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                @if (IsSapaTask)
                {
                    <AMDropdown AllowClear="true" AllowFiltering="true"
                                Data=@SapaWorkPackageDict.ToNullableDictionary()
                                @bind-Value="@PreventiveAction.SapaWorkpackageId"
                                Label=@Localizer["PaSapaWorkPackageLbl"]
                                Required=true ShowValidations=@ShowFailedValidations />
                }
                else
                {
                    <AMDropdown AllowClear="true" AllowFiltering="true" Disabled=@DisableWorkPackage
                                Data=@WorkPackageDict.ToNullableDictionary()
                                @bind-Value="@PreventiveAction.WorkpackageId"
                                Label=@Localizer["PaWorkPackageLbl"]
                                Required=true ShowValidations=@ShowFailedValidations />
                }
            </div>
            <div class="col-sm-4">
                <AMDropdown AllowFiltering="true" Disabled=@DisableExecutor
                    Data=@ExecutorsDict.ToNullableDictionary()
                    @bind-Value="@PreventiveAction.ExecutorId"
                    Label=@Localizer["PaExecutorLbl"] 
                    Required=true ShowValidations=@ShowFailedValidations />
            </div>
            <div class="col-sm-4">
                <div class="row">
                    <div class="col-sm-5 ">
                        <AMproverNumberInput Name="Interval" TValue="decimal?" Format="n2" 
                            @bind-Value="PreventiveAction.Interval"
                            Change=@ReCalculateTaskCosts Disabled=@DisableIntervalUnit 
                            Label=@Localizer["PaEveryLbl"] 
                            Required=true DefaultValue=0/>
                    </div>
                    <div class="col-sm-7 ">
                        <AMDropdown AllowClear="true" AllowFiltering="true"
                            Data=@IntervalUnitsDict.ToNullableDictionary()
                            @bind-Value=@PreventiveAction.IntervalUnitId
                            Change=@ReCalculateTaskCosts Disabled=@DisableIntervalUnit
                            Label="IntervalUnit" 
                            Required=true ShowValidations=@ShowFailedValidations />
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            @if (IsSapaTask)
            {
                <div class="col-sm-2">
                    <AMproverNumberInput Label=@Localizer["PaValidFromLbl"] TValue="int?" @bind-Value=@PreventiveAction.ValidFromYear class="form-control" />
                </div>
                <div class="col-sm-2">
                    <AMproverNumberInput Label=@Localizer["PaValidUntilLbl"] TValue="int?" @bind-Value=@PreventiveAction.ValidUntilYear class="form-control" />
                </div>
                <div class="col-4">
                </div>
                <div class="col-4">
                    <AMproverNumberInput Label=@Localizer["PaCalcCostLbl"] TValue="decimal?"
                                         @bind-Value=@PreventiveAction.EstCosts Disabled="true"
                                         Format="c0" />
                </div>
            }
            else
            {
            <div class="col-sm-4">
                <AMDropdown AllowFiltering="true" AllowClear=true
                    Data=@PartOfDict
                    @bind-Value=@PreventiveAction.PartOf
                    Label=@Localizer["PaPartOfLbl"] />
            </div>
            <div class="col-4">
                <AMproverNumberInput Label=@Localizer["PaEstCostLbl"] TValue="decimal?" 
                    @bind-Value=@PreventiveAction.EstCosts
                    Format="c0" />
            </div>
            <div class="col-4">
                <AMproverNumberInput Label=@Localizer["PaCalcCostLbl"] TValue="decimal?" 
                    @bind-Value=@PreventiveAction.ClusterCosts Disabled="true" 
                    Format="c2" />
            </div>
            }
        </div>
        @if (IsSapaTask)
        {
            <div class="row">
                @if (GetYears() >= 0)
                {
                    <div class="col-sm-2">
                        <AMproverNumberInput Label=@Localizer["PaCostY1"] TValue="decimal?" @bind-Value=@PreventiveAction.CostY1 Change=@UpdateYearCosts class="form-control" Format="c0" />
                    </div>
                }
                @if (GetYears() >= 1)
                {
                    <div class="col-sm-2">
                        <AMproverNumberInput Label=@Localizer["PaCostY2"] TValue="decimal?" @bind-Value=@PreventiveAction.CostY2 Change=@UpdateYearCosts class="form-control" Format="c0" />
                    </div>                    
                }
                @if (GetYears() >= 2)
                {
                    <div class="col-sm-2">
                        <AMproverNumberInput Label=@Localizer["PaCostY3"] TValue="decimal?" @bind-Value=@PreventiveAction.CostY3 Change=@UpdateYearCosts class="form-control" Format="c0" />
                    </div>
                }
                @if (GetYears() >= 3)
                {
                    <div class="col-sm-2">
                        <AMproverNumberInput Label=@Localizer["PaCostY4"] TValue="decimal?" @bind-Value=@PreventiveAction.CostY4 Change=@UpdateYearCosts class="form-control" Format="c0" />
                    </div>
                }
                @if (GetYears() >= 4)
                {
                    <div class="col-sm-2">
                        <AMproverNumberInput Label=@Localizer["PaCostY5"] TValue="decimal?" @bind-Value=@PreventiveAction.CostY5 Change=@UpdateYearCosts class="form-control" Format="c0" />
                    </div>
                }
            </div>
        }
        <RadzenTabs>
            <Tabs>
                <RadzenTabsItem Text=@Localizer["PaDescriptionLbl"]>
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <AMproverTextArea @bind-Value=@PreventiveAction.Description Cols="30" Rows="4" Label=@Localizer["PaDescriptionLbl"] />
                        </div>
                        <div class="col-sm-6">
                            <AMproverTextArea @bind-Value=@PreventiveAction.GeneralDescription Cols="30" Rows="4" Label=@Localizer["PaInstructionLbl"] />
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@Localizer["PaRemarksLbl"]>
                    <div class="row mb-3">
                        <div class="col-sm-12">
                            <AMproverTextArea @bind-Value=@PreventiveAction.Remark Cols="30" Rows="4" Label=@Localizer["PaRemarksLbl"] />
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@Localizer["PaNormConditionLbl"]>
                    <div class="row mb-3">
                        <div class="col-sm-6">
                            <AMproverTextArea @bind-Value=@PreventiveAction.Norm Cols="30" Rows="4" Label=@Localizer["PaNormConditionLbl"] />
                        </div>
                        <div class="col-sm-6">
                            <AMproverTextArea @bind-Value=@PreventiveAction.Permit Cols="30" Rows="4" Label=@Localizer["PaPermitLbl"] />
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@Localizer["PaExtraLbl"]>
                    <div class="row">
                        <div class="col-sm-2">
                            <AMproverNumberInput Label=@Localizer["PaSortOrderLbl"] Format="0" TValue="int?" @bind-Value="PreventiveAction.SortOrder" class="form-control" />
                        </div>
                        <div class="col-sm-4">
                            <AMDropdown AllowClear="true" AllowFiltering="true"
                                        Data="@PartOfDict"
                                        @bind-Value="@PreventiveAction.PartOf"
                                        Label=@Localizer["PaPartOfTaskLbl"] />
                        </div>
                        <div class="col-sm-3">
                            <AMproverNumberInput TValue="decimal?" @bind-Value=@PreventiveAction.DownTime Format="N2" Label=@Localizer["PaDownTimeLbl"] Change=@CalculateDowntimeCosts />
                        </div>
                        <div class="col-sm-3">
                            <AMproverNumberInput TValue="decimal?" @bind-Value=@PreventiveAction.Duration Format="N2" Label=@Localizer["PaDurationLbl"] />
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <AMproverNumberInput Label=@Localizer["PaValidFromLbl"] TValue="int?" @bind-Value=@PreventiveAction.ValidFromYear class="form-control" />
                        </div>
                        <div class="col-sm-3">
                            <AMproverNumberInput Label=@Localizer["PaValidUntilLbl"] TValue="int?" @bind-Value=@PreventiveAction.ValidUntilYear class="form-control" />
                        </div>
                        <div class="col-sm-3">
                            <AMDropdown @bind-Value="@PreventiveAction.Status"
                                        AllowClear=true
                                        Data=@StatusDict.ToNullableDictionary()
                                        Label=@Localizer["PaAssesmentStatusLbl"] />
                        </div>
                        <div class="col-sm-3">
                            <AMproverNumberInput TValue="decimal?" @bind-Value=@PreventiveAction.DtCost Format="C2" Disabled="true" Label=@Localizer["PaDtCostLbl"] />
                        </div>
                    </div>
                </RadzenTabsItem>
                <RadzenTabsItem Text=@Localizer["PaCostLbl"]>
                    <div class="row">
                        <div class="col-sm-4">
                            <AMDropdown AllowFiltering="true" 
                                        Label=@Localizer["PaUnitTypeLbl"]
                                        Data=@UnitTypeDict
                                        @bind-Value=@PreventiveAction.UnitType 
                                        Change=@ReCalculateTaskCosts />
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput Label=@Localizer["PaTotalUnitsLbl"] TValue="decimal?" @bind-Value=@PreventiveAction.Units class="form-control" Change=@ReCalculateEstimatedCosts  />
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput Label=@Localizer["PaCostPerUnitsLbl"] @bind-Value=@PreventiveAction.EstCostPerUnit TValue="decimal?"
                                                 Format="c2"  class="form-control" Change=@ReCalculateEstimatedCosts />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4">
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput ReadOnly="true" Label=@Localizer["PaClusterCostPerUnitLbl"] @bind-Value=@PreventiveAction.ClusterCostPerUnit TValue="decimal?" 
                                                 Format="c2"  class="form-control" Change=@ReCalculateTaskCosts />
                        </div>
                        <div class="col-sm-4">
                            <AMproverNumberInput ReadOnly="true" Label=@Localizer["PaClusterCostLbl"] @bind-Value=@PreventiveAction.ClusterCosts TValue="decimal?"
                                                 Format="c2"  class="form-control" Change=@ReCalculateTaskCosts />
                        </div>
                    </div>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
        <RadzenButton type="submit" class="btn btn-primary" Text=@Localizer["PaSaveBtn"] Disabled=!GlobalDataService.CanEdit />
    </Radzen.Blazor.RadzenTemplateForm>
}
