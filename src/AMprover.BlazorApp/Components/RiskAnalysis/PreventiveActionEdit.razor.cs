﻿using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Ra<PERSON>zen;
using System.Collections.Generic;
using Newtonsoft.Json;
using System.Linq;
using AMprover.BlazorApp.Enums;
using AutoMapper;
using Microsoft.Extensions.Localization;

namespace AMprover.BlazorApp.Components.RiskAnalysis;

public partial class PreventiveActionEdit
{
    [Inject] private IRiskAnalysisManager RiskAnalysisManager { get; set; }
    [Inject] private IPortfolioSetupManager PortfolioSetupManager { get; set; }
    [Inject] private ILookupManager LookupManager { get; set; }
    [Inject] private IMapper Mapper { get; set; }
    [Inject] private IDropdownManager DropdownManager { get; set; }
    [Inject] private IObjectManager ObjectManager { get; set; }
    [Inject] private DialogService DialogService { get; set; }
    [Inject] private IGlobalDataService GlobalDataService { get; set; }
    [Inject] private ILogger<PreventiveActionEdit> Logger { get; set; }
    [Inject] private IStringLocalizer<PreventiveActionEdit> Localizer { get; set; }

    [Parameter] public int PreventiveActionId { get; set; }
    [Parameter] public int? RiskId { get; set; }
    [Parameter] public bool IsClusterTask { get; set; }
    [Parameter] public bool Pmo { get; set; }
    [Parameter] public bool IsSapaTask { get; set; }
    [Parameter] public EventCallback<TaskModel> Callback { get; set; }

    public TaskModel PreventiveAction { get; set; }
    private RiskModel Risk { get; set; }
    private ObjectModel ObjectModel { get; set; }

    private bool ShowFailedValidations { get; set; }
    public bool IsSapaView { get; set; }

    private Dictionary<int, string> ExecutorsDict { get; set; } = new();
    private Dictionary<int, string> InitiatorsDict { get; set; } = new();
    private Dictionary<int, string> WorkPackageDict { get; set; } = new();
    private Dictionary<int, string> SapaWorkPackageDict { get; set; } = new();
    private Dictionary<int, string> IntervalUnitsDict { get; set; } = new();
    public Dictionary<int, string> ClusterDict { get; set; } = new();
    private Dictionary<int, string> PolicyDict { get; set; } = new();
    private Dictionary<int, string> UnitTypeDict { get; set; } = new();
    private Dictionary<string, string> TaskDict { get; set; } = new();
    public Dictionary<int, string> TaskOnRiskDict { get; set; } = new();
    private Dictionary<int, string> RiskDict { get; set; } = new();
    private Dictionary<int, string> CommonActionDict { get; set; } = new();
    private Dictionary<int?, string> PartOfDict { get; set; } = new();
    private Dictionary<int, string> StatusDict { get; set; } = new();

    private EntityEditorMode EditorMode
    {
        get
        {
            return PreventiveActionId switch
            {
                0 => EntityEditorMode.Create,
                _ => EntityEditorMode.Update,
            };
        }
    }

    private bool DisableReferenceId { get; set; }
    private bool DisableExecutor { get; set; }
    private bool DisableInitiator { get; set; }
    private bool DisableWorkPackage { get; set; }
    private bool DisableIntervalUnit { get; set; }
    private bool DisableCosts { get; set; }
    private bool DisablePolicy { get; set; }
    private bool DisableActionType { get; set; }
    private bool DisableName { get; set; }
    private bool IsCsirView { get; set; }

    protected override void OnInitialized()
    {
        ExecutorsDict = DropdownManager.GetExecutorDict();
        InitiatorsDict = DropdownManager.GetInitiatorDict();
        WorkPackageDict = DropdownManager.GetWorkpackageDict();
        SapaWorkPackageDict = DropdownManager.GetSapaWorkpackageDict();
        IntervalUnitsDict = DropdownManager.GetIntervalUnitDict();
        ClusterDict = DropdownManager.GetClusterDict();
        PolicyDict = DropdownManager.GetPolicyDict();
        RiskDict = DropdownManager.GetRisksDict();
        StatusDict = DropdownManager.GetStatusDict();
        UnitTypeDict = DropdownManager.GetLookupUserDefinedByFilterDict("UnitTypes");
        TaskDict = LookupManager.GetLookupByFilterDict("MeasureType", true);
        TaskOnRiskDict = DropdownManager.GetTasksDict((int)RiskId, Pmo);

        Risk = RiskAnalysisManager.GetRisk((int)RiskId);
        var task = RiskAnalysisManager.GetTaskById(PreventiveActionId);

        if (Risk != null && task != null)
        {
            task.CalculateCosts(Risk);
        }
            
        PreventiveAction = EditorMode switch
        {
            EntityEditorMode.Create => new TaskModel { Id = PreventiveActionId, MrbId = RiskId, Type = "tsk", Pmo = Pmo },
            _ => task
        };
            
        PartOfDict = Risk.Tasks.Where(x => x.PartOf == null && x.Id != PreventiveAction.Id).ToDictionary(x => (int?)x.Id, y => y.Name);
            
        var analysisType = Risk.RiskObject.AnalysisType.ToLower();
        IsCsirView = analysisType.Contains("csir");
        IsSapaView = analysisType.Contains("sapa");

        if (!IsCsirView)
            CommonActionDict = DropdownManager.GetCommonTasksDict();
        else if (Risk.SystemId == null)
            CommonActionDict = DropdownManager.GetCommonTasksPrioDict();
        else if (Risk.ComponentId == null)
        {
            ObjectModel = ObjectManager.GetObject((int)Risk.SystemId);
            CommonActionDict = DropdownManager.GetCommonTasksPrioDefinedByFilterDict(ObjectModel.ShortKey);
        }
        else
        {
            ObjectModel = ObjectManager.GetObject((int)Risk.ComponentId);
            CommonActionDict = DropdownManager.GetCommonTasksPrioDefinedByFilterDict(ObjectModel.ShortKey);
        }

        // Set disabled state of common action related fields
        ProcessCommonActionContent();

        base.OnInitialized();
    }

    private void ProcessCommonActionContent(bool setFields = false)
    {
        var selectedCommonAction = PreventiveAction.CommonActionId;
        if (!selectedCommonAction.HasValue)
        {
            DisableInitiator = false;
            DisableExecutor = false;
            DisableWorkPackage = false;
            DisableIntervalUnit = false;
            DisableCosts = false;
            DisablePolicy = false;
            DisableName = false;
            DisableActionType = false;

            return;
        }

        var commonAction = PortfolioSetupManager.GetCommonAction(selectedCommonAction.Value);

        if (setFields)
        {
            if (!string.IsNullOrEmpty(commonAction.Name))
                PreventiveAction.Name = commonAction.Name;

            if(!string.IsNullOrEmpty(commonAction.ReferenceId))
                PreventiveAction.ReferenceId = commonAction.ReferenceId;

            if (!string.IsNullOrEmpty(commonAction.Description))
                PreventiveAction.Description = commonAction.Description;

            if (!string.IsNullOrEmpty(commonAction.GeneralDescription))
                PreventiveAction.GeneralDescription = commonAction.GeneralDescription;

            if (!string.IsNullOrEmpty(commonAction.Remark))
                PreventiveAction.Remark = commonAction.Remark;

            if (!string.IsNullOrEmpty(commonAction.Permit))
                PreventiveAction.Permit = commonAction.Permit;

            if (!string.IsNullOrEmpty(commonAction.Responsible))
                PreventiveAction.Responsible = commonAction.Responsible;

            if (!string.IsNullOrEmpty(commonAction.Type))
                PreventiveAction.Type = commonAction.Type;

            if (commonAction.MxPolicyId.HasValue)
                PreventiveAction.MxPolicy = commonAction.MxPolicyId;

            if (commonAction.InitiatorId.HasValue)
                PreventiveAction.InitiatorId = commonAction.InitiatorId.Value;

            if (commonAction.ExecutorId.HasValue)
                PreventiveAction.ExecutorId = commonAction.ExecutorId.Value;

            if (commonAction.WorkPackageId.HasValue)
                PreventiveAction.WorkpackageId = commonAction.WorkPackageId.Value;

            if (commonAction.IntervalUnitId.HasValue)
                PreventiveAction.IntervalUnitId = commonAction.IntervalUnitId.Value;

            if (commonAction.Interval.HasValue)
                PreventiveAction.Interval = commonAction.Interval.Value;

            if (commonAction.Costs.HasValue)
                PreventiveAction.EstCosts = commonAction.Costs.Value;

            if (commonAction.DownTime.HasValue)
                PreventiveAction.DownTime = commonAction.DownTime.Value;

            if (commonAction.Duration.HasValue)
                PreventiveAction.Duration = commonAction.Duration.Value;

            if (commonAction.PriorityCode.HasValue)
                PreventiveAction.PriorityCode = commonAction.PriorityCode.Value;

            if (commonAction.SortOrder.HasValue)
                PreventiveAction.SortOrder = commonAction.SortOrder.Value;

            PreventiveAction.UnitType = commonAction.UnitType;
        }

        //Determine which fields need to be readonly
        DisableInitiator = commonAction.InitiatorModifiable == false;
        DisableExecutor = commonAction.ExecutorModifiable == false;
        DisableWorkPackage = commonAction.WorkPackageModifiable == false;
        DisableIntervalUnit = commonAction.IntervalModifiable == false;
        DisableCosts = commonAction.CostModifiable == false;

        //always readonly when common action is selected
        DisableReferenceId = !string.IsNullOrWhiteSpace(commonAction.ReferenceId);
        DisablePolicy = true;
        DisableName = true;
        DisableActionType = true;

        // Fix UI bug where all fields required fields give an error message
        PreventiveAction = JsonConvert.DeserializeObject<TaskModel>(JsonConvert.SerializeObject(PreventiveAction));
    }

    private void ValidTaskSubmitted(TaskModel task)
    {
        RemoveYearCostsFromNonExistentYears(task);
        ShowFailedValidations = false;
        Callback.InvokeAsync(task);
        DialogService.Close();
    }

    private void RemoveYearCostsFromNonExistentYears(TaskModel task)
    {
        if (task.ValidFromYear > 0)
        {
            var years = (task.ValidUntilYear ?? task.ValidFromYear) - task.ValidFromYear + 1;

            if (years <= 1)
            {
                task.CostY2 = 0;
            }
            if (years <= 2)
            {
                task.CostY3 = 0;
            }
            if (years <= 3)
            {
                task.CostY4 = 0;
            }
            if (years <= 4)
            {
                task.CostY5 = 0;
            }

            UpdateYearCosts();
        }
    }

    private void ReCalculateTaskCosts()
    {
        PreventiveAction.CalculateCosts();
    }

    private void ReCalculateEstimatedCosts()
    { 
        PreventiveAction.RecalculateEstimatedCosts();
        PreventiveAction.CalculateCosts();
    }

    private void CalculateDowntimeCosts()
    {
        Risk = RiskAnalysisManager.GetRisk((int)RiskId);

        var DtCost = Risk.RiskObject.DownTimeCost;
        PreventiveAction.CalculateDownTimeCost(DtCost);
    }

    private int GetYears()
    {
        if (PreventiveAction.ValidFromYear == null || PreventiveAction.ValidUntilYear == null)
            return 0;

        return PreventiveAction.ValidUntilYear.Value - PreventiveAction.ValidFromYear.Value;
    }

    private void UpdateYearCosts()
    {
        PreventiveAction.EstCosts =
            (PreventiveAction.CostY1 ?? 0)
            + (PreventiveAction.CostY2 ?? 0)
            + (PreventiveAction.CostY3 ?? 0)
            + (PreventiveAction.CostY4 ?? 0)
            + (PreventiveAction.CostY5 ?? 0);
    }

    private void InvalidTaskSubmitted(FormInvalidSubmitEventArgs args)
    {
        ShowFailedValidations = true;

        Logger.LogWarning(
            $"Invalid {EditorMode} form submitted for {nameof(TaskModel)} with Id {PreventiveActionId}.");

        Logger.LogWarning(JsonConvert.SerializeObject(args));
    }
}