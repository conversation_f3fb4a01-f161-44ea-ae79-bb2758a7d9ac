﻿@inherits RadzenComponentWithChildren
@if (Visible)
{
    <div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()">
        <button disabled="@Disabled" class="@getButtonCss()" type="button" @onclick="@OnClick" @onclick:stopPropagation=true>
            @if (!string.IsNullOrEmpty(@Icon))
            {
                <i class="rz-button-icon-left rzi">@((MarkupString) Icon)</i>
            }
            @if (!string.IsNullOrEmpty(Image))
            {
                <img class="rz-button-icon-left rzi" src="@Image"/>
            }
            @if (!string.IsNullOrEmpty(Text))
            {
                <span class="rz-button-text">@Text</span>
            }
            else
            {
                <span class="rz-button-text">&nbsp;</span>
            }
        </button>
        <button onclick="@OpenPopupScript()" class="@getPopupButtonCss()" icon="rzi rzi-chevron-down" type="button" @onclick:stopPropagation=true>
            <span aria-hidden="true" class="rz-button-icon-left rzi rzi-chevron-down"></span><span class="rz-button-text ">rz-btn</span>
        </button>
        <div id="@PopupID" class="rz-splitbutton-menu">
            <ul class="rz-menu-list">
                <CascadingValue Value=this>
                    @ChildContent
                </CascadingValue>
            </ul>
        </div>
    </div>
}

@code{

    private ElementReference Element { get; set; }

    /// <summary>
    /// Gets or sets the text.
    /// </summary>
    /// <value>The text.</value>
    [Parameter]
    public string Text { get; set; } = "";

    /// <summary>
    /// Gets or sets the icon.
    /// </summary>
    /// <value>The icon.</value>
    [Parameter]
    public string Icon { get; set; }

    /// <summary>
    /// Gets or sets the image.
    /// </summary>
    /// <value>The image.</value>
    [Parameter]
    public string Image { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether this <see cref="RadzenSplitButton"/> is disabled.
    /// </summary>
    /// <value><c>true</c> if disabled; otherwise, <c>false</c>.</value>
    [Parameter]
    public bool Disabled { get; set; }

    /// <summary>
    /// Gets or sets the click callback.
    /// </summary>
    /// <value>The click callback.</value>
    [Parameter]
    public EventCallback<AMproverSplitButtonItem> Click { get; set; }

    /// <summary>
    /// Handles the <see cref="E:Click" /> event.
    /// </summary>
    /// <param name="args">The <see cref="MouseEventArgs"/> instance containing the event data.</param>
    public async Task OnClick(MouseEventArgs args)
    {
        if (!Disabled)
        {
            await Click.InvokeAsync(null);
        }
    }

    /// <summary>
    /// Closes this instance popup.
    /// </summary>
    public void Close()
    {
        JSRuntime.InvokeVoidAsync("Radzen.closePopup", PopupID);
    }

    /// <summary>
    /// Gets the popup identifier.
    /// </summary>
    /// <value>The popup identifier.</value>
    private string PopupID
    {
        get { return $"popup{UniqueID}"; }
    }

    private string getButtonCss()
    {
        return $"rz-button {(Disabled ? " rz-state-disabled" : "")} amprover-splitbutton ampover-combined-button";
    }

    private string getPopupButtonCss()
    {
        return $"rz-splitbutton-menubutton rz-button rz-button-icon-only{(Disabled ? " rz-state-disabled" : "")} ampover-combined-button";
    }

    private string OpenPopupScript()
    {
        if (Disabled)
        {
            return string.Empty;
        }

        return $"Radzen.togglePopup(this.parentNode, '{PopupID}')";
    }

    /// <inheritdoc />
    protected override string GetComponentCssClass()
    {
        return Disabled ? "rz-splitbutton rz-buttonset rz-state-disabled" : "rz-splitbutton rz-buttonset";
    }

}