using AMprover.BusinessLogic.Models.Rams;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace AMprover.BlazorApp.Components.Rams;

public partial class RamsDiagramComponent
{
    [Inject] private IJSRuntime JSRuntime { get; set; }

    [Parameter] public double AvailableTime { get; set; }
        
    [Parameter] public RamsDiagramContentModel Diagram { get; set; }

    [Parameter] public RamsDisplayModel DisplayMode { get; set; }

    [Parameter] public string Title { get; set; }

    [Parameter] public EventCallback<RamsComponentModel> ComponentSelectCallback { get; set; }

    [Parameter] public EventCallback<RamsComponentModel> ComponentReOrderCallback { get; set; }

    [Parameter] public EventCallback ComponentCollapseCallback { get; set; }

    [Parameter] public EventCallback ComponentParallelCallback { get; set; }

    [Parameter] public EventCallback<NewContainerModel> AddItemsToNewContainerCallback { get; set; }

    [Parameter] public EventCallback DrawLines { get; set; }

    [Parameter] public EventCallback RemoveLines { get; set; }

    [Parameter] public EventCallback RecalculateDiagramSize { get; set; }

    [Parameter] public int? RamsAllowedDept { get; set; }
        
    [Parameter] public string Language { get; set; }

    public ElementReference RecursiveTable { get; set; }
}