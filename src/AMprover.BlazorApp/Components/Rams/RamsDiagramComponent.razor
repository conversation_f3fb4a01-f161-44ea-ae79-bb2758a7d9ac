<div class="rams-diagram rams-flex">
    <div class="row">
        <div class="col-sm-8">
            <h3>@Title</h3>
        </div>
        <div class="col-sm-4">
            <ZoomControls/>
        </div>
    </div>
    
    <div class="rams-layout">
        <div class="rams-diagram-container">
            <table @ref="@RecursiveTable">
                <tbody>
                <RamsComponent Class="flex-container"
                               Diagram=@Diagram
                               Items=@Diagram.Parts
                               IsRoot=@(true)
                               ItemWrapperClass=@(_ => "flex-item")
                               ComponentsReOrderCallback=@ComponentReOrderCallback
                               AddItemsToNewContainerCallback=@AddItemsToNewContainerCallback>
                    <ChildContent Context="item">
                        <RamsDiagramRecursiveComponent AvailableTime=@AvailableTime Language=@Language
                                                       Diagram=@Diagram Item=@item DisplayMode=@DisplayMode
                                                       ComponentCollapseCallback="ComponentCollapseCallback"
                                                       ComponentParallelCallback="ComponentParallelCallback"
                                                       ComponentSelectCallback="ComponentSelectCallback"
                                                       ComponentReOrderCallback="ComponentReOrderCallback"
                                                       AddItemsToNewContainerCallback="AddItemsToNewContainerCallback"
                                                       RecalculateDiagramSize=@RecalculateDiagramSize
                                                       RemoveLines=@RemoveLines
                                                       DrawLines=@DrawLines
                                                       RamsAllowedDept=@RamsAllowedDept>
                        </RamsDiagramRecursiveComponent>
                    </ChildContent>
                </RamsComponent>
                </tbody>
            </table>
        </div>
    </div>
</div>
