using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Models.Rams;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace AMprover.BlazorApp.Components.Rams;

public partial class RamsComponent<TItem>
{
    #region Parameters

    [Parameter] public EventCallback<RamsComponentModel> ComponentsReOrderCallback { get; set; }

    [Parameter] public bool IsRoot { get; set; }

    [Parameter] public EventCallback<NewContainerModel> AddItemsToNewContainerCallback { get; set; }

    [Parameter] public EventCallback RecalculateDiagramSize { get; set; }

    [Parameter] public EventCallback RemoveLines { get; set; }

    [Parameter] public EventCallback DrawLines { get; set; }

    /// <summary>
    /// Allows to pass a delegate which executes if something is dropped and decides if the item is accepted
    /// </summary>
    [Parameter]
    public Func<TItem, TItem, bool> Accepts { get; set; }

    /// <summary>
    /// Allows to pass a delegate which executes if something is dropped and decides if the item is accepted
    /// </summary>
    [Parameter]
    public Func<TItem, bool> AllowsDrag { get; set; }

    /// <summary>
    /// Allows to pass a delegate which executes if a drag operation ends
    /// </summary>
    [Parameter]
    public Action<TItem> DragEnd { get; set; }

    /// <summary>
    /// Raises a callback with the dropped item as parameter in case the item can not be dropped due to the given Accept Delegate
    /// </summary>
    [Parameter]
    public EventCallback<TItem> OnItemDropRejected { get; set; }

    /// <summary>
    /// Raises a callback with the dropped item as parameter
    /// </summary>
    [Parameter]
    public EventCallback<TItem> OnItemDrop { get; set; }

    /// <summary>
    /// Raises a callback with the replaced item as parameter
    /// </summary>
    [Parameter]
    public EventCallback<TItem> OnReplacedItemDrop { get; set; }

    /// <summary>
    /// List of items for the dropzone
    /// </summary>
    [Parameter]
    public IList<TItem> Items { get; set; }

    /// <summary>
    /// Raises a callback with the dropped item as parameter in case the item can not be dropped due to item limit.
    /// </summary>
    [Parameter]
    public EventCallback<TItem> OnItemDropRejectedByMaxItemLimit { get; set; }

    [Parameter] public RenderFragment<TItem> ChildContent { get; set; }

    /// <summary>
    /// Specifies one or more classnames for the Dropzone element.
    /// </summary>
    [Parameter]
    public string Class { get; set; }

    /// <summary>
    /// Specifies the id for the Dropzone element.
    /// </summary>
    [Parameter]
    public string Id { get; set; }

    /// <summary>
    /// Allows to pass a delegate which specifies one or more classnames for the draggable div that wraps your elements.
    /// </summary>
    [Parameter]
    public Func<TItem, string> ItemWrapperClass { get; set; }

    /// <summary>
    /// If set items dropped are copied to this dropzone and are not removed from their source.
    /// </summary>
    [Parameter]
    public Func<TItem, TItem> CopyItem { get; set; }

    /// <summary>
    /// Additional custom content
    /// </summary>
    [Parameter]
    public RenderFragment Footer { get; set; }

    /// <summary>
    /// Parallel tracks
    /// </summary>
    [Parameter]
    public int? ParallelTracks { get; set; }

    [Parameter] public RamsDiagramContentModel Diagram { get; set; }

    [Parameter] public int? RamsAllowedDept { get; set; }

    #endregion

    #region Drop Methods

    private async Task OnDropItemOnSpacing(int track, int column, Guid group)
    {
        bool groupChange = false;

        if (!IsDropAllowed())
        {
            DragAndDropService.Reset();
            return;
        }

        if (DragAndDropService.ActiveItem is not RamsComponentModel activeItem) return;

        if (group == activeItem.Id)
            return;

        activeItem.ParallelTrack = track;
        activeItem.Changed = DateTime.Now;

        if (group != Guid.Empty)
        {
            groupChange = activeItem.GroupId != group;
            activeItem.GroupId = group;
        }

        activeItem.Order = column;
        DragAndDropService.Reset();

        await ComponentsReOrderCallback.InvokeAsync(groupChange ? activeItem : null);
        StateHasChanged();

        await RemoveLines.InvokeAsync();
        await RecalculateDiagramSize.InvokeAsync();
        await DrawLines.InvokeAsync();
    }

    private async Task OnDropNestedContainer()
    {
        DragAndDropService.ShouldRender = true;
        if (!IsDropAllowed())
        {
            DragAndDropService.Reset();
            return;
        }

        if (DragAndDropService.DragTargetItem == null)
            return;

        if (DragAndDropService.ActiveItem is not RamsComponentModel activeItem) return;
        if (DragAndDropService.DragTargetItem is not RamsComponentModel dropItem) return;
        
        //create new container and nest active and target item in container
        var originalOrder = dropItem.Order;
        dropItem.Order = 1;
        activeItem.Order = 1;
        activeItem.ParallelTrack = dropItem.ParallelTrack + 1;

        await AddItemsToNewContainerCallback.InvokeAsync(new NewContainerModel
        {
            OriginalOrder = originalOrder,
            DropItem = dropItem,
            ComponentsToMove = [activeItem.Id, dropItem.Id]
        });

        DragAndDropService.Reset();
        StateHasChanged();

        await RemoveLines.InvokeAsync();
        await RecalculateDiagramSize.InvokeAsync();
        await DrawLines.InvokeAsync();
    }

    private async Task OnDrop()
    {
        DragAndDropService.ShouldRender = true;
        if (!IsDropAllowed())
        {
            DragAndDropService.Reset();
            return;
        }

        if (DragAndDropService.DragTargetItem == null)
            return;

        if (DragAndDropService.ActiveItem is not RamsComponentModel activeItem) return;
        if (DragAndDropService.DragTargetItem is not RamsComponentModel dropItem) return;

        if (dropItem.Type == RamsComponentType.Container)
        {
            var template = dropItem.Parts.Any() ? dropItem.Parts.MaxBy(x => x.Order) : null;
            activeItem.Changed = DateTime.Now;
            if (template != null)
            {
                activeItem.Order = template.Order + 1;
                activeItem.ParallelTrack = template.ParallelTrack ?? 1;
            }
            else
            {
                activeItem.Order = 0;
                activeItem.ParallelTrack = 1;
            }

            activeItem.GroupId = dropItem.Id;

            await ComponentsReOrderCallback.InvokeAsync(activeItem);
        }
        else
        {
            activeItem.Order = dropItem.Order;
            dropItem.Order += 1;

            activeItem.Changed = DateTime.Now;
            activeItem.ParallelTrack = dropItem.ParallelTrack;

            var originalGroupId = activeItem.GroupId;
            activeItem.GroupId = dropItem.GroupId;

            //If item has been moved to different group
            await ComponentsReOrderCallback.InvokeAsync(originalGroupId != activeItem.GroupId ? activeItem : null);
        }

        DragAndDropService.Reset();
        StateHasChanged();

        await RemoveLines.InvokeAsync();
        await RecalculateDiagramSize.InvokeAsync();
        await DrawLines.InvokeAsync();
    }

    private bool IsDropAllowed()
    {
        var activeItem = DragAndDropService.ActiveItem;
        if (!IsValidItem())
        {
            return false;
        }

        if (IsItemAccepted(DragAndDropService.DragTargetItem)) return true;
        OnItemDropRejected.InvokeAsync(activeItem);
        return false;
    }

    #endregion

    #region Drag Methods

    private string CheckIfDraggable(TItem item)
    {
        if (AllowsDrag == null)
            return "";
        if (item == null)
            return "";
        return AllowsDrag(item) ? "" : "rams-dd-noselect";
    }

    private string CheckIfDragOperationIsInProgress()
    {
        var activeItem = DragAndDropService.ActiveItem;
        return activeItem == null ? "" : "rams-dd-inprogress";
    }

    private void OnDragEnd()
    {
        DragEnd?.Invoke(DragAndDropService.ActiveItem);
        ComponentsReOrderCallback.InvokeAsync();
        DragAndDropService.Reset();
    }

    private async Task OnDragEnter(TItem item)
    {
        if (item != null && item is RamsComponentModel model)
        {
            await JsRuntime.InvokeVoidAsync("removeRamsDraggedOverClassExceptID", model.Id);

            if (model.GroupId == null)
                return;
            
            var activeItem = DragAndDropService.ActiveItem;
            DragAndDropService.DragTargetColumn = null;
            DragAndDropService.DragTargetGroup = null;
            DragAndDropService.DragTargetTrack = null;

            if (model.Equals(activeItem) || !IsValidItem() || !IsItemAccepted(item) || IsAboveDeptSetting(item))
            {
                DragAndDropService.DragTargetItem = default;
                return;
            }

            DragAndDropService.DragTargetItem = item;

            DragAndDropService.ShouldRender = true;
            StateHasChanged();
            DragAndDropService.ShouldRender = false;
        }
    }

    private void OnDragEnterSpacing(int trackId, int columnId, Guid groupId)
    {
        var activeItem = DragAndDropService.ActiveItem;

        if (activeItem is not RamsComponentModel)
            return;

        DragAndDropService.DragTargetColumn = columnId;
        DragAndDropService.DragTargetGroup = groupId != Guid.Empty ? groupId : null;
        DragAndDropService.DragTargetTrack = trackId;
        DragAndDropService.DragTargetItem = default;

        DragAndDropService.ShouldRender = true;
        StateHasChanged();
        DragAndDropService.ShouldRender = false;
    }

    private void OnDragStart(TItem item)
    {
        DragAndDropService.ShouldRender = true;
        DragAndDropService.ActiveItem = item;
        DragAndDropService.Items = Items;
        StateHasChanged();
        DragAndDropService.ShouldRender = false;
    }

    #endregion

    protected override bool ShouldRender()
    {
        return DragAndDropService.ShouldRender;
    }

    private void ForceRender(object sender, EventArgs e)
    {
        StateHasChanged();
    }

    protected override void OnInitialized()
    {
        DragAndDropService.StateHasChanged += ForceRender;
        base.OnInitialized();
    }

    public void Dispose()
    {
        DragAndDropService.StateHasChanged -= ForceRender;
    }

    #region Item support methods

    private string IsItemDraggable(TItem item)
    {
        if (AllowsDrag == null && !IsRoot)
            return "true";

        return item == null || AllowsDrag == null ? "false" : AllowsDrag(item).ToString();
    }

    private bool IsItemAccepted(TItem dragTargetItem)
    {
        var accepted = Accepts == null || Accepts(DragAndDropService.ActiveItem, dragTargetItem);
        return accepted;
    }

    private bool IsAboveDeptSetting(TItem dragTargetItem)
    {
        if (RamsAllowedDept == null)
        {
            return false;
        }

        //If drop will surpass the dept limit set do not execute this action
        var outcome = dragTargetItem is RamsComponentModel model && model.Dept >= RamsAllowedDept.Value;
        return outcome;
    }

    private bool IsValidItem()
    {
        return DragAndDropService.ActiveItem != null;
    }

    private string CheckIfItemIsInTransit(TItem item)
    {
        return item.Equals(DragAndDropService.ActiveItem) ? "rams-dd-in-transit no-pointer-events" : "";
    }

    private string CheckIfItemIsDragTarget(TItem item)
    {
        if (item.Equals(DragAndDropService.ActiveItem))
            return "";
        if (item is RamsComponentModel model && model.Equals(DragAndDropService.DragTargetItem))
        {
            return IsItemAccepted(DragAndDropService.DragTargetItem)
                ? "rams-dd-dragged-over"
                : "rams-dd-dragged-over-denied";
        }

        return "";
    }

    private string CheckIfSpacerIsDragTarget(TItem item)
    {
        if (item.Equals(DragAndDropService.DragTargetItem))
        {
            return IsItemAccepted(DragAndDropService.DragTargetItem)
                ? "rams-dd-dragged-over"
                : "rams-dd-dragged-over-denied";
        }

        return "";
    }

    #endregion

    #region Styling support methods

    private string GetClassesForDraggable(TItem item)
    {
        var builder = new StringBuilder();
        builder.Append("rams-dd-draggable");
        if (ItemWrapperClass == null) return builder.ToString();
        var itemWrapperClass = ItemWrapperClass(item);
        builder.AppendLine(" " + itemWrapperClass);

        return builder.ToString();
    }

    private string GetClassesForDropzone(RamsComponentModel model = null)
    {
        var builder = new StringBuilder();

        if (model == null || model.Type == RamsComponentType.Block)
        {
            builder.Append("rams-dd-dropzone");
            if (!string.IsNullOrEmpty(Class))
            {
                builder.AppendLine(" " + Class);
            }
        }
        else
        {
            if (!string.IsNullOrEmpty(Class))
            {
                builder.AppendLine(Class);
            }
        }

        return builder.ToString();
    }

    private static string GetClassesForBlockMargins(RamsComponentModel current = null,
        RamsComponentModel previous = null,
        RamsComponentModel next = null)
    {
        var builder = new StringBuilder();

        if (current?.Order > 1 && previous == null)
        {
            builder.Append(" rams-dd-dropzone-padding-previous");
        }

        if (next == null)
        {
            builder.Append(" rams-dd-dropzone-padding-next");
        }

        return builder.ToString();
    }

    private string GetClassesForSpacing(uint spacerId, bool empty, bool top, bool initial)
    {
        var builder = new StringBuilder();
        builder.Append("rams-dd-spacing");
        //if active space id and item is from another dropzone -> always create insert space
        if (DragAndDropService.ActiveSpacerId == spacerId && Items.IndexOf(DragAndDropService.ActiveItem) == -1)
        {
            builder.Append(" rams-dd-spacing-dragged-over");
        }
        // else -> check if active space id and that it is an item that needs space
        else if (DragAndDropService.ActiveSpacerId == spacerId &&
                 spacerId != Items.IndexOf(DragAndDropService.ActiveItem) &&
                 spacerId != Items.IndexOf(DragAndDropService.ActiveItem) + 1)
        {
            builder.Append(" rams-dd-spacing-dragged-over");
        }

        if (!empty)
        {
            builder.Append(" no-width");
            return builder.ToString();
        }

        if (initial) builder.Append(top ? " rams-dd-spacing-full-width-top" : " rams-dd-spacing-full-width-bottom");
        return builder.ToString();
    }

    private string GetClassesForEmpty(int trackId, int columnId, Guid groupId)
    {
        var builder = new StringBuilder();

        if (trackId == DragAndDropService.DragTargetTrack && columnId == DragAndDropService.DragTargetColumn)
        {
            builder.Append("rams-dd-dragged-over");
        }

        return builder.ToString();
    }

    #endregion
}