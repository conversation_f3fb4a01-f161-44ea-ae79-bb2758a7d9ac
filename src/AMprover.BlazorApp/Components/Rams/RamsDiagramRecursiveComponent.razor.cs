using AMprover.BlazorApp.Helpers;
using AMprover.BusinessLogic.Enums.Rams;
using AMprover.BusinessLogic.Models.Rams;
using Microsoft.AspNetCore.Components;
using Ra<PERSON>zen;
using System;

namespace AMprover.BlazorApp.Components.Rams;

public partial class RamsDiagramRecursiveComponent
{
    [Inject] private TooltipService TooltipService { get; set; }

    [Parameter] public RamsLinkType LinkType { get; set; }
    [Parameter] public RamsNodeType NodeType { get; set; }
    [Parameter] public RamsComponentModel Item { get; set; }
    [Parameter] public RamsDiagramContentModel Diagram { get; set; }
    [Parameter] public RamsDisplayModel DisplayMode { get; set; }
    [Parameter] public double AvailableTime { get; set; }
    [Parameter] public EventCallback<RamsComponentModel> ComponentSelectCallback { get; set; }
    [Parameter] public EventCallback<RamsComponentModel> ComponentReOrderCallback { get; set; }
    [Parameter] public EventCallback ComponentCollapseCallback { get; set; }
    [Parameter] public EventCallback ComponentParallelCallback { get; set; }
    [Parameter] public EventCallback<NewContainerModel> AddItemsToNewContainerCallback { get; set; }
    [Parameter] public EventCallback RecalculateDiagramSize { get; set; }
    [Parameter] public EventCallback RemoveLines { get; set; }
    [Parameter] public EventCallback DrawLines { get; set; }
    [Parameter] public int? RamsAllowedDept { get; set; }
    [Parameter] public string Language { get; set; }

    private Guid? SelectedId { get; set; }
    public ElementReference RecursiveTable { get; set; }
    private ElementReference ToolTip { get; set; }

    private void ShowTooltipWithHtml()
    {
        TooltipService.Open(ToolTip, GetCalculationsBasedOnDisplay(), new TooltipOptions { Position = TooltipPosition.Bottom, Duration = 2000 });
    }

    private RenderFragment<TooltipService> GetCalculationsBasedOnDisplay() => ds => builder =>
    {
        var concatenatedCalculations = string.Empty;

        if (Item.Results == null)
            return;

        // Lambda
        if (DisplayMode.ShowLambda)
        {
            if (DisplayMode.ScientificNotation)
                concatenatedCalculations += $"λ(t) = {Item.Results.LabdaFunctional:0.0#e+0}/y,";
            else
                concatenatedCalculations += $"λ(t) = {Math.Round(Item.Results.LabdaFunctional, 3)}/y,";
        }
        // MTBF
        else
        {
            if (DisplayMode.ShowFunctional)
            {
                if (Item.Results.MtbfFunctional > 10000)
                    concatenatedCalculations += "MTBF = ꝏ";
                else
                    concatenatedCalculations += $"MTBF = {RamsDisplayHelper.FormatRoundedValue(Item.Results.MtbfFunctional, DisplayMode.ShowYears, AvailableTime, Language, 2)}{RamsDisplayHelper.FormatUnit(DisplayMode.ShowYears)},";
            }
            else
            {
                if (Item.Results.MtbfTechnical > 10000)
                    concatenatedCalculations += "MTBF = ꝏ";
                else
                    concatenatedCalculations += $"MTBF = {RamsDisplayHelper.FormatRoundedValue(Item.Results.MtbfTechnical, DisplayMode.ShowYears, AvailableTime, Language, 2)}{RamsDisplayHelper.FormatUnit(DisplayMode.ShowYears)},";
            }
        }

        // Reliability
        if (DisplayMode.ScientificNotation)
        {
            if (DisplayMode.ShowFunctional)
                concatenatedCalculations += $" R(t) = {Item.Results.ReliabilityFunctional:0.0#e+0}%,";
            else
                concatenatedCalculations += $" R(t) = {Item.Results.ReliabilityTechnical:0.0#e+0}%,";
        }
        else
        {
            if (DisplayMode.ShowFunctional)
                concatenatedCalculations += $" R(t) = {(Item.Results.ReliabilityFunctional * 100).FormatRoundedValue(Language, 2)}%,";
            else
                concatenatedCalculations += $" R(t) = {(Item.Results.ReliabilityTechnical * 100).FormatRoundedValue(Language, 2)}%,";
        }

        // Availability
        if (Item.Results.AffectedBufferTime > 0)
        {
            if (DisplayMode.ScientificNotation)
            {
                if (DisplayMode.ShowFunctional)
                    concatenatedCalculations += $" <em>A = {Item.Results.AvailFunctional:0.0#e+0}%</em>,";
                else
                    concatenatedCalculations += $" <em>A = {Item.Results.AvailTechnical:0.0#e+0}%</em>,";
            }
            else
            {
                if (DisplayMode.ShowFunctional)
                    concatenatedCalculations += $" <em>A = {(Item.Results.AvailFunctional * 100).FormatRoundedValue(Language, 2)}%</em>,";
                else
                    concatenatedCalculations += $" <em>A = {(Item.Results.AvailTechnical * 100).FormatRoundedValue(Language, 2)}%</em>,";
            }
        }
        else
        {
            if (DisplayMode.ScientificNotation)
            {
                if (DisplayMode.ShowFunctional)
                    concatenatedCalculations += $" A = {Item.Results.AvailFunctional:0.0#e+0}%,";
                else
                    concatenatedCalculations += $" A = {Item.Results.AvailTechnical:0.0#e+0}%,";
            }
            else
            {
                if (DisplayMode.ShowFunctional)
                    concatenatedCalculations += $" A = {(Item.Results.AvailFunctional * 100).FormatRoundedValue(Language, 2)}%,";
                else
                    concatenatedCalculations += $" A = {(Item.Results.AvailTechnical * 100).FormatRoundedValue(Language, 2)}%,";
            }
        }

        // PFD or SIL
        if (DisplayMode.ShowPfd)
        {
            concatenatedCalculations += $" PFD = {Math.Round(Item.Results.PFD, 3)}";
        }
        else if (DisplayMode.ShowSil)
        {
            concatenatedCalculations += $"SIL = {Item.Results.RamsSil ?? "N/A"}";
        }

        builder.OpenElement(0, "div");
        builder.AddMarkupContent(1, concatenatedCalculations.TrimEnd(','));
        builder.CloseElement();
    };

    private void SelectItem(RamsComponentModel component)
    {
        SelectedId = SelectedId == component.Id ? null : component.Id;
        StateHasChanged();
        ComponentSelectCallback.InvokeAsync(component);
    }

    private void CollapseItem(RamsComponentModel component)
    {
        component.Collapsed = !component.Collapsed;
        StateHasChanged();
        ComponentCollapseCallback.InvokeAsync();
    }
}