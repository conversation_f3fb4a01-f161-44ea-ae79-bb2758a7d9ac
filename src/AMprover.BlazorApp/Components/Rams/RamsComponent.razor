@typeparam TItem
@inject DragAndDropService<TItem> DragAndDropService
@using AMprover.BusinessLogic.Enums.Rams
@implements IDisposable
@inject IJSRuntime JsRuntime

@if (Items.Any())
{
    var differentTracks = ((List<RamsComponentModel>)Items).DistinctBy(x => x.ParallelTrack)
        .OrderBy(x => x.ParallelTrack)
        .Select(x => x.ParallelTrack).ToList();

    var items = ((List<RamsComponentModel>)Items)
        .OrderBy(x => x.ParallelTrack)
        .ThenBy(x => x.Order)
        .GroupBy(x => x.ParallelTrack)
        .Select((x, i) => new { index = i, component = x }).ToList();

    var maxColumns = ((List<RamsComponentModel>)Items).DistinctBy(x => x.Order).Count();
    var groupIndex = ((List<RamsComponentModel>)Items).FirstOrDefault()?.GroupId ?? Guid.Empty;

    //Only show this for nested containers, not the main container
    @if (!IsRoot)
    {
        <tr class="rams-dd-dropzone-parallel rams-top" style="display: flex;">

            @for (var i = 0; i < maxColumns * 2 + 1; i++)
            {
                var tempColumn = i;
                <td column-iterator=@tempColumn>
                    <div id=@Id style="width:100%; height: 100%;"
                    @ondragover:preventDefault
                    @ondragover=@(() => { })
                    @ondragenter:preventDefault
                    @ondragenter=@(() => { })
                         ondragstart="event.dataTransfer.setData('text', event.target.id);"
                    @ondragenter:stopPropagation
                    @ondragend:stopPropagation
                    @ondragover:stopPropagation
                    @ondragleave:stopPropagation
                    @ondragstart:stopPropagation>

                        @if (i % 2 != 0)
                        {
                            <div @ondrop=@(() => OnDropItemOnSpacing((differentTracks.FirstOrDefault() ?? 1) - 1, tempColumn, groupIndex))
                                 @ondragenter=@(() => OnDragEnterSpacing((differentTracks.FirstOrDefault() ?? 1) - 1, tempColumn, groupIndex))
                                 class="@GetClassesForSpacing(0, true, true, true) @GetClassesForEmpty((differentTracks.FirstOrDefault() ?? 1) - 1, tempColumn, groupIndex)">
                            </div>
                        }
                    </div>
                </td>
            }
        </tr>
    }

    @* First group by column. Each column then has the same amount of rows. *@
    @foreach (var group in items)
    {
        var trackIndex = group.component.FirstOrDefault()?.ParallelTrack ?? 1;

        <tr style="display:flex;">
            @for (var i = 0; i < maxColumns; i++)
            {
                var column = group.component.FirstOrDefault(x => x.Order == i + 1);

                if (column != null && column.Order == i + 1)
                {
                    var item = Items.FirstOrDefault(x => x.Equals(column));
                    var trackItem = group.component.FirstOrDefault(x => x.Equals(item));

                    //Not root and previous column also contains block
                    @if (!IsRoot)
                    {
                        <td style="position: relative;display: flex;" class="spacing-before" column-iterator=@i>
                            @* // Spacing to allow block to be placed in front of other *@
                            <div id=@Id style="width: 25px; margin-left: 5px; margin-right: 5px; min-height: 90px; display: flex;align-items: center"
                            @ondragover:preventDefault
                            @ondragover=@(() => { })
                            @ondragenter:preventDefault
                            @ondragenter=@(() => { })
                            @ondragenter:stopPropagation
                            @ondragend:stopPropagation
                            @ondragover:stopPropagation
                            @ondragleave:stopPropagation
                            @ondragstart:stopPropagation>
                                <div style="min-height: 90px; width: 25px" @ondragenter=@(() => OnDragEnterSpacing(trackItem?.ParallelTrack ?? 1, trackItem?.Order - 1 ?? 1, trackItem?.GroupId ?? Guid.Empty))
                                     @ondrop=@(() => OnDropItemOnSpacing(trackItem?.ParallelTrack ?? 1, trackItem?.Order ?? 1, trackItem?.GroupId ?? Guid.Empty))
                                     class="@GetClassesForSpacing(0, true, true, true) @GetClassesForEmpty(trackItem?.ParallelTrack ?? 1, trackItem?.Order - 1 ?? 1, trackItem?.GroupId ?? Guid.Empty)">
                                </div>
                            </div>
                        </td>
                    }

                    <td style="position: relative; display: flex; justify-content: center; align-items: center;" class="rams-block-item" column-iterator=@i>
                        <div id=@Id class=@GetClassesForDropzone(trackItem)
                        @ondragover:preventDefault
                        @ondragover=@(() => { })
                        @ondragover:stopPropagation
                        @ondragenter:preventDefault
                        @ondragenter:stopPropagation
                        @ondragenter=@(() => { })
                        @ondrop=@OnDropNestedContainer
                             ondragstart="event.dataTransfer.setData('text', event.target.id);"
                        @ondragstart:stopPropagation
                        @ondragend:stopPropagation
                        @ondragleave:stopPropagation>

                            <div id=@trackItem?.Id
                                 draggable=@(IsItemDraggable(item))
                                 @ondragstart=@(() => OnDragStart(item))
                                 @ondragend=@OnDragEnd
                                 @ondragenter=@(() => OnDragEnter(item))
                                 parallel-track=@(trackItem?.ParallelTrack ?? 1)
                                 class="@GetClassesForDraggable(item) @CheckIfItemIsInTransit(item) @CheckIfItemIsDragTarget(item) @CheckIfDragOperationIsInProgress() @CheckIfDraggable(item) order-@(trackItem?.Order) @(trackItem.Type == RamsComponentType.Container ? "type-container" : "type-block")">

                                @{
                                    if (ChildContent != null)
                                    {
                                        @ChildContent(item)
                                    }
                                }

                            </div>

                            @Footer
                        </div>
                    </td>

                    @if (column.Order == maxColumns && !IsRoot)
                    {
                        <td style="position: relative;display: flex;" class="spacing-after" column-iterator=@i>
                            @* // Spacing to allow block to be placed in front of other *@
                            <div id=@Id style="width: 25px;margin-left: 5px; margin-right:5px;min-height: 90px; display: flex;align-items: center"
                            @ondragover:preventDefault
                            @ondragover=@(() => { })
                            @ondragenter:preventDefault
                            @ondragenter=@(() => { })
                            @ondragenter:stopPropagation
                            @ondragend:stopPropagation
                            @ondragover:stopPropagation
                            @ondragleave:stopPropagation
                            @ondragstart:stopPropagation>

                                <div style="min-height: 90px; width: 25px;"
                                     @ondragenter=@(() => OnDragEnterSpacing(trackItem?.ParallelTrack ?? 1, trackItem?.Order + 1 ?? 1, trackItem?.GroupId ?? Guid.Empty))
                                     @ondrop=@(() => OnDropItemOnSpacing(trackItem?.ParallelTrack ?? 1, trackItem?.Order + 1 ?? 1, trackItem?.GroupId ?? Guid.Empty))
                                     class="@GetClassesForSpacing(0, true, true, true) @GetClassesForEmpty(trackItem?.ParallelTrack ?? 1, trackItem?.Order + 1 ?? 1, trackItem?.GroupId ?? Guid.Empty)">
                                </div>
                            </div>
                        </td>
                    }
                }
                else if (i < maxColumns)
                {
                    var columnIndex = i + 1;

                    <td style="display:flex;" column-iterator=@i>
                        <div id=@Id style="width: 25px;margin-left: 5px; margin-right:5px; min-height: 90px; display: flex; align-items: center;"
                        @ondragover:preventDefault
                        @ondragover=@(() => { })
                        @ondragenter:preventDefault
                        @ondragenter=@(() => { })
                        @ondragenter:stopPropagation
                        @ondragend:stopPropagation
                        @ondragover:stopPropagation
                        @ondragleave:stopPropagation
                        @ondragstart:stopPropagation>

                            <div style="width:25px;min-height:90px" @ondragenter=@(() => OnDragEnterSpacing(trackIndex, columnIndex, groupIndex))
                                 @ondrop=@(() => OnDropItemOnSpacing(trackIndex, columnIndex, groupIndex))
                                 class="@GetClassesForSpacing(0, true, true, false) @GetClassesForEmpty(trackIndex, columnIndex, groupIndex)">
                            </div>
                        </div>
                    </td>
                }
            }
        </tr>
    }

    //Only show this for nested containers, not the main container
    @if (!IsRoot)
    {
        <tr class="rams-dd-dropzone-parallel rams-bottom" style="display: flex;">
            @for (var i = 0; i < maxColumns * 2 + 1; i++)
            {
                var tempColumn = i;
                <td style="display: flex">
                    <div id=@Id style="width: 100%"
                    @ondragover:preventDefault
                    @ondragover=@(() => { })
                    @ondragenter:preventDefault
                    @ondragenter=@(() => { })
                    @ondragenter:stopPropagation
                    @ondragend:stopPropagation
                    @ondragover:stopPropagation
                    @ondragleave:stopPropagation
                    @ondragstart:stopPropagation>

                        @if (i % 2 != 0)
                        {
                            <div @ondragenter=@(() => OnDragEnterSpacing((differentTracks.LastOrDefault() ?? 1) + 1, tempColumn, groupIndex))
                                 @ondrop=@(() => OnDropItemOnSpacing((differentTracks.LastOrDefault() ?? 1) + 1, tempColumn, groupIndex))
                                 class="@GetClassesForSpacing(0, true, false, true) @GetClassesForEmpty((differentTracks.LastOrDefault() ?? 1) + 1, tempColumn, groupIndex)">
                            </div>
                        }
                    </div>
                </td>
            }
        </tr>
    }
}