﻿@using AMprover.BusinessLogic.Models.Sapa

<Radzen.Blazor.RadzenTemplateForm TItem=SapaDetailModel Data=@SapaDetail>
    <DataAnnotationsValidator/>
    <div class="row">              
        <div class="col-sm-3">
            <div class="sapa-tree-frame my-3 mr-3 pt-4 px-4 pb-3">
                <div>
                    <text class="sapa-large-bold"> @Localizer["SapaDetPortfolioTxt"]:</text><br />
                </div>
                <div>
                    <text class="right"> - @SapaDetail.Installation</text><br />
                </div>
                <div>
                    <text class="right"> &nbsp - @SapaDetail.System</text><br />
                </div>
                <div>
                    <text class="right"> &nbsp &nbsp - @SapaDetail.Component</text>
                </div>
            </div>
        </div>
        <div class="col-sm-5">
            <div class="my-0 mr-3 pt-4 px-4 pb-3">
                <div>
                    <text class="sapa-overview-header"> @SapaDetail.RiskName</text><br /><br />
                </div>
                <text class="bold"> @Localizer["SapaDetMotivation"]:</text>
                <div>
                    <text class="ml-2"> @SapaDetail.RiskDescription</text><br />
                </div>
                <text class="bold"> @Localizer["SapaDetFailureConsequences"]:</text>
                <div>
                    <text class="ml-2"> @SapaDetail.RiskFailureConsequence</text><br />
                </div>
                <text class="bold"> @Localizer["SapaDetFailureCause"]:</text>
                <div>
                    <text class="ml-2"> @SapaDetail.RiskFailureCause</text><br />
                </div>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="sapa-overview-frame my-3 mr-3 pt-4 px-4 pb-3">
                <div>
                    <text class="bold"> @Localizer["SapaDetValueRiskBefore"]:</text>
                    <text class="currency-bold"> @FormatAsSelectedCurrency(SapaDetail.RiskBefore)</text>
                </div><br />
                <div>
                    <text class="bold"> @Localizer["SapaDetRequested"]:</text>
                    <text class="currency-bold"> @FormatAsSelectedCurrency(SapaDetail.TotalCapexNeeded)</text>
                </div><br />
                <div>
                    <text class="bold"> @Localizer["SapaDetValueRiskAfter"]:</text>
                    <text class="currency-bold"> @FormatAsSelectedCurrency(SapaDetail.RiskAfter)</text>
                </div>
                <br /><br /><br />
                <text class="large-bold-right">
                    @Localizer["SapaDetIndex"]: &nbsp @FormatAsNumber(SapaDetail.RiskSapaIndex)
                </text>
            </div>
        </div>
    </div>

    <RadzenTabs>
        <Tabs>
            <RadzenTabsItem Text=@Localizer["SapaRiskMatricesLbl"]>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="rz-mx-4 sapa-large-bold">
                            @Localizer["SapaDetMatrixWithout"]: 
                        </div>
                        <div>
                            <img class="rz-mx-2 rz-my-5 sapa-risk-matrix" src="data:image/jpeg;base64,@Convert.ToBase64String(SapaDetail?.RiskMatrixBefore)" alt="MRB image" />
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="rz-mx-4 sapa-large-bold">
                            @Localizer["SapaDetMatrixWith"]:
                        </div>
                        <div>
                            <img class="rz-mx-2 rz-my-5 sapa-risk-matrix" src="data:image/jpeg;base64,@Convert.ToBase64String(SapaDetail?.RiskMatrixAfter)" alt="MRB image" />
                        </div>
                    </div>
                </div>
            </RadzenTabsItem>
             <RadzenTabsItem Text=@Localizer["SapaDetAttachmentTabTxt"]>
                <div class="row riskassessment-top-tabs">
                    <div class="col-sm-8">
                        <div style="max-height:250px;">
                            <AttachmentContainer ReadOnly="true" Data=@Risk.Attachments SaveCallback=@UpdateCurrentRisk ParentItem=@Risk />
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <text class="bold"> @Localizer["SapaDetRemark1"]:</text>
                        <div>
                            <text class="ml-2"> @SapaDetail.RiskRemark1</text><br />
                        </div>
                    </div>
                </div>
            </RadzenTabsItem>
        </Tabs>
    </RadzenTabs>
   <br/>
    <UtilityGrid TItem=TaskModel
                 @ref=PreventiveActionsGrid
                 Data=@Risk?.Tasks.ToList()
                 FileName=@GridNames.RiskEdit.PreventiveActions
                 Title=@Localizer["SapaDetDefinedActions"]
                 UseOpenTextInsteadOfEdit=true 
                 DropDownOverrides=@TaskModelDropDownOverrides
                 CssClass="neg-margin-large" />

    @if ((@Risk?.Spares.ToList()).Any())
    {
        <br />
        <UtilityGrid TItem=SpareModel
                     @ref=SparesGrid
                     Title=@Localizer["SapaDetDefinedSpares"]
                     Data=@Risk?.Spares.ToList()
                     FileName=@GridNames.RiskEdit.Spares
                     CssClass="neg-margin-large" />
    }
                 
    </Radzen.Blazor.RadzenTemplateForm>
