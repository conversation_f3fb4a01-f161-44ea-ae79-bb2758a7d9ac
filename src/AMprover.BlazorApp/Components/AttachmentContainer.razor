﻿@using AMprover.BusinessLogic.Models.Sapa
@inject IAttachmentManager AttachmentManager;
@inject IDropdownManager DropdownManager;

<div class="attachment-container">
    <div class="attachment-new-container">

        <h3 style="display:inline-block">Attachments</h3>
        @if (_editAttachment == null)
        {
            <RadzenButton Disabled="ReadOnly" Text="Add" Icon="add_circle_outline" Click="AddItem" Style="float:right;margin-right:35px;"/>
        }

    </div>

    @if (Data?.Count > 0)
    {
        <ul class="attachment-list">
            @foreach (var attachment in Data)
            {
                <li>
                    @if (attachment == _editAttachment)
                    {
                        <Radzen.Blazor.RadzenTemplateForm TItem="AttachmentModel" Data=@attachment Submit=@Save>

                            <div class="row">
                                <div class="col-12">
                                    <RadzenButton class="btn-centered mt-2 mr-2" Text="Save" Disabled="ReadOnly" Icon="Save" type="submit" ButtonStyle="ButtonStyle.Secondary" />
                                    <RadzenButton class="btn-centered mt-2 ml-1 mr-2" Text="Cancel" Icon="Cancel" ButtonStyle=ButtonStyle.Warning Click=@(() => CancelEditItem(attachment)) />
                                    <RadzenButton class="btn-centered mt-2 mb-4 ml-1 mr-2" Text="Delete" Disabled="ReadOnly" Icon="Delete" ButtonStyle=ButtonStyle.Danger Click=@(() => DeleteItem(attachment)) />
                                </div>
                                <div class="col-6">
                                    <AMproverTextBox Label="Title" Required=true @bind-Value=@attachment.Title />
                                </div>
                                <div class="col-6">
                                    <AMproverTextBox Label="Url" Required=true @bind-Value=@attachment.Uri />
                                </div>
                                <div class="col-6">
                                    <AMproverDictionaryDropdown Label="Category" Data=@Categories @bind-Value=@attachment.CatgoryId />
                                </div>
                                <div class="col-6">
                                    <AMproverTextBox Label="Description" @bind-Value=@attachment.Description />
                                </div>
                            </div>

                        </Radzen.Blazor.RadzenTemplateForm>
                    }
                    else
                    {
                        <div class="row">
                            <div class="col-4">
                                <RadzenButton Icon="Edit" class="mr-3" Click=@(() => EditItem(attachment)) />
                                <a title="@attachment.Description" href="@attachment.Uri" target="_blank">@attachment.Title</a>
                            </div>
                            <div class="col-3 pt-2">
                                <span><b>@GetCategory(attachment)</b></span>
                            </div>
                            <div class="col-5 pt-2">
                                <span>@attachment.Description</span>
                            </div>
                        </div>
                    }
                </li>
            }
        </ul>
    }
</div>

@code {

    [Parameter, EditorRequired] public List<AttachmentModel> Data { get; set; }

    [Parameter, EditorRequired] public EventCallback SaveCallback { get; set; }

    [Parameter, EditorRequired] public object ParentItem { get; set; }

    [Parameter, EditorRequired] public bool ReadOnly { get; set; }

    AttachmentModel _editAttachment = null;

    AttachmentModel _tempAttachment = null;

    Dictionary<int?, string> Categories { get; set; }

    protected override void OnInitialized()
    {
        Categories = DropdownManager.GetAttachmentCategoryDict().ToNullableDictionary();
        base.OnInitialized();
    }

    void EditItem(AttachmentModel attachment)
    {
        _tempAttachment = attachment.Copy();
        _editAttachment = attachment;
    }

    void CancelEditItem(AttachmentModel attachment)
    {
        if(_tempAttachment != null)
        {
            var index = Data.IndexOf(attachment);
            Data[index] = _tempAttachment;
            _tempAttachment = null;
        }
        else
        {
            Data.Remove(attachment);
        }

        _editAttachment = null;
    }

    void Save()
    {
        SaveCallback.InvokeAsync();
        _tempAttachment = null;
        _editAttachment = null;
    }

    void DeleteItem(AttachmentModel attachment)
    {
        Data.Remove(attachment);
        AttachmentManager.DeleteAttachment(attachment.Id);
        _tempAttachment = null;
        _editAttachment = null;
    }

    void AddItem()
    {
        _editAttachment = new AttachmentModel();
        Data.Insert(0, _editAttachment);

        if (ParentItem is RiskModel risk)
        {
            _editAttachment.RiskId = risk.Id;
        }
        else if (ParentItem is TaskModel task)
        {
            _editAttachment.TaskId = task.Id;
        }
        else if (ParentItem is RiskObjectModel riskObject)
        {
            _editAttachment.RiskObjectId = riskObject.Id;
        }
        else if (ParentItem is SapaModel sapa)
        {
            _editAttachment.SapaId = sapa.Id;
        }
        else
        {
            throw new InvalidOperationException($"AddItem has not been implemented for Type: {ParentItem.GetType()}");
        }
    }

    string GetCategory(AttachmentModel attachment)
    {
        Categories.TryGetValue(attachment?.CatgoryId ?? 0, out var categoryText);
        return categoryText;
    }
}
