﻿@typeparam TItem

<div class="treeview-container">
    <div class="treeview-content @CssClass" style="@GetStyle()">

        @if (Treeview?.Node?.Nodes != null && Treeview?.Node?.Nodes.Any() == true)
        {
            @foreach (var item in Treeview.Node.Nodes.Where(x => !x.Nodes.Any()))
            {
                if (!item.Hidden)
                {
                    <TreeNodeComponent Node=@item
                                       ParentNode=@Treeview.Node
                                       Tree=Treeview/>
                }
            }

            @foreach (var item in Treeview.Node.Nodes.Where(x => x.Nodes.Any()))
            {
                if (!item.Hidden)
                {
                    <TreeNodeComponent Node=@item
                                       ParentNode=@Treeview.Node
                                       Tree=Treeview/>
                }
            }
        }
    </div>
</div>