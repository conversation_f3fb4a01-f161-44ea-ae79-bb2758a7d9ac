using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using NPOI.SS.Formula.Functions;
using System.Collections.Generic;

namespace AMprover.BlazorApp.Components.Tree
{
    public partial class TreeComponent<TItem>
    {
        [Parameter] public TreeGeneric<TItem> Treeview { get; set; }

        [Parameter] public EventCallback<TreeNodeGeneric<TItem>> NodeClickCallback { get; set; }

        [Parameter] public EventCallback<TreeNodeGeneric<TItem>> NodeExpandCallback { get; set; }

        [Parameter] public EventCallback<TreeNodeGeneric<TItem>> NodeCollapseCallback { get; set; }

        [Parameter] public EventCallback<TItem> DeleteCallback { get; set; }

        [Parameter] public EventCallback<TItem> NewCallback { get; set; }

        [Parameter] public EventCallback<TItem> OpenCallback { get; set; }

        [Parameter] public EventCallback<(List<TItem>, TItem)> PasteCallback { get; set; }

        [Parameter] public EventCallback<List<TreeNodeGeneric<TItem>>> ChangeOrderCallback { get; set; }

        [Parameter] public int Height { get; set; }

        [Parameter] public string CssClass { get; set; }

        [Parameter] public bool HandleCutPasteInternally { get; set; }

        [Parameter] public bool CascadeDeleteParentsWithNoOtherChildren { get; set; }

        [Parameter] public bool ExpandNodeWhenSelected { get; set; } = true;

        protected override void OnParametersSet()
        {
            base.OnParametersSet();

            Treeview.NodeClickCallback = NodeClickCallback;
            Treeview.NodeExpandCallback = NodeExpandCallback;
            Treeview.NodeCollapseCallback = NodeCollapseCallback;
            Treeview.DeleteCallback = DeleteCallback;
            Treeview.NewCallback = NewCallback;
            Treeview.OpenCallback = OpenCallback;
            Treeview.PasteCallback = PasteCallback;
            Treeview.HandleCutPasteInternally = HandleCutPasteInternally;
            Treeview.CascadeDeleteParentsWithNoOtherChildren = CascadeDeleteParentsWithNoOtherChildren;
            Treeview.ExpandNodeWhenSelected = ExpandNodeWhenSelected;
            Treeview.SetSelectedNode = EventCallback.Factory.Create<object>(this, SetSelectedNode);
            Treeview.ChangeOrderCallback = ChangeOrderCallback;
            Treeview.GenerateContextMenuList();
        }

        private string GetStyle()
        {
            return Height <= 0
                ? ""
                : $"height:{Height}px;";
        }

        private void SetSelectedNode(object newSelected)
        {
            Treeview.SelectNode((TreeNodeGeneric<TItem>)newSelected);
        }
    }
}
