using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.ContextMenu;
using AMprover.BusinessLogic.Models.Tree;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Radzen;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Components.Tree
{
    public partial class TreeNodeComponent<TItem>
    {
        [Inject] protected IGlobalDataService GlobalDataService { get; set; }
        [Inject] protected ContextMenuService ContextMenuService { get; set; }
        [Inject] protected DialogService DialogService { get; set; }
        [Inject] protected IClipboardManager ClipboardManager { get; set; }

        [Parameter] public int Depth { get; set; }
        [Parameter] public TreeGeneric<TItem> Tree { get; set; }
        [Parameter] public TreeNodeGeneric<TItem> Node { get; set; }
        [Parameter] public TreeNodeGeneric<TItem> ParentNode { get; set; }
        [Parameter] public FilteredState FilteredState { get; set; }
        [Parameter] public bool Interactive { get; set; } = true;

        protected override void OnInitialized()
        {
            Node.Parent = ParentNode;
            base.OnInitialized();
        }

        private void OnContextMenuItemClicked(MenuItemEventArgs args)
        {
            ContextMenuItemCLickedInternal((ContextMenuItemType)args.Value);
            ContextMenuService.Close();
        }

        private void ContextMenuItemCLickedInternal(ContextMenuItemType itemType)
        {
            switch (itemType)
            {
                case ContextMenuItemType.Delete:
                    if (Tree.DeleteCallback.HasDelegate)
                    {
                        var flattenedNodes = Node.Flatten(x => x.Nodes).Select(x => x.Source).ToList();

                        if (flattenedNodes.Count > 1)
                        {
                            DialogService.Open<AreYouSureDialog<TItem>>($"WARNING! deleting multiple items.",
                                new Dictionary<string, object>
                                {
                                    { nameof(AreYouSureDialog<TItem>.Items), flattenedNodes.Select(x => x.ToString()).ToList() },
                                    { nameof(AreYouSureDialog<TItem>.YesCallback), EventCallback.Factory.Create<TItem>(this, ConfirmDelete) }
                                });
                        }
                        else
                        {
                            DialogService.Open<AreYouSureDialog<TItem>>($"Are you sure?",
                                new Dictionary<string, object>
                                {
                                    { nameof(AreYouSureDialog<TItem>.Item), Node.Source },
                                    { nameof(AreYouSureDialog<TItem>.YesCallback), EventCallback.Factory.Create<TItem>(this, ConfirmDelete) }
                                });
                        }
                    }
                    break;

                case ContextMenuItemType.Copy or
                     ContextMenuItemType.Cut:
                    ClipboardManager.CopyItem(Node);
                    break;

                case ContextMenuItemType.Paste:
                    var (item, _) = ClipboardManager.GetCopiedItem<TreeNodeGeneric<TItem>>();
                    if (item != null)
                    {
                        if (Tree.HandleCutPasteInternally)
                        {
                            var parent = Node.Parent;
                            while (parent != null)
                            {
                                if (parent == item)
                                {
                                    DialogService.Open<InformationDialog>("Not possible", new Dictionary<string, object> {
                                        { "DialogContent", "You can not paste a Tree Node onto one of it's descendants." }
                                    });
                                    return;
                                }
                                parent = parent.Parent;
                            }
                            item.Parent.Nodes.Remove(item);
                            Node.Nodes.Add(item);
                            ClipboardManager.ClearCopiedItem<TreeNodeGeneric<TItem>>();
                        }

                        var nodesAsList = item.Flatten(n => n.Nodes).Select(n => n.Source).ToList();
                        if (Tree.PasteCallback.HasDelegate)
                            Tree.PasteCallback.InvokeAsync((nodesAsList, Node.Source));
                    }
                    else
                    {
                        DialogService.Open<InformationDialog>("No item copied", new Dictionary<string, object> {
                            { "DialogContent", "You should first copy an Item of the same type before you can paste it." }
                        });
                    }
                    break;

                case ContextMenuItemType.New:
                    Tree.NewCallback.InvokeAsync(Node.Source);
                    break;

                case ContextMenuItemType.Open:
                    Tree.OpenCallback.InvokeAsync(Node.Source);
                    break;
                case ContextMenuItemType.MoveDown:
                    MoveDown();
                    break;

                case ContextMenuItemType.MoveUp:
                    MoveUp();
                    break;
            }
        }

        private void MoveDown()
        {
            var index = Node.Parent.Nodes.IndexOf(Node);
            MoveNodeInternal(index, index + 1);
        }

        private void MoveUp()
        {
            var index = Node.Parent.Nodes.IndexOf(Node);
            MoveNodeInternal(index - 1, index);
        }

        private void MoveNodeInternal(int firstIndex, int nextIndex)
        {
            var nodes = Node.Parent.Nodes;
            if (firstIndex < 0 || nextIndex >= nodes.Count) return;
            (nodes[firstIndex], nodes[nextIndex]) = (nodes[nextIndex], nodes[firstIndex]);

            for (var i = 0; i < nodes.Count; i++)
            {
                nodes[i].SortOrder = i;
            }

            Tree.ChangeOrderCallback.InvokeAsync(nodes);
        }

        private void ConfirmDelete(TItem node)
        {
            ParentNode.Nodes.Remove(Node);

            if (Tree.CascadeDeleteParentsWithNoOtherChildren)
            {
                var nodeToCheck = Node.Parent;
                while (true)
                {
                    if (!nodeToCheck.Nodes.Any())
                    {
                        if (nodeToCheck.Parent == null)
                            break;

                        nodeToCheck.Parent.Nodes.Remove(nodeToCheck);
                        nodeToCheck = nodeToCheck.Parent;
                        continue;
                    }

                    break;
                }
            }

            var flattenedNodes = Node.Flatten(x => x.Nodes).Select(x => x.Source).Reverse().ToList();
            foreach (var n in flattenedNodes)
            {
                Tree.DeleteCallback.InvokeAsync(n);
            }
        }

        private void ExpandButtonClick()
        {
            if (!Node.Nodes.Any())
                return;

            Node.Open = !Node.Open;

            if (Node.Open)
                Tree.NodeExpandCallback.InvokeAsync(Node);
            else
                Tree.NodeCollapseCallback.InvokeAsync(Node);
        }

        private async Task ClickItem()
        {
            Node.IsSelected = true;
            await Tree.SetSelectedNode.InvokeAsync(Node);
            await Tree.NodeClickCallback.InvokeAsync(Node);
        }

        private string GetRowClass() => Node.IsSelected
            ? "treeview-row-selected noselect"
            : "treeview-row noselect";

        private string GetRowStyle() => $"margin-left:{10 * Depth}px;";

        private string GetFilteredStyle()
        {
            return Node.FilteredState switch
            {
                FilteredState.Self => "color:green;",
                FilteredState.Inherited => "color:blue;",
                _ => "display:none;"
            };
        }

        private void ShowContextMenuWithItems(MouseEventArgs args)
        {
            if (Interactive && GlobalDataService.CanEdit)
            {
                RenderFragment<ContextMenuService> menuFragment = _ =>
                {
                    return builder =>
                    {
                        // Open Radzen Menu component
                        builder.OpenComponent<RadzenMenu>(0);
                        builder.AddAttribute(1, "Click", EventCallback.Factory.Create<MenuItemEventArgs>(this, OnContextMenuItemClicked));

                        // Add content for Radzen Menu (the menu items)
                        builder.AddAttribute(2, "ChildContent", (RenderFragment)(menuBuilder =>
                        {
                            var sequence = 0;
                            foreach (var menuItem in Tree.ContextMenuItems)
                            {
                                // Move Up and MoveDown is currently only Implemented for Risks.
                                // We don't want to show these items elsewhere in the tree.
                                // Possibly come up with a better way of having a context specific menu.
                                if (menuItem.Type is ContextMenuItemType.MoveUp or ContextMenuItemType.MoveDown && Node.Nodes?.Count > 0)
                                {
                                    continue;
                                }

                                // Open Radzen MenuItem component for each menu item
                                menuBuilder.OpenComponent<RadzenMenuItem>(sequence++);
                                menuBuilder.AddAttribute(sequence++, "Value", (int)menuItem.Type);
                                menuBuilder.AddAttribute(sequence++, "Text", menuItem.Text);
                                menuBuilder.AddAttribute(sequence++, "Image", GetFa6SvgPath(menuItem.Icon));
                                menuBuilder.CloseComponent();
                            }
                        }));

                        // Close RadzenMenu component
                        builder.CloseComponent();
                    };
                };

                ContextMenuService.Open(args, menuFragment);
            }
        }

        private static string GetFa6SvgPath(string name) => $"/svg/{name}.svg";
    }
}
