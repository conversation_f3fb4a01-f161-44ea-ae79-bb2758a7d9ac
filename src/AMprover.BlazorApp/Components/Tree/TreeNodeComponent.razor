﻿@typeparam TItem

<ContextMenuDiv class=@GetRowClass() style=@GetRowStyle() 
                                @onclick=@ClickItem ContextMenu=@(ShowContextMenuWithItems)>

    <div class="treeview-expand" @onclick=@ExpandButtonClick @onclick:stopPropagation="true">
        &nbsp;@(Node.Nodes.Any() ? Node.Open ? "-" : "+" : "")
    </div>

    <div class="text-truncate">
        <span class=@Node.GetIconClass() aria-hidden="true"></span>
        <span class="fas fa-check treenode-filtered-icon" aria-hidden="true" style=@GetFilteredStyle()></span>
        <span class="treenode-text @Node.GetDisabledClass()">@Node.Name</span>
    </div>

</ContextMenuDiv>

@if (Node.Open)
{
    @foreach (var childNode in Node.Nodes.Where(x => !x.Nodes.Any()))
    {
        if (!childNode.Hidden)
        {
            <TreeNodeComponent Node=@childNode
                               ParentNode=@Node
                               Depth="Depth + 1"
                               Tree=Tree />
        }
    }

    @foreach (var childNode in Node.Nodes.Where(x => x.Nodes.Any()))
    {
        if (!childNode.Hidden)
        {
            <TreeNodeComponent Node=@childNode
                               ParentNode=@Node
                               Depth="Depth + 1"
                               Tree=Tree />
        }
    }
}