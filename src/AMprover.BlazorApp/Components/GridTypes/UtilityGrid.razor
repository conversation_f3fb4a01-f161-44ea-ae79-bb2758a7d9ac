@typeparam TItem
@namespace AMprover.BlazorApp.Components.GridTypes

<div class=@GetHeaderDivClass()>
    <div class="row spacer">
        <div class="col-5 pl-4">
            @if (Interactive)
            {
                @if (AddRowCallBack.HasDelegate)
                {
                    <RadzenButton Icon=add_circle_outline Text=@GetLocalizedText("UgAddBtn") ButtonStyle=ButtonStyle.Success Size=ButtonSize.Medium Click=@AddRowCallBack
                                  Disabled=@(!UserCanEdit) @ref=NewButton
                                  @onmouseover="@(() => ShowTooltip(NewButton.Element, GetLocalizedText("UgAddBtnTxt")))"
                                  @onmouseleave="@(() => HideTooltip(NewButton.Element))"/>
                }
                else if (AddNewButton || AddNewOverride != null)
                {
                    <RadzenButton Icon="add_circle_outline" Text=@GetLocalizedText("UgAddBtn") ButtonStyle=ButtonStyle.Success Size=ButtonSize.Medium Click=@AddNew
                                  Disabled=@(!UserCanEdit || CurrentItem != null)                                @ref=NewButton
                                  @onmouseover="@(_ => ShowTooltip(NewButton.Element, @GetLocalizedText("UgAddBtnTxt2")))"
                                  @onmouseleave="@(() => HideTooltip(NewButton.Element))"/>
                }
            }
            @if (FileUploadCallBack.HasDelegate)
            {
                <RadzenFileInput ChooseText=Import @ref=@UploadRef @bind-Value=@FileUpload Accept=".xls,.xlsx" TValue=string
                                 Disabled=@(!UserCanEdit) Change=@(async args => await OnChange(args)) Error=@(args => OnError(args))/>
            }
            @if (@Title != "")
            {
                <div class="utility-title">
                    @Title
                </div>
            }
        </div>

        <div class="col-2 text-center">
            @if (CustomAction.HasDelegate)
            {
                <RadzenButton ButtonStyle=ButtonStyle.Success Text=@CustomButtonText Size=ButtonSize.Medium
                              Click=@(async () => await CustomAction.InvokeAsync())
                              Disabled=@(!UserCanEdit)                          @ref=CustomButton
                              @onmouseover="@(() => ShowTooltip(CustomButton.Element, CustomToolTipText))"
                              @onmouseleave="@(() => HideTooltip(CustomButton.Element))"/>
            }
        </div>

        <div class="col-5">
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.End" Gap="0.4rem" Wrap="FlexWrap.Wrap" class="btn-float-right">
                <RadzenButton Icon=@OrderedListCallbackBtnIcon Text=@OrderedListCallbackBtnTxt Size=ButtonSize.Medium Click=@CallbackOrderedList
                              Visible="OrderedListCallback.HasDelegate"
                              Disabled=@(!UserCanEdit || !OrderedListCallback.HasDelegate) ButtonStyle=ButtonStyle.Secondary />

                <RadzenButton Text=Excel Icon=grid_on Click=@DownloadExcel Gap="1rem" Size=ButtonSize.Medium
                              Visible="AllowXlsExport"
                              Disabled="!AllowXlsExport"
                              @ref=ExportExcelButton
                              @onmouseover="@(() => ShowTooltip(ExportExcelButton.Element, GetLocalizedText("UgExceltnTxt")))"
                              @onmouseleave="@(() => HideTooltip(ExportExcelButton.Element))" />

                <RadzenButton Icon="edit" Text=@GetLocalizedText("UgChangeColumnsBtn") Size=ButtonSize.Medium Click=@OpenColumnEditor
                              Visible="AllowChangeColumns"
                              Disabled=@(!UserCanEdit || !AllowChangeColumns)                              @ref=ChangeColumnsButton
                              @onmouseover="@(() => ShowTooltip(ChangeColumnsButton.Element, GetLocalizedText("UgChangeColumnsBtnTxt")))"
                              @onmouseleave="@(() => HideTooltip(ChangeColumnsButton.Element))" />
            </RadzenStack>
        </div>
    </div>

    <div class=@GetOuterDivClass()>
        <div id=@ContainerInnerDivId>
            <RadzenDataGrid Data=@Data TItem=TItem @ref=@Grid
                            EditMode=DataGridEditMode.Multiple
                            FilterMode=FilterMode.Simple FilterCaseSensitivity=FilterCaseSensitivity.CaseInsensitive
                            CellContextMenu=@(args => ShowContextMenuWithItems(args)) AllowColumnResize="true"
                            RowSelect=@RowSelectCallBack RowDoubleClick=@RowDoubleClick Filter=@OnFilter FilterCleared=@OnFilter
                            AllowFiltering=@AllowFiltering AllowSorting=@AllowSorting AllowPaging=@(MaxRows != null) PageSize=@(MaxRows ?? 25)>
                <Columns>

                    <!-- Actions -->
                    @if (Interactive && !DisableEditButton)
                    {
                        <RadzenDataGridColumn Width="80px" TItem="TItem" Title="Actions" TextAlign=TextAlign.Center
                                              Filterable=false Sortable=false CssClass="m-0 p-1">
                            <Template Context="item">
                                @if (ContextMenuCondition == null || ContextMenuCondition?.Invoke(item) == true)
                                {
                                    @if (UserCanEdit && (CurrentItem == null || CurrentItem.Equals(item)))
                                    {
                                        <AMproverSplitButton Text=@GetEditButtonText() class="float-right m-0 p-0" Click=@(args => SplitButtonItemClicked(args, item))>
                                            <ChildContent>
                                                @foreach (var menuItem in ContextMenuItems)
                                                {
                                                    <AMproverSplitButtonItem Text=@menuItem.Text Value=@(((int) menuItem.Type).ToString()) Image=@GetFa6SvgPath(menuItem.Icon)/>
                                                }
                                            </ChildContent>
                                        </AMproverSplitButton>
                                    }
                                    else
                                    {
                                        <AMproverSplitButton Text=@GetEditButtonText() class="float-right m-0 p-0" Disabled=!EditCallBackOverride.HasDelegate Click=@(args => EditCallBackOverride.InvokeAsync(item))>
                                            <ChildContent>
                                                @foreach (var menuItem in ContextMenuItems.Where(x => ReadOnlyAllowedCommands.Contains(x.Type)))
                                                {
                                                    <AMproverSplitButtonItem Text=@menuItem.Text Value=@(((int) menuItem.Type).ToString()) Image=@GetFa6SvgPath(menuItem.Icon)/>
                                                }
                                            </ChildContent>
                                        </AMproverSplitButton>
                                    }
                                }
                            </Template>
                            <EditTemplate Context="item">
                                <div class="rz-splitbutton rz-buttonset float-right m-0 p-0">

                                    <!-- Save Inline Changes-->
                                    <RadzenButton Click=@(() => SaveRow(item))
                                                  class="amprover-splitbutton ampover-combined-button"
                                                  Text=@GetLocalizedText("UgSaveBtn") type="button"
                                                  @ref=SaveButton
                                                  @onmouseover="@(() => ShowTooltip(SaveButton.Element, GetLocalizedText("UgSaveBtnTxt")))"
                                                  @onmouseleave="@(() => HideTooltip(SaveButton.Element))"/>

                                    <!-- Cancel Inline Changes-->
                                    <RadzenButton Click=@(() => CancelEditRow(item)) type="button"
                                                  class="rz-splitbutton-menubutton rz-button-icon-only ampover-combined-button"
                                                  @ref=CancelButton
                                                  @onmouseover="@(() => ShowTooltip(CancelButton.Element, GetLocalizedText("UgUndoBtnTxt")))"
                                                  @onmouseleave="@(() => HideTooltip(CancelButton.Element))">
                                        <i class="fas fa-undo amprover-split-button-right-content"></i>
                                    </RadzenButton>

                                </div>
                            </EditTemplate>
                        </RadzenDataGridColumn>
                    }

                    @foreach (var prop in PropertiesToDisplay)
                    {
                        <RadzenDataGridColumn Filterable=@IsFilterable(prop) FilterProperty=@(prop.GetFilterProperty())
                                              Sortable=@(prop.GridColumn.FieldType != FieldType.Complex)
                                              TItem=TItem Property=@prop.GridColumn.FieldName Title=@(prop.GridColumn.ColumnHeader ?? prop.Header ?? prop.Name)
                                              Width=@GetColumnWidth(prop)>

                            <Template Context="item">
                                @GetDisplayValueHtml(item, prop)
                            </Template>

                            <EditTemplate Context="item">
                                @if (prop.IsEditable())
                                {
                                    <div class=@(prop.Valid ? "" : "invalid-cell")>
                                        @if (prop.DropdownOverride != null)
                                        {
                                            @if (prop.Nullable)
                                            {
                                                <AMDropdown
                                                    AllowClear=!prop.Required
                                                    TKeyType=int?
                                                    Value=@(GetValue<int?>(item, prop))
                                                    Change=@(args => ValueChanged(item, prop.Name, args, !prop.Required))
                                                    Data=@prop.DropdownOverride.ToNullableDictionary()
                                                    ContainerClass=@string.Empty/>
                                            }
                                            else
                                            {
                                                <AMDropdown
                                                    AllowClear=!prop.Required
                                                    TKeyType=int?
                                                    Value=@(GetValue<int>(item, prop))
                                                    Change=@(args => ValueChanged(item, prop.Name, args, !prop.Required))
                                                    Data=@prop.DropdownOverride.ToNullableDictionary()
                                                    ContainerClass=@string.Empty/>
                                            }
                                        }
                                        else if (prop.OptionOverride != null)
                                        {
                                            <AMDropdown
                                                AllowClear=!prop.Required
                                                Value=@(GetValue<string>(item, prop))
                                                Change=@(args => ValueChanged(item, prop.Name, args, !prop.Required))
                                                Data=@prop.OptionOverride
                                                ContainerClass=@string.Empty/>
                                        }
                                        else if (prop.GridColumn.FieldType is FieldType.Text)
                                        {
                                            if (prop.TextArea)
                                            {
                                                <RadzenTextArea Rows=@prop.TextAreaRows Name=@prop.Name class="form-control"
                                                                Value=@(GetValue<string>(item, prop))
                                                                Change=@(args => ValueChanged(item, prop.Name, args, !prop.Required))
                                                                MaxLength=@prop.MaxLength/>
                                            }
                                            else
                                            {
                                                <RadzenTextBox Name=@prop.Name class="form-control"
                                                               Value=@(GetValue<string>(item, prop))
                                                               Change=@(args => ValueChanged(item, prop.Name, args, !prop.Required))
                                                               MaxLength=@prop.MaxLength/>
                                            }
                                        }
                                        else if (prop.GridColumn.FieldType is FieldType.Currency or FieldType.DetailCurrency or FieldType.Percentage or FieldType.Number)
                                        {
                                            <AMproverNumberInput Name=@prop.Name TValue=decimal? Format=@prop.GetFormat()
                                                                 Value=@(GetValue<decimal?>(item, prop)) UsedInGrid=true
                                                                 Change=@(args => ValueChanged(item, prop.Name, args, !prop.Required))
                                                                 Min=@prop.MinValue Max=@prop.MaxValue class="form-control"/>
                                        }
                                        else if (prop.GridColumn.FieldType is FieldType.Integer or FieldType.IntegerNoStyling)
                                        {
                                            <RadzenNumeric Name=@prop.Name TValue=int? class="form-control" Format=@prop.GetFormat()
                                                           Value=@(GetValue<int?>(item, prop))
                                                           Change=@(args => ValueChanged(item, prop.Name, args, !prop.Required))
                                                           Min=@prop.MinValue Max=@prop.MaxValue/>
                                        }

                                        @if (!prop.Valid)
                                        {
                                            <small>Required</small>
                                        }
                                    </div>
                                }
                                else
                                {
                                    @GetDisplayValue(item, prop)
                                }
                            </EditTemplate>

                        </RadzenDataGridColumn>
                    }
                </Columns>
            </RadzenDataGrid>

            @if (Data?.Any() != true)
            {
                <div class="datagrid-context-header" @oncontextmenu=@(args => ShowContextMenuWithoutItem(args)) @oncontextmenu:preventDefault="true"></div>
            }
            <br/>
        </div>
    </div>
</div>
