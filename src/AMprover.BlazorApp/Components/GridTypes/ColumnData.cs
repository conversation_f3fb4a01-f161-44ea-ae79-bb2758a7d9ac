﻿using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models;
using System.Collections.Generic;

namespace AMprover.BlazorApp.Components.GridTypes;

public class ColumnData
{
    public string Name { get; set; }

    public bool Editable { get; set; }

    public int Width { get; set; }

    public string Header { get; set; }

    public bool Nullable { get; set; }

    public Dictionary<int, string> DropdownOverride { get; set; }

    public Dictionary<string, string> OptionOverride { get; set; }

    public bool Required { get; set; }

    public bool Valid { get; set; } = true;

    public int? MaxLength { get; set; }

    public bool TextArea { get; set; }

    public int TextAreaRows { get; set; }

    public decimal? MinValue { get; set; }

    public decimal? MaxValue { get; set; }

    public string NumericFormat { get; set; }

    public GridColumnModel GridColumn { get; set; }

    public string FilterProperty { get; set; }

    public string GetFormat()
    {
        if (!string.IsNullOrWhiteSpace(NumericFormat))
            return NumericFormat;

        return GetFieldType() switch
        {
            FieldType.Currency => "c0",
            FieldType.DetailCurrency => "c2",
            FieldType.Percentage => "p2",
            FieldType.Integer => "n0",
            FieldType.IntegerNoStyling => "n0",
            _ => "n2"
        };
    }

    public FieldType GetFieldType()
    {
        // Field Types set by the user take precedence
        if (GridColumn != null && GridColumn.FieldType != FieldType.None)
            return GridColumn.FieldType;

        // Text as fallback
        return FieldType.Text;
    }

    public bool IsEditable()
    {
        return Editable && GridColumn?.FieldName?.Contains(".") != true;
    }

    public string GetFilterProperty()
    {
        return FilterProperty ?? GridColumn.FieldName;
    }
}