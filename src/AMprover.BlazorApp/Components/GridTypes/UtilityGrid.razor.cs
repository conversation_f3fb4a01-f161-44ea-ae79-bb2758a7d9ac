﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using AMprover.BlazorApp.Components.SplitButton;
using AMprover.BlazorApp.Pages;
using AMprover.BusinessLogic;
using AMprover.BusinessLogic.Attributes;
using AMprover.BusinessLogic.Constants;
using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Helpers;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.ContextMenu;
using BlazorDownloadFile;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using Radzen;
using Radzen.Blazor;

namespace AMprover.BlazorApp.Components.GridTypes;

public partial class UtilityGrid<TItem>
{
    #region Dependency Injection Properties

    [Inject] protected ILoggerFactory LoggerFactory { get; set; }
    [Inject] protected ILookupManager LookupManager { get; set; }
    [Inject] protected TooltipService TooltipService { get; set; }
    [Inject] protected NavigationManager NavigationManager { get; set; }
    [Inject] protected DialogService DialogService { get; set; }
    [Inject] protected IJSRuntime JsRuntime { get; set; }
    [Inject] protected IExportManager ExportManager { get; set; }
    [Inject] protected IBlazorDownloadFileService DownloadFileService { get; set; }
    [Inject] protected ContextMenuService ContextMenuService { get; set; }
    [Inject] protected IClipboardManager ClipboardManager { get; set; }
    [Inject] protected IGlobalDataService GlobalDataService { get; set; }
    [Inject] protected LocalizationHelper LocalizationHelper { get; set; }

    #endregion

    #region BaseGrid Parameters

    [Parameter] public Dictionary<string, Dictionary<int, string>> DropDownOverrides { get; set; }
    [Parameter] public Dictionary<string, Dictionary<string, string>> OptionOverrides { get; set; }
    [Parameter] public string FileName { get; set; }
    [Parameter] public List<TItem> Data { get; set; }
    [Parameter] public Action AddNewOverride { get; set; }
    [Parameter] public EventCallback<TItem> SaveCallback { get; set; }
    [Parameter] public EventCallback<TItem> DeleteCallback { get; set; }
    [Parameter] public TItem NewItemTemplate { get; set; }
    [Parameter] public Func<TItem, bool> ExternalSaveValidation { get; set; }
    [Parameter] public EventCallback<TItem> ExternalSaveValidationFailed { get; set; }
    [Parameter] public List<string> EditableFields { get; set; }

    #endregion

    #region UtilityGrid Parameters

    [Parameter] public bool Interactive { get; set; }
    [Parameter] public bool DeleteAreYouSurePopup { get; set; } = true;
    [Parameter] public bool DisableEditButton { get; set; }
    [Parameter] public EventCallback<FileUpload> FileUploadCallBack { get; set; }
    [Parameter] public EventCallback<TItem> EditRowCallback { get; set; }
    [Parameter] public EventCallback<TItem> OpenLccCallback { get; set; }
    [Parameter] public EventCallback<(TItem, CopyType)> PasteCallBack { get; set; }
    [Parameter] public EventCallback<TItem> CloneCallBack { get; set; }
    [Parameter] public EventCallback<TItem> OpenPopup { get; set; }
    [Parameter] public EventCallback<TItem> OpenInfo { get; set; }
    [Parameter] public EventCallback<TItem> EditCallBackOverride { get; set; }
    [Parameter] public EventCallback<TItem> DeriveRowCallBack { get; set; }
    [Parameter] public EventCallback<TItem> CopyFromPmoCallback { get; set; }
    [Parameter] public EventCallback<TItem> CopyToPmoRowCallBack { get; set; }
    [Parameter] public EventCallback<TItem> OpenPageCallback { get; set; }
    [Parameter] public EventCallback<TItem> AcceptCallback { get; set; }
    [Parameter] public EventCallback<TItem> DeclineCallback { get; set; }
    [Parameter] public EventCallback<List<TItem>> OrderedListCallback { get; set; }
    [Parameter] public string OrderedListCallbackExplanation { get; set; }
    [Parameter] public string Title { get; set; }
    [Parameter] public ContextMenuItemType DefaultContextMenuAction { get; set; } = ContextMenuItemType.Edit;
    [Parameter] public bool DynamicWidth { get; set; }
    [Parameter] public bool AllowFiltering { get; set; }
    [Parameter] public bool AllowXlsExport { get; set; }
    [Parameter] public bool AllowChangeColumns { get; set; } = true;
    [Parameter] public string OrderedListCallbackBtnTxt { get; set; }
    [Parameter] public string OrderedListCallbackBtnIcon { get; set; }
    [Parameter] public Func<TItem, bool> ContextMenuCondition { get; set; }
    [Parameter] public EventCallback<DataGridColumnFilterEventArgs<TItem>> OnFilter { get; set; }
    [Parameter] public EventCallback AddRowCallBack { get; set; }
    [Parameter] public EventCallback RowSelectCallBack { get; set; }
    [Parameter] public EventCallback CustomAction { get; set; }
    [Parameter] public bool AllowCut { get; set; }
    [Parameter] public string OpenPageTextOverride { get; set; }
    [Parameter] public bool UseOpenTextInsteadOfEdit { get; set; }
    [Parameter] public string CustomButtonText { get; set; }
    [Parameter] public string CustomToolTipText { get; set; }
    [Parameter] public int? MaxRows { get; set; }
    [Parameter] public bool AddNewButton { get; set; }
    [Parameter] public bool AllowSorting { get; set; } = true;
    [Parameter] public int? PageSize { get; set; }
    [Parameter] public string CssClass { get; set; }

    #endregion

    #region State Properties

    private ILogger Logger { get; set; }
    private List<ColumnData> Properties { get; set; }
    private List<ColumnData> PropertiesToDisplay { get; set; }
    private List<GridColumnModel> TableColumns { get; set; }
    private RadzenButton ChangeColumnsButton { get; set; }
    private TItem CurrentItem { get; set; }
    private TItem BackupItem { get; set; }
    public RadzenDataGrid<TItem> Grid { get; set; }
    private string ElementWithMouse { get; set; }
    private TItem ContextMenuOpenFor { get; set; }

    // Used in javascript to dynamically scale container width
    private string ContainerInnerDivId { get; set; } = $"UtilityGridInnerDiv-{Guid.NewGuid()}";

    #endregion

    #region UI Components

    // Upload Requirements
    public string FileUpload { get; set; }
    private RadzenFileInput<string> UploadRef { get; set; }
    public string UploadError { get; set; }

    // Buttons
    private RadzenButton CancelButton { get; set; }
    private RadzenButton CustomButton { get; set; }
    public RadzenButton ExportCsvButton { get; set; }
    private RadzenButton ExportExcelButton { get; set; }
    private RadzenButton NewButton { get; set; }
    private RadzenButton SaveButton { get; set; }

    #endregion

    #region Context Menu Properties

    private List<AMproverContextMenuItem> ContextMenuItems { get; set; } = [];
    private List<AMproverContextMenuItem> ContextMenuItemsEditMode { get; set; } = [];
    private List<AMproverContextMenuItem> ContextMenuItemsForEmptyRow { get; set; } = [];
    private ContextMenuItemType[] ReadOnlyAllowedCommands { get; set; } = new[] { ContextMenuItemType.Open, ContextMenuItemType.EditOverride };

    #endregion

    #region Helper Properties

    private IStringLocalizer Localizer => LocalizationHelper.GetLocalizer<UtilityGrid<TItem>>();
    private string Language => GlobalDataService.Language;
    private string Currency => GlobalDataService.Currency;
    private bool UserCanEdit => GlobalDataService.CanEdit;
    private string GetLocalizedText(string key) => $"{Localizer[key]}";
    private string GetFa6SvgPath(string name) => $"/svg/{name}.svg";

    #endregion

    #region CSS Helpers

    private string GetHeaderDivClass() => DynamicWidth ? $"{CssClass} utilityGrid-dynamic-header" : CssClass;
    private string GetOuterDivClass() => DynamicWidth ? "utilityGrid-dynamic-container" : string.Empty;

    #endregion

    #region Lifecycle Methods

    protected override void OnInitialized()
    {
        Logger = LoggerFactory.CreateLogger(GetType().Name);
        BackupItem = (TItem)Activator.CreateInstance(typeof(TItem));

        GenerateContextMenuListEditMode();
        GenerateContextMenuList();
        GenerateContextMenuListForEmptyRow();

        Properties = GetProperties();
        TableColumns = ForceGetTableColumns();

        foreach (var prop in Properties)
            prop.GridColumn = TableColumns.FirstOrDefault(x => x.ColumnName == prop.Name);

        PropertiesToDisplay = Properties
            .Where(x => x.GridColumn?.Visible == true)
            .OrderBy(x => x.GridColumn.DisplayIndex).ThenBy(x => x.Name)
            .ToList();
    }

    #endregion

    #region Column and Property Management

    public void RefreshDropdownOverrides(Dictionary<string, Dictionary<int, string>> dropdownOverrides)
    {
        DropDownOverrides = dropdownOverrides;
        OnInitialized();
    }

    private List<GridColumnModel> ForceGetTableColumns()
    {
        var result = LookupManager.GetColumns(FileName, false).ToList();
        return result.Any() ? result : LookupManager.SeedGridColumns<TItem>(FileName);
    }

    private List<ColumnData> GetProperties()
    {
        var properties = new List<ColumnData>();
        foreach (var prop in typeof(TItem).GetProperties())
        {
            // Attributes
            var attrs = Attribute.GetCustomAttributes(prop);

            if (attrs.Any(x => x is GridIgnoreAttribute))
                continue;

            // result
            var columnData = new ColumnData
            {
                Name = prop.Name,
                Nullable = Nullable.GetUnderlyingType(prop.PropertyType) != null
            };

            foreach (var attr in attrs)
            {
                switch (attr)
                {
                    case GridWidthPixelsAttribute w:
                        columnData.Width = w.Width;
                        break;
                    case GridHeaderAttribute h:
                        columnData.Header = h.Header;
                        break;
                    case RequiredAttribute r:
                        columnData.Required = true;
                        break;
                    case MaxLengthAttribute m:
                        columnData.MaxLength = m.Length;
                        break;
                    case GridNumericFormatAttribute nf:
                        columnData.NumericFormat = nf.Format;
                        break;
                    case GridStringEnumAttribute { ValuesAsEnum: not { IsEnum: true } } ea:
                        throw new ArgumentException($"GridStringEnumAttribute can only have an Enum as parameter. Currently it is `{ea?.ValuesAsEnum?.GetType()}`");
                    case GridStringEnumAttribute ea:
                        {
                            var values = Enum.GetValues(ea.ValuesAsEnum)
                                .Cast<object>()
                                .Select(x => x.ToString())
                                .ToList();

                            columnData.OptionOverride = values.ToDictionary(x => x);
                            break;
                        }
                    case RangeAttribute ra:
                        columnData.MinValue = Convert.ToDecimal(ra.Minimum);
                        columnData.MaxValue = Convert.ToDecimal(ra.Maximum);
                        break;
                    case GridTextAreaAttribute t:
                        columnData.TextArea = true;
                        columnData.TextAreaRows = t.Lines;
                        break;
                    case GridFilterProperty f:
                        columnData.FilterProperty = f.FilterProperty;
                        break;
                }
            }

            // Auto gen enum dropdown values
            var type = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
            if (type.IsEnum)
            {
                columnData.DropdownOverride = new Dictionary<int, string>();

                foreach (var val in Enum.GetValues(type))
                    columnData.DropdownOverride.Add(Convert.ToInt32(val), val.ToString());
            }

            columnData.Editable = !attrs.Any(x => x is GridReadOnlyAttribute);
            properties.Add(columnData);
        }

        if (DropDownOverrides != null)
        {
            foreach (var pair in DropDownOverrides)
            {
                var column = properties.FirstOrDefault(x => x.Name == pair.Key);

                if (column != null)
                {
                    column.DropdownOverride = pair.Value;
                }
            }
        }

        if (OptionOverrides != null)
        {
            foreach (var pair in OptionOverrides)
            {
                var column = properties.FirstOrDefault(x => x.Name == pair.Key);

                if (column != null)
                {
                    column.OptionOverride = pair.Value;
                }
            }
        }

        if (EditableFields?.Any() == true)
        {
            properties.ForEach(x => x.Editable = false);

            foreach (var prop in properties.Where(x => EditableFields.Contains(x.Name)))
                prop.Editable = true;
        }

        return properties;
    }

    private string GetColumnWidth(ColumnData columnData)
    {
        if (columnData.Width > 0)
            return $"{columnData.Width}px";

        return $"{columnData.GridColumn.ColumnWidth}%";
    }

    private bool IsFilterable(ColumnData columnData)
    {
        return AllowFiltering && columnData.GridColumn.FieldType != FieldType.Complex;
    }

    #endregion

    #region Column Editor

    private void OpenColumnEditor()
    {
        DialogService.Open<TableColumns>("Table columns",
            new Dictionary<string, object>
            {
                {"ControlName", FileName},
                {"ReseedFunction", EventCallback.Factory.Create<object>(this, ReSeedColumns)},
                {"SaveCallback", EventCallback.Factory.Create<object>(this, SaveGridColumnsCallback)}
            },
            new DialogOptions { Width = "880px", Resizable = true, Draggable = true });
    }

    private void ReSeedColumns(object _)
    {
        DialogService.Close();
        LookupManager.SeedGridColumns<TItem>(FileName);
        Grid.Reload();
        OnInitialized();
        SetDynamicContainerWidth();
    }

    private void SaveGridColumnsCallback(object _)
    {
        Grid.Reload();
        OnInitialized();
        SetDynamicContainerWidth();
    }

    private void SetDynamicContainerWidth()
    {
        if (!DynamicWidth)
            return;

        var widthFromColumns = PropertiesToDisplay.Sum(x => x?.GridColumn?.ColumnWidth ?? 0);
        Task.Run(async () =>
            await JsRuntime.InvokeVoidAsync("SetUtilityGridInnerContainerWidth", widthFromColumns,
                ContainerInnerDivId));
    }

    #endregion

    #region Row Operations

    private async Task EditRow(TItem item)
    {
        var canEdit = CurrentItem == null || await SaveRow(CurrentItem);

        if (canEdit)
        {
            CurrentItem = item;
            Properties.ForEach(x => x.Valid = true);
            CopyValues(item, BackupItem);
            await Grid.EditRow(item);
        }
    }

    public async Task<bool> SaveRow(TItem item)
    {
        Properties.ForEach(x => x.Valid = true);
        var type = item.GetType();

        // Get both fields and properties
        var fields = type.GetFields(BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Public);
        var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        var parentType = type.BaseType;

        // Include parent class fields and properties if in the same namespace
        if (parentType != null && parentType.Namespace == type.Namespace)
        {
            var parentFields = parentType.GetFields(BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Public);
            var parentProperties = parentType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            fields = fields.Concat(parentFields).ToArray();
            properties = properties.Concat(parentProperties).ToArray();
        }

        foreach (var prop in Properties.Where(x => x.Editable && x.Required))
        {
            try
            {
                // Try to find field or property with matching name (case-insensitive)
                var field = fields.FirstOrDefault(f =>
                    string.Equals(f.Name, $"<{prop.Name}>k__BackingField", StringComparison.OrdinalIgnoreCase) ||
                    string.Equals(f.Name, prop.Name, StringComparison.OrdinalIgnoreCase));

                var property = properties.FirstOrDefault(p =>
                    string.Equals(p.Name, prop.Name, StringComparison.OrdinalIgnoreCase));

                object value = null;
                if (field != null)
                {
                    value = field.GetValue(item);
                }
                else if (property != null && property.CanRead)
                {
                    value = property.GetValue(item);
                }

                // Handle different types of values
                if (value == null)
                {
                    prop.Valid = false;
                }
                else if (value is string strValue)
                {
                    prop.Valid = !string.IsNullOrWhiteSpace(strValue);
                }
                else if (value is int intValue)
                {
                    prop.Valid = intValue != 0; // Assuming 0 is not a valid value for required int fields
                }
                else if (value is decimal decimalValue)
                {
                    prop.Valid = decimalValue != 0; // Assuming 0 is not a valid value for required decimal fields
                }
                else
                {
                    // For other types (like enums, objects, etc.), consider them valid if not null
                    prop.Valid = true;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error validating property {PropName}", prop.Name);
                prop.Valid = false;
            }
        }

        if (Properties.All(x => x.Valid))
        {
            if (ExternalSaveValidation != null)
            {
                var passedExtraValidation = ExternalSaveValidation.Invoke(item);
                if (!passedExtraValidation)
                {
                    if (ExternalSaveValidationFailed.HasDelegate)
                        await ExternalSaveValidationFailed.InvokeAsync(item);

                    return false;
                }
            };

            await SaveCallback.InvokeAsync(item);
            Grid.CancelEditRow(item);
            CurrentItem = default;
            return true;
        }

        return false;
    }

    private void CancelEditRow(TItem item)
    {
        CopyValues(BackupItem, item);
        Grid.CancelEditRow(item);
        Properties.ForEach(x => x.Valid = true);
        CurrentItem = default;
    }

    public async Task DeleteRow(TItem item)
    {
        Properties.ForEach(x => x.Valid = true);
        await DeleteCallback.InvokeAsync(item).ConfigureAwait(false);

        CopyValues(BackupItem, item);
        Grid.CancelEditRow(item);
        CurrentItem = default;
    }

    private async Task AddNew()
    {
        if (AddNewOverride != null)
            AddNewOverride.Invoke();

        else
        {
            CurrentItem = (TItem)Activator.CreateInstance(typeof(TItem));

            if (NewItemTemplate != null)
                CopyValues(NewItemTemplate, CurrentItem);

            await Grid.InsertRow(CurrentItem);

            // It is impossible to edit details on a new entry when the original collection was empty. This 'fixes' that
            if (!Data.Any())
                Data.Add(CurrentItem);
        }
    }

    public void SelectRow(TItem item)
    {
        Grid.SelectRow(item);
    }

    public void EditItem(TItem item)
    {
        CurrentItem = item;
    }

    #endregion

    #region Value Handling

    private FieldInfo[] GetFieldsRecursive(TItem item)
    {
        var fields = item.GetType().GetFields(BindingFlags.NonPublic | BindingFlags.Instance);
        var parentType = item.GetType().BaseType;

        while (parentType != typeof(object))
        {
            fields = fields.AsEnumerable().Concat(parentType?.GetFields(BindingFlags.NonPublic | BindingFlags.Instance)!).ToArray();
            parentType = parentType?.BaseType;
        }

        return fields;
    }

    private T GetValue<T>(TItem item, ColumnData columnData)
    {
        var fields = GetFieldsRecursive(item);

        var field = fields.FirstOrDefault(x => x.IsField(columnData.Name));
        var value = field?.GetValue(item);

        var t = Nullable.GetUnderlyingType(typeof(T)) ?? typeof(T);

        return value != null
            ? (T)Convert.ChangeType(value, t)
            : default;
    }

    private MarkupString GetDisplayValueHtml(TItem item, ColumnData columnData)
    {
        var (val, displayValue) = GetDisplayValue(item, columnData);
        var cssClass = GetCssClassDisplayValue(val, displayValue, columnData);
        var html = $"<div class=\"{cssClass}\">{displayValue}</div>";
        return new MarkupString(html);
    }

    private string GetCssClassDisplayValue(object val, string displayValue, ColumnData columnData)
    {
        return columnData.GetFieldType() switch
        {
            FieldType.Currency => "text-right",
            FieldType.Text => GetTextCss(displayValue, columnData),
            _ => string.Empty
        };
    }

    private readonly Dictionary<string, string> CssDict = new()
    {
        { "A", "red-cell" },
        { "B", "orange-cell" },
        { "C", "green-cell" }
    };

    private string GetTextCss(string displayValue, ColumnData columnData)
    {
        if (columnData?.GridColumn?.ControlName == "Criticality_Rankings" && columnData.Name == "Category")
        {
            CssDict.TryGetValue(displayValue, out string css);
            return css;
        }

        return string.Empty;
    }

    /// <summary>
    /// Will be used for more coloring of cells
    /// </summary>
    private static bool IsGreaterThan(object value, double threshold)
    {
        return value switch
        {
            int i => i > threshold,
            long l => l > threshold,
            float f => f > threshold,
            double d => d > threshold,
            decimal m => m > (decimal)threshold,
            _ => false
        };
    }

    public (object, string) GetDisplayValue(TItem item, ColumnData columnData)
    {
        // Dropdown
        if (columnData.DropdownOverride != null)
        {
            var index = GetValue<int?>(item, columnData);
            if (index != null)
            {
                return columnData.DropdownOverride.TryGetValue(index.Value, out var value1)
                    ? (null, value1)
                    : (null, string.Empty);
            }
        }

        // String Dropdown
        if (columnData.OptionOverride != null)
        {
            var val = GetValue<string>(item, columnData);
            if (val != null)
            {
                return columnData.OptionOverride.TryGetValue(val, out var value1)
                    ? (null, value1)
                    : (null, string.Empty);
            }
        }

        // Value as object
        var fields = GetFieldsRecursive(item);
        var field = fields.FirstOrDefault(x => x.IsField(columnData.Name));
        var value = field?.GetValue(item) ?? string.Empty;

        var stringResult = columnData.GetFieldType() switch
        {
            FieldType.Complex => value.ToString(),
            FieldType.None => value.ToString(),
            FieldType.Text => value.ToString(),
            FieldType.Number => value.ToNumericDisplayValue(Language, GetFormatString(columnData, "n2")),
            FieldType.Currency => value.ToNumericDisplayValue(Currency, GetFormatString(columnData, "c0")),
            FieldType.DetailCurrency => value.ToNumericDisplayValue(Currency, GetFormatString(columnData, "c2")),
            FieldType.Percentage => value.ToNumericDisplayValue(Language, GetFormatString(columnData, "p2")),
            FieldType.Date => DateTime.TryParse(value.ToString(), out var date) ? date.ToDateString(Language) : string.Empty,
            FieldType.Integer => value.ToNumericDisplayValue(Language, GetFormatString(columnData, "n0")),
            FieldType.IntegerNoStyling => $"{value}",
            FieldType.Boolean => GetBooleanFormat(item, field),
            _ => value.ToString(),
        };

        return (value, stringResult);
    }

    private string GetBooleanFormat(TItem item, FieldInfo field)
    {
        var value = field?.GetValue(item);
        if (value is bool b)
        {
            return b ? "✅" : "❌";
        }
        return "❌";
    }

    private string GetFormatString(ColumnData columnData, string defaultFormat)
    {
        return string.IsNullOrWhiteSpace(columnData?.NumericFormat)
            ? defaultFormat
            : columnData.NumericFormat;
    }

    private void ValueChanged<T>(TItem item, string propertyName, T value, bool allowNull = true)
    {
        var fields = GetFieldsRecursive(item);
        var field = fields.FirstOrDefault(x => x.IsField(propertyName));

        if (field == null)
            return;

        var prop = Properties.FirstOrDefault(x => x.Name == propertyName);
        if (prop == null)
            return;

        if (value == null && !allowNull)
        {
            prop.Valid = false;
            return;
        }

        prop.Valid = true;
        field.SetValue(item, value);
    }

    private void CopyValues(TItem source, TItem target)
    {
        if (source == null || target == null)
            return;

        foreach (var property in typeof(TItem).GetProperties().Where(p => p.CanWrite))
        {
            var value = property.GetValue(source, null);
            property.SetValue(target, value, null);
        }
    }

    #endregion

    #region Context Menu

    private void GenerateContextMenuListEditMode()
    {
        if (ContextMenuItemsEditMode.Any()) return;
        ContextMenuItemsEditMode.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Save, Text = GetLocalizedText("UgCtxSave"), Icon = "check" });
        ContextMenuItemsEditMode.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Cancel, Text = GetLocalizedText("UgCtxCancel"), Icon = "rotate-left" });
    }

    private void GenerateContextMenuListForEmptyRow()
    {
        if (ContextMenuItemsForEmptyRow.Any()) return;
        if (PasteCallBack.HasDelegate)
        {
            ContextMenuItemsForEmptyRow.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Paste, Text = GetLocalizedText("UgCtxPaste"), Icon = "paste" });
        }
    }
    private void GenerateContextMenuList()
    {
        if (ContextMenuItems.Any()) return;

        if (!DisableEditButton)
        {
            if (!EditCallBackOverride.HasDelegate)
            {
                ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Edit, Text = GetLocalizedText("UgCtxEdit"), Icon = "pencil" });
            }
            if (EditCallBackOverride.HasDelegate)
            {
                ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.EditOverride, Text = GetLocalizedText("UgCtxOpen"), Icon = "arrow-up-right-from-square" });
            }
        }

        if (OpenPageCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem
            {
                Type = ContextMenuItemType.OpenPage,
                Text = OpenPageTextOverride ?? GetLocalizedText("UgCtxOpen"),
                Icon = "arrow-up-right-from-square"
            });
        }

        if (OpenPopup.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Open, Text = GetLocalizedText("UgCtxOpenPopup"), Icon = "arrow-up-right-from-square" });
        }

        if (OpenInfo.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Info, Text = GetLocalizedText("UgCtxOpenInfoPopup"), Icon = "arrow-up-left-from-square" });
        }

        if (OpenLccCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.OpenLcc, Text = GetLocalizedText("UgCtxOpenLcc"), Icon = "chart-line-solid" });
        }

        if (CloneCallBack.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Clone, Text = GetLocalizedText("UgCtxCopy"), Icon = "copy" });
        }

        if (PasteCallBack.HasDelegate)
        {
            if (AllowCut)
            {
                ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Cut, Text = GetLocalizedText("UgCtxCut"), Icon = "scissors" });
            }

            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Copy, Text = GetLocalizedText("UgCtxCopy"), Icon = "copy" });
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Paste, Text = GetLocalizedText("UgCtxPaste"), Icon = "paste" });
        }

        if (DeleteCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Delete, Text = GetLocalizedText("UgCtxDelete"), Icon = "trash-can" });
        }

        if (DeriveRowCallBack.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Derive, Text = GetLocalizedText("UgCtxDerive"), Icon = "copy" });
        }

        if (CopyFromPmoCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.CopyFromPmo, Text = GetLocalizedText("UgCtxCopyFromPmo"), Icon = "copy" });
        }

        if (CopyToPmoRowCallBack.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.CopyToPmo, Text = GetLocalizedText("UgCtxCopyToPmo"), Icon = "copy" });
        }

        if (AcceptCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Accept, Text = GetLocalizedText("UgCtxAccept"), Icon = "check" });
        }

        if (DeclineCallback.HasDelegate)
        {
            ContextMenuItems.Add(new AMproverContextMenuItem { Type = ContextMenuItemType.Decline, Text = GetLocalizedText("UgCtxDecline"), Icon = "circle-xmark" });
        }

        var defaultItem = ContextMenuItems.Find(x => x.Type == DefaultContextMenuAction);

        if (defaultItem != null)
        {
            ContextMenuItems.Remove(defaultItem);
            ContextMenuItems.Insert(0, defaultItem);
        }
    }

    private void ShowContextMenuWithItems(DataGridCellMouseEventArgs<TItem> args)
    {
        if (Interactive)
            ContextMenuOpenFor = args == default ? default : args.Data;
    }

    private void ShowContextMenuWithoutItem(MouseEventArgs args)
    {
        if (Interactive)
            ContextMenuOpenFor = default;
    }

    private string GetEditButtonText()
    {
        switch (DefaultContextMenuAction)
        {
            case ContextMenuItemType.Edit:
                return EditCallBackOverride.HasDelegate ? GetLocalizedText("UgCtxOpen") : GetLocalizedText("UgCtxEdit");

            case ContextMenuItemType.OpenPage:
                return GetLocalizedText("UgCtxOpen");

            default:
                throw new NotImplementedException($"{DefaultContextMenuAction} has not been implemented");
        }
    }

    private async Task SplitButtonItemClicked(AMproverSplitButtonItem args, TItem item)
    {
        ContextMenuOpenFor = item;
        await ContextMenuItemCLickedInternal(
            (ContextMenuItemType)int.Parse(args?.Value ?? $"{(int)DefaultContextMenuAction}"));
    }

    private async Task ContextMenuItemCLickedInternal(ContextMenuItemType itemType)
    {
        switch (itemType)
        {
            case ContextMenuItemType.Edit:
                if (EditCallBackOverride.HasDelegate)
                    await EditCallBackOverride.InvokeAsync(ContextMenuOpenFor);
                else
                    await EditRow(ContextMenuOpenFor);
                break;

            case ContextMenuItemType.EditOverride:
                await EditCallBackOverride.InvokeAsync(ContextMenuOpenFor);
                break;

            case ContextMenuItemType.Copy:
                ClipboardManager.CopyItem(ContextMenuOpenFor);
                break;

            case ContextMenuItemType.Cut:
                ClipboardManager.CutItem(ContextMenuOpenFor);
                break;

            case ContextMenuItemType.Paste:
                if (PasteCallBack.HasDelegate)
                {
                    var (item, copyType) = ClipboardManager.GetCopiedItem<TItem>();
                    if (item != null)
                    {
                        await PasteCallBack.InvokeAsync((item, copyType));
                    }
                    else
                    {
                        DialogService.Open<InformationDialog>(GetLocalizedText("UgNoItemCopied"),
                            new Dictionary<string, object>
                            {
                                {"DialogContent", GetLocalizedText("UgCopyFirst")}
                            });
                    }
                }

                break;

            case ContextMenuItemType.Delete:
                if (DeleteCallback.HasDelegate)
                {
                    if (DeleteAreYouSurePopup)
                    {
                        DialogService.Open<AreYouSureDialog<TItem>>(GetLocalizedText("UgAreYouSure"),
                            new Dictionary<string, object>
                            {
                                {nameof(AreYouSureDialog<TItem>.Item), ContextMenuOpenFor},
                                {
                                    nameof(AreYouSureDialog<TItem>.YesCallback),
                                    EventCallback.Factory.Create<TItem>(this, ConfirmDelete)
                                }
                            });
                    }
                    else
                    {
                        ConfirmDelete(ContextMenuOpenFor);
                    }
                }

                break;

            case ContextMenuItemType.Open:
                if (OpenPopup.HasDelegate)
                    await OpenPopup.InvokeAsync(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.Info:
                if (OpenInfo.HasDelegate)
                    await OpenInfo.InvokeAsync(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.OpenPage:
                await OpenPageCallback.InvokeAsync(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.Save:
                await SaveRow(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.Cancel:
                CancelEditRow(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.Derive:
                await DeriveRowCallBack.InvokeAsync(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.CopyFromPmo:
                await CopyFromPmoCallback.InvokeAsync(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.CopyToPmo:
                await CopyToPmoRowCallBack.InvokeAsync(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.Clone:
                await CloneCallBack.InvokeAsync(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.OpenLcc:
                await OpenLccCallback.InvokeAsync(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.Accept:
                await AcceptCallback.InvokeAsync(ContextMenuOpenFor);
                break;
            case ContextMenuItemType.Decline:
                await DeclineCallback.InvokeAsync(ContextMenuOpenFor);
                break;
            default:
                throw new NotImplementedException();
        }

        ContextMenuOpenFor = default;
        ContextMenuService.Close();
    }

    private void ConfirmDelete(TItem item) => DeleteCallback.InvokeAsync(item);

    #endregion

    #region Event Handlers

    private async Task RowDoubleClick(DataGridRowMouseEventArgs<TItem> args)
    {
        if (Interactive && !DisableEditButton)
        {
            switch (DefaultContextMenuAction)
            {
                case ContextMenuItemType.Edit:
                    if (EditCallBackOverride.HasDelegate)
                    {
                        await EditCallBackOverride.InvokeAsync(args.Data);
                    }
                    else if (GlobalDataService.CanEdit)
                    {
                        if (CurrentItem == null)
                        {
                            await EditRow(args.Data);
                        }
                        else
                        {
                            await SaveRow(args.Data);
                        }
                    }
                    break;

                case ContextMenuItemType.OpenPage:
                    if (OpenPageCallback.HasDelegate)
                    {
                        await OpenPageCallback.InvokeAsync(args.Data);
                    }
                    break;

                default:
                    throw new NotImplementedException($"{DefaultContextMenuAction} has not been implemented");
            }
        }
    }

    private async Task OnChange(string value)
    {
        // clear ref, the component wants to display an image preview which does not apply
        UploadError = FileUpload = null;

        var fileUpload = new FileUpload
        {
            FileType = value?.Split(',').FirstOrDefault()?.Trim(),
            Base64Content = value?.Split(',').LastOrDefault()?.Trim()
        };

        await FileUploadCallBack.InvokeAsync(fileUpload).ConfigureAwait(true);
        await JsRuntime.InvokeAsync<bool>("ClearFileInputValue", UploadRef.Element).ConfigureAwait(true);
    }

    private void OnError(UploadErrorEventArgs args)
    {
        UploadError = args.Message;
    }

    #endregion

    #region Tooltip Handling

    private async Task ShowTooltip(ElementReference elementReference, string content,
        TooltipPosition position = TooltipPosition.Top)
    {
        ElementWithMouse = elementReference.Id;
        await Task.Delay(2000);

        if (elementReference.Id == ElementWithMouse)
            TooltipService.Open(elementReference, content, new TooltipOptions { Position = position, Duration = 6000 });
    }

    private async Task HideTooltip(ElementReference elementReference)
    {
        if (elementReference.Id == ElementWithMouse)
        {
            ElementWithMouse = string.Empty;
        }

        await Task.Delay(6000);

        if (string.IsNullOrEmpty(elementReference.Id))
        {
            TooltipService.Close();
        }
    }

    #endregion

    #region Export and Ordered List Operations

    private async Task DownloadExcel()
    {
        var stream = ExportManager.ExportExcel(Data, TableColumns);

        if (stream != null)
            _ = await DownloadFileService.DownloadFile($"{FileName}_{DateTime.Now.ToDateString(Language)}.xlsx",
                stream, FileContentTypes.Excel);
    }

    private async Task CallbackOrderedList()
    {
        DialogService.Open<AreYouSureDialog<int>>(GetLocalizedText("UgAreYouSure"),
            new Dictionary<string, object>
            {
                {
                    nameof(AreYouSureDialog<int>.Text),
                    OrderedListCallbackExplanation
                },
                {
                    nameof(AreYouSureDialog<int>.YesCallback),
                    EventCallback.Factory.Create<int>(this, PerformOrderListCallBack)
                }
            });
    }

    private async Task PerformOrderListCallBack(int i)
    {
        if (OrderedListCallback.HasDelegate)
        {
            var ordered = Grid.PagedView.ToList();
            await OrderedListCallback.InvokeAsync(ordered);
        }
    }

    #endregion

    #region Grid Operations

    public async Task Reload()
    {
        await Grid.Reload();
    }

    #endregion
}
