using Microsoft.AspNetCore.Components;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Components.Pagination
{
    public partial class Paginator
    {
        [Parameter]
        public int Count { get; set; }

        [Parameter]
        public EventCallback<int> CallBack { get; set; }

        [Parameter]
        public int Initial { get; set; }
        
        private int Current { get; set; } = 0;

        protected override void OnInitialized()
        {
            Current = Initial;
            base.OnInitialized();
        }

        private int GetCurrentDisplayIndex()
        {
            return Current + 1;
        }

        public void SetCurrentExternally(int index)
        {
            Current = index;
        }

        private async Task First()
        {
            await SetCurrent(0);
        }

        private async Task Previous()
        {
            var prev = Current - 1;

            if (prev < 0)
                prev = Count - 1;

            await SetCurrent(prev);
        }

        private async Task<int> SetFromInput(int input)
        {
            await SetCurrent(input - 1);
            return GetCurrentDisplayIndex();
        }

        private async Task Next()
        {
            var next = Current + 1;

            if (next >= Count)
                next = 0;

            await SetCurrent(next);
        }

        private async Task Last()
        {
            await SetCurrent(Count - 1);
        }

        private async Task SetCurrent(int index)
        {
            Current = index;
            if (CallBack.HasDelegate)
                await CallBack.InvokeAsync(index);
        }
    }
}