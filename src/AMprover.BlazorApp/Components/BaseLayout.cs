﻿using System.Threading.Tasks;
using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;

namespace AMprover.BlazorApp.Components;

public abstract class BaseLayout : LayoutComponentBase
{
    [Inject] private IGlobalDataService GlobalDataService { get; set; }

    [Inject] private IAMproverUserManager UserManager { get; set; }

    public BaseLayout() { }

    protected override async Task OnInitializedAsync()
    {
        // _globalDataService.UserRole = await _userManager.GetUserRole();
    }

    /// <summary>
    /// Hamburger Menu Logic
    /// </summary>
    public string SideMenuClass { get; set; }
    public bool SideMenuActive { get; set; }
    public void ActivateMenu()
    {
        SideMenuActive = !SideMenuActive;
        SideMenuClass = SideMenuActive ? "active" : string.Empty;
    }
}