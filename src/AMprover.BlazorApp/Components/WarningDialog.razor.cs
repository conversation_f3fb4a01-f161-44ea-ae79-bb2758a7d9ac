﻿using Microsoft.AspNetCore.Components;
using <PERSON><PERSON><PERSON>;

namespace AMprover.BlazorApp.Components;

public partial class WarningDialog
{
    [Inject] private DialogService DialogService { get; set; }

    [Parameter] public string DialogTitle { get; set; }
    [Parameter] public string DialogContent { get; set; }

    private void CloseWarning()
    {
        DialogService.Close();
    }
}