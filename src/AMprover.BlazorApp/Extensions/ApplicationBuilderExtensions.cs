using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using System;
using System.Linq;
using Microsoft.Extensions.Hosting;

namespace AMprover.BlazorApp.Extensions;

public static class ApplicationBuilderExtensions
{
    public static WebApplication ConfigureAMproverApp(this WebApplication app, IConfiguration configuration)
    {
            // Configure the HTTP request pipeline
            if (app.Environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/Error");
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();

            // Use request localization
            app.UseRequestLocalization(GetLocalizationOptions(configuration));

            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();

            app.MapControllers();
            app.MapBlazorHub(opts => {
                opts.WebSockets.CloseTimeout = TimeSpan.FromMinutes(5);
                opts.LongPolling.PollTimeout = TimeSpan.FromMinutes(5);
            });
            app.MapFallbackToPage("/_Host");

            return app;
        }

    private static RequestLocalizationOptions GetLocalizationOptions(IConfiguration configuration)
    {
            var cultures = configuration.GetSection("Languages")
                .GetChildren()
                .ToDictionary(x => x.Key, x => x.Value);

            var supportedCultures = cultures.Keys.ToArray();

            var localizationOptions = new RequestLocalizationOptions()
                .AddSupportedCultures(supportedCultures)
                .AddSupportedUICultures(supportedCultures);

            return localizationOptions;
        }
}