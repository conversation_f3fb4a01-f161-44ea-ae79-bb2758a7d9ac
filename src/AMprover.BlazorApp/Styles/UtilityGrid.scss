﻿.invalid-cell {
    input {
        border: 1px solid red !important;
        border-radius: 4px !important;
    }

    small {
        color: red;
        display: block;
    }
}

.btn-float-right {
    float: right;
}

.context-menu-trigger {
    height: 28px;
    width: 80px;
    left: -5px;
    position: absolute;
    margin-top: -13px;
    padding: 5px 0px;
}

.datagrid-context-header {
    height: 30px;
    margin-top: -30px;
    z-index: 1;
    position: relative;
}

// Set variables:
.rz-data-grid-data {
    --rz-grid-filter-padding: 3px;
    --rz-grid-filter-margin: 3px;
    --rz-numeric-input-padding: 3px;
    --rz-input-padding: 3px;
}

.rz-cell-filter {
    margin-left: 0px;
    max-height: 3.5rem;

    .rz-cell-filter-content {
        &:before {
            content: url(../svg/magnifying-glass-solid.svg);
            width: .75rem;
            min-width: .75rem;
            margin: .25rem .25rem .75rem .25rem;
        }

        .rz-spinner, .rz-textbox {
            border-color: #e9ecef;
            max-height: 2.25rem;
        }

        .rz-spinner-button {
            display: none;
        }
    }
}

.utility-title {
    font-size: 14px;
    font-weight: bold;
    margin-top: 8px;
}

.utilityGrid-dynamic-header {
    width: calc(100vw - 140px);
    overflow-x: hidden;
}

.utilityGrid-dynamic-container {
    width: calc(100vw - 140px);
    overflow-x: scroll;
}

.utilityGrid-xxs {
    width: calc(100vw - 140px);
}

.utilityGrid-xs {
    width: calc(150vw - 140px);
}

.utilityGrid-s {
    width: calc(200vw);
}

.utilityGrid-m {
    width: calc(250vw);
}

.utilityGrid-l {
    width: calc(300vw);
}

.utilityGrid-xl {
    width: calc(350vw);
}

.utilityGrid-xxl {
    width: calc(450vw);
}

.rz-grid-table td {
    &:has(.green-cell) {
        background-color: #80c080 !important;
    }
}

.rz-grid-table td {
    &:has(.orange-cell) {
        background-color: #ffd280 !important;
    }
}

.rz-grid-table td {
    &:has(.red-cell) {
        background-color: #ff8080 !important;
    }
}