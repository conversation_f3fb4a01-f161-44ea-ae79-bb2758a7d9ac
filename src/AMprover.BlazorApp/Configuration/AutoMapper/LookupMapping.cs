﻿using AMprover.BusinessLogic.Enums;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.Failures;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AutoMapper;

namespace AMprover.BlazorApp.Configuration.AutoMapper;

public static class LookupMapping
{
    public static void CreateLookupMapping(this IMapperConfigurationExpression mc)
    {
        mc.CreateMap<Data.Entities.AM.LookupFailMode, FailureModeModel>()
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.FailMode))
            .ForMember(dest => dest.WeibullShape, opt => opt.MapFrom(src => src.FailShape))
            .ForMember(dest => dest.WeibullMeanTimeBetweenFailureInHours, opt => opt.MapFrom(src => src.FailIntervalUnit))
            .ForMember(dest => dest.WeibullLocation, opt => opt.MapFrom(src => src.FailWeibullLocation))
            .ForMember(dest => dest.RateTrend, opt => opt.MapFrom(src => (FailureRateTrend)src.FailRateId))
            .ForMember(dest => dest.RateDistributionCalculationMethod, opt => opt.MapFrom(src => (FailureRateDistributionCalculationMethod?)src.FailDistributionId))
            .ForMember(dest => dest.Visibility, opt => opt.MapFrom(src => (FailureVisibility?)src.FailRiskTypeId))
            .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src.ToEntityMetaData()))
            .ReverseMap()
            .ForMember(src => src.FailDistributionId, opt => opt.MapFrom(dest => (int?)dest.RateDistributionCalculationMethod))
            .ForMember(src => src.FailRateId, opt => opt.MapFrom(dest => (int)dest.RateTrend))
            .ForMember(src => src.FailRiskTypeId, opt => opt.MapFrom(dest => (int?)dest.Visibility));

        mc.CreateMap<Data.Entities.AM.LookupFailCat, FailureCategory>()
           .ReverseMap();

        mc.CreateMap<Data.Entities.AM.LookupAdditionalData, AdditionalDataModel>()
            .ReverseMap()
            .ForMember(dest => dest.AdditionalDataId, opt => opt.Ignore());

        mc.CreateMap<Data.Entities.AM.LookupExecutor, ExecutorModel>()
            .ReverseMap()
            .ForMember(dest => dest.ExecutorId, opt => opt.Ignore());

        mc.CreateMap<Data.Entities.AM.LookupInitiator, InitiatorModel>()
            .ReverseMap()
            .ForMember(dest => dest.InitiatorId, opt => opt.Ignore());

        mc.CreateMap<Data.Entities.AM.LookupMxPolicy, PolicyModel>().ReverseMap();

        mc.CreateMap<Data.Entities.AM.LookupGridColumn, GridColumnModel>()
            .ForMember(dest => dest.FieldType, opt => opt.MapFrom(src => (FieldType)src.FieldType))
            .ReverseMap();

        mc.CreateMap<Data.Entities.AM.Lookup, LookupModel>().ReverseMap();

        mc.CreateMap<Data.Entities.AM.LookupSettings, LookupSettingModel>()
            .AfterMap((_, dest) => dest.SetSettingsType())
            .ReverseMap();

        mc.CreateMap<Data.Entities.AM.LookupUserDefined, LookupUserDefinedModel>().ReverseMap();

        mc.CreateMap<Data.Entities.AM.LookupIntervalUnit, IntervalUnitModel>()
            .ForMember(dest => dest.UnitsPerYear, opt => opt.MapFrom(src => src.IntUnitCalculationKey))
            .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src.ToEntityMetaData()))
            .ReverseMap()
            .ForMember(src => src.IntUnitId, opt => opt.Ignore());

        mc.CreateMap<Data.Entities.AM.LookupUserDefined, GenericObjectLevel>()
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.UserDefinedShortDescription))
            .ForMember(dest => dest.Level, opt => opt.MapFrom(src => src.UserDefinedValue))
            .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src.ToEntityMetaData()))
            .ReverseMap();
    }

    private static EntityMetadata ToEntityMetaData(this Data.Entities.AM.LookupIntervalUnit intervalUnit)
    {
            return new EntityMetadata
            {
                LastUpdatedByUsername = intervalUnit.IntUnitModifiedBy,
                LastUpdatedOn = intervalUnit.IntUnitDateModified
            };
        }

    private static EntityMetadata ToEntityMetaData(this Data.Entities.AM.LookupFailMode failMode)
    {
            return new EntityMetadata
            {
                LastUpdatedByUsername = failMode.FailModifiedBy,
                LastUpdatedOn = failMode.FailDateModified
            };
        }

    private static EntityMetadata ToEntityMetaData(this Data.Entities.AM.LookupUserDefined userDefined)
    {
            return new EntityMetadata
            {
                LastUpdatedByUsername = userDefined.UserDefinedModifiedBy,
                LastUpdatedOn = userDefined.UserDefinedDateModified
            };
        }
}
