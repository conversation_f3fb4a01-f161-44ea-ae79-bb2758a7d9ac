﻿using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.ABS;
using AutoMapper;
using System;

namespace AMprover.BlazorApp.Configuration.AutoMapper;

public static class AssetMapping
{
    public static void CreateAssetMapping(this IMapperConfigurationExpression mc)
    {
            mc.CreateMap<Data.Entities.AM.Si, AssetModel>()
                .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.SiName))
                .ForMember(dest => dest.ParentCode, opt => opt.MapFrom(src => src.SiParentName))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.SiDescription))
                .ForMember(dest => dest.ParentId, opt => opt.MapFrom(src => src.SiPartOf))
                .ForMember(dest => dest.YearOfOrigin, opt => opt.MapFrom(src => src.SiYear.DateTimeToYear()))
                .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src.SiModifiedBy.ToEntityMetaData(src.SiDateModified)))
                .ForMember(dest => dest.CategoryId, opt => opt.MapFrom(src => src.SiCategory))
                .ForMember(dest => dest.Category, opt => opt.MapFrom(src => src.SiCategoryNavigation.UserDefinedShortDescription))
                .ForMember(dest => dest.PickAssets, opt => opt.MapFrom(src => src.PickSi))
                .ReverseMap()
                .ForMember(src => src.SiCategoryNavigation, opt => opt.Ignore())
                .ForMember(src => src.SiModifiedBy, opt => opt.MapFrom(dest => dest.Metadata.LastUpdatedByUsername))
                .ForMember(src => src.SiDateModified, opt => opt.MapFrom(dest => dest.Metadata.LastUpdatedOn))
                .ForMember(src => src.SiYear, opt => opt.MapFrom(dest => dest.YearOfOrigin.YearToDateTime()))
                .ForMember(src => src.SiCategory, opt => opt.MapFrom(dest => dest.CategoryId));

            mc.CreateMap<Data.Entities.AM.PickSi, PickAssetModel>()
                .ForMember(dest => dest.AssetId, opt => opt.MapFrom(src => src.PckSiSiId))
                .ForMember(dest => dest.Asset, opt => opt.MapFrom(src => src.PckSiSi))
                .ForMember(dest => dest.RiskId, opt => opt.MapFrom(src => src.PckSiMrbId))
                .ForMember(dest => dest.Risk, opt => opt.MapFrom(src => src.PckSiMrb))
                .ReverseMap();
        }

    // Set min year to 1753 due to: 'SqlDateTime overflow. Must be between 1/1/1753 12:00:00 AM and 12/31/9999 11:59:59 PM'
    private static DateTime? YearToDateTime(this int? year) =>
        year > 0 ? new DateTime(Math.Max(year.Value, 1753), 1, 1) : null;

    private static int? DateTimeToYear(this DateTime? datetime) => datetime?.Year;
}