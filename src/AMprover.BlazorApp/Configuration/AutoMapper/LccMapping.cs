﻿using AMprover.BusinessLogic.Models.LCC;
using AMprover.Data.Entities.AM;
using AutoMapper;

namespace AMprover.BlazorApp.Configuration.AutoMapper;

public static class LccMapping
{
    public static void CreateLccMapping(this IMapperConfigurationExpression mc)
    {
        mc.CreateMap<VdmxlModel, Vdmxl>()
            .ForMember(dest => dest.VdmId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.VdmLccId, opt => opt.MapFrom(src => src.LccId))
            .ForMember(dest => dest.Lcc, opt => opt.Ignore())
            .ReverseMap();

        mc.CreateMap<LccModel, Lcc>()
            .ForMember(dest => dest.LccRiskObject, opt => opt.MapFrom(src => src.RiskObjectId))
            .ForMember(dest => dest.LccId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.RiskObject, opt => opt.Ignore())
            .ForMember(dest => dest.LccScenario, opt => opt.Ignore())
            .ForMember(dest => dest.LccChildObject1, opt => opt.MapFrom(src => src.ChildObject1Id))
            .ForMember(dest => dest.LccChildObject2, opt => opt.MapFrom(src => src.ChildObject2Id))
            .ForMember(dest => dest.LccChildObject3, opt => opt.MapFrom(src => src.ChildObject3Id))
            .ForMember(dest => dest.LccChildObject4, opt => opt.MapFrom(src => src.ChildObject4Id))
            .ForMember(dest => dest.VdmxlItem, opt => opt.MapFrom(src => src.Vdmxl))
            .ReverseMap()
            .ForMember(dest => dest.RiskObjectId, opt => opt.MapFrom(src => src.LccRiskObject))
            .ForMember(dest => dest.ChildObject1Id, opt => opt.MapFrom(src => src.LccChildObject1))
            .ForMember(dest => dest.ChildObject2Id, opt => opt.MapFrom(src => src.LccChildObject2))
            .ForMember(dest => dest.ChildObject3Id, opt => opt.MapFrom(src => src.LccChildObject3))
            .ForMember(dest => dest.ChildObject4Id, opt => opt.MapFrom(src => src.LccChildObject4))
            .ForMember(dest => dest.ChildObject, opt => opt.MapFrom(src => src.ChildObject))
            .ForMember(dest => dest.ChildObject1, opt => opt.MapFrom(src => src.ChildObject1))
            .ForMember(dest => dest.ChildObject2, opt => opt.MapFrom(src => src.ChildObject2))
            .ForMember(dest => dest.ChildObject3, opt => opt.MapFrom(src => src.ChildObject3))
            .ForMember(dest => dest.ChildObject4, opt => opt.MapFrom(src => src.ChildObject4))
            .ForMember(dest => dest.RiskObject, opt => opt.MapFrom(src => src.RiskObject))
            .AfterMap((_, ml) => ml.CreateGraph());
        // For the RiskOrganizer we need to append Lcc to RiskObjects, but we want very minimal data.
        // since you can't place a Base model inside a derivedModel Property we just map twice
        // First from DB to baseModel, and then from baseModel to "Full" Model
        mc.CreateMap<LccBaseModel, Lcc>().ReverseMap()
            .ForMember(dest => dest.RiskObjectId, opt => opt.MapFrom(src => src.LccRiskObject));
        mc.CreateMap<LccBaseModel, LccModel>().ReverseMap();
        mc.CreateMap<LccModel, LccPlainModel>();

        mc.CreateMap<LccPlainModel, Lcc>()
            .ForMember(dest => dest.LccName, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.LccRemark, opt => opt.MapFrom(src => src.Remark))
            .ForMember(dest => dest.LccMaxYears, opt => opt.MapFrom(src => src.MaxYears))
            .ForMember(dest => dest.LccStartYear, opt => opt.MapFrom(src => src.StartYear))
            .ForMember(dest => dest.LccAge, opt => opt.MapFrom(src => src.Age));

        mc.CreateMap<Lccdetail, LccDetailModel>();
        mc.CreateMap<LccDetailModel, Lccdetail>()
            .ForMember(dest => dest.LccDetLccId, opt => opt.MapFrom(src => src.LccId));

        mc.CreateMap<LcceffectDetail, LccEffectDetailModel>().ReverseMap();
    }
}
