﻿using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AutoMapper;

namespace AMprover.BlazorApp.Configuration.AutoMapper;

public static class ScenarioMapping
{
    public static void CreateScenarioMapping(this IMapperConfigurationExpression mc)
    {
            mc.CreateMap<Data.Entities.AM.Scenario, ScenarioModel>()
             .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.ScenDescr))
             .ForMember(dest => dest.ShortKey, opt => opt.MapFrom(src => src.ScenShrtKey))
             .ForMember(dest => dest.Prerequisites, opt => opt.MapFrom(src => src.ScenStartPoint))
             .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.ScenStatus))
             .ForMember(dest => dest.StatusNavigation, opt => opt.MapFrom(src => src.ScenStatusNavigation))
             .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src.ToEntityMetaData()))
             .ReverseMap()
             .ForMember(dest => dest.RiskObject, opt => opt.Ignore())
             .ForMember(dest => dest.ScenStatusNavigation, opt => opt.Ignore());

            mc.CreateMap<ScenarioModel, ScenarioModelFlat>();

            mc.CreateMap<Data.Entities.AM.Scenario, ScenarioModelFlat>()
               .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.ScenDescr))
               .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.ScenDescr))
               .ForMember(dest => dest.ShortKey, opt => opt.MapFrom(src => src.ScenShrtKey))
               .ForMember(dest => dest.Prerequisites, opt => opt.MapFrom(src => src.ScenStartPoint))
               .ForMember(dest => dest.Metadata, opt => opt.MapFrom(src => src.ToEntityMetaData()))
               .ReverseMap();
        }
}