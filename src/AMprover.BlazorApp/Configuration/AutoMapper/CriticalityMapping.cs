﻿using AMprover.BusinessLogic.Models.Criticality;
using AutoMapper;

namespace AMprover.BlazorApp.Configuration.AutoMapper;

public static class CriticalityMapping
{
    public static void CreateCriticalityMapping(this IMapperConfigurationExpression mc)
    {
            // Normal mapping from DB To BusinessLogic
            mc.CreateMap<Data.Entities.AM.CriticalityRanking, CriticalityRankingModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.CritId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.CritName))
                .ForMember(dest => dest.AssetCode, opt => opt.MapFrom(src => src.CritSiName))
                .ForMember(dest => dest.Asset, opt => opt.MapFrom(src => src.CritSi))
                .ReverseMap()
                .ForMember(src => src.CritSi, opt => opt.Ignore());

            // Used for import, Map imported properties onto existing DB model
            mc.CreateMap<CriticalityRankingModelFlat, Data.Entities.AM.CriticalityRanking>()
                .ForMember(dest => dest.CritId, opt => opt.Ignore())
                .ForMember(dest => dest.CritSiName, opt => opt.Ignore());

            // Business Logic models to other business Logic Model
            mc.CreateMap<CriticalityRankingModel, CriticalityRankingModelFlat>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.AssetCode, opt => opt.MapFrom(src => src.AssetCode))
                .ForMember(dest => dest.AssetDescription, opt => opt.MapFrom(src => src.Asset.Name))
                .ForMember(dest => dest.AssetCategory, opt => opt.MapFrom(src => src.Asset.Category))
                .ForMember(dest => dest.AssetCategoryId, opt => opt.MapFrom(src => src.Asset.CategoryId))
                .ForMember(dest => dest.AssetMtbf, opt => opt.MapFrom(src => src.Asset.Mtbf))
                .ReverseMap();
        }
}