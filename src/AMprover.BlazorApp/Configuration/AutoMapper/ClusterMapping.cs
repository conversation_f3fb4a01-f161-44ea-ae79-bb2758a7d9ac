using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AutoMapper;

namespace AMprover.BlazorApp.Configuration.AutoMapper;

public static class ClusterMapping
{
    public static void CreateClusterMapping(this IMapperConfigurationExpression mc)
    {
            mc.CreateMap<ClusterModel, Data.Entities.AM.Cluster>()
                .ForMember(dest => dest.Task, opt => opt.MapFrom(src => src.Tasks))
                .ForMember(dest => dest.ClusterCost, opt => opt.MapFrom(src => src.Costs))
                .ForMember(dest => dest.ClusterTaskPlan, opt => opt.MapFrom(src => src.TaskPlans))
                .ReverseMap()
                .AfterMap((_, dest) => dest.CalculateAllCosts());

            mc.CreateMap<ClusterPlainModel, Data.Entities.AM.Cluster>();
            mc.CreateMap<ClusterModel, ClusterPlainModel>();

            mc.CreateMap<ClusterCostPlainModel, Data.Entities.AM.ClusterCost>();
            mc.CreateMap<ClusterCostModel, ClusterCostPlainModel>();

            mc.CreateMap<Data.Entities.AM.ClusterCost, ClusterCostModel>()
                .AfterMap((_, dest) => dest.CalculateCosts())
                .ReverseMap();

            mc.CreateMap<Data.Entities.AM.ClusterTaskPlan, ClusterTaskPlanModel>()
                .ForMember(dest => dest.AssetId, opt => opt.MapFrom(src => src.CltpSiId))
                .ForMember(dest => dest.Asset, opt => opt.MapFrom(src => src.CltpSi))
                .ReverseMap()
                .ForMember(dest => dest.CltpSi, opt => opt.MapFrom(src => src.AssetId))
                .ForMember(dest => dest.CltpRisk, opt => opt.Ignore())
                .ForMember(dest => dest.CltpCluster, opt => opt.Ignore())
                .ForMember(dest => dest.CltpSi, opt => opt.Ignore())
                .ForMember(dest => dest.CltpTask, opt => opt.Ignore());

            mc.CreateMap<ClusterTaskPlanModel, ClusterTaskPlanWithPlainObjectsModel>()
                .ForMember(dest => dest.Task, opt => opt.MapFrom(src => src.Task.Name))
                .ForMember(dest => dest.Interval, opt => opt.Ignore())
                .ForMember(dest => dest.ShiftStartDate, opt => opt.MapFrom(src => src.ShiftStartDate))
                .ForMember(dest => dest.ShiftEndDate, opt => opt.MapFrom(src => src.ShiftEndDate))
                .ReverseMap();

            mc.CreateMap<ClusterTaskPlanModel, ClusterTaskPlanModelWithExtraTaskProperties>()
                .ForMember(dest => dest.Task, opt => opt.MapFrom(src => src.Task))
                .ForMember(dest => dest.Interval, opt => opt.MapFrom(src => src.Task.Interval))
                .ForMember(dest => dest.IntervalUnit, opt => opt.MapFrom(src => src.Task.IntervalUnit.Name))
                .ForMember(dest => dest.Initiator, opt => opt.MapFrom(src => src.Task.Initiator.Name))
                .ForMember(dest => dest.Executor, opt => opt.MapFrom(src => src.Task.Executor.Name))
                .ForMember(dest => dest.Policy, opt => opt.MapFrom(src => src.Task.Executor.Name))
                .ForMember(dest => dest.ShiftStartDate, opt => opt.MapFrom(src => src.ShiftStartDate))
                .ForMember(dest => dest.ShiftEndDate, opt => opt.MapFrom(src => src.ShiftEndDate))
                .ReverseMap();

            mc.CreateMap<TaskModel, ClusterTaskModel>()
                .ForMember(dest => dest.System, opt => opt.MapFrom(src => src.Risk.System))
                .ForMember(dest => dest.SystemId, opt => opt.MapFrom(src => src.Risk.System.Id))
                .ForMember(dest => dest.Component, opt => opt.MapFrom(src => src.Risk.Component))
                .ForMember(dest => dest.ComponentId, opt => opt.MapFrom(src => src.Risk.Component.Id))
                .ForMember(dest => dest.Assembly, opt => opt.MapFrom(src => src.Risk.Assembly))
                .ForMember(dest => dest.AssemblyId, opt => opt.MapFrom(src => src.Risk.Assembly.Id))
                .ReverseMap();
        }
}