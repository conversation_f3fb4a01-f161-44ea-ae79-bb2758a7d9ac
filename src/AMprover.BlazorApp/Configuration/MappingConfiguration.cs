﻿using System;
using AMprover.BusinessLogic.Models;
using AMprover.BusinessLogic.Models.PortfolioSetup;
using AMprover.BusinessLogic.Models.RiskAnalysis;
using AMprover.BusinessLogic.Models.Cluster;
using AMprover.BusinessLogic.XMLEntities;
using AMprover.Data.Entities.AM;
using AMprover.Data.Entities.Identity;
using AutoMapper;
using Microsoft.Extensions.DependencyInjection;
using AMprover.Data.Entities.AM.SP;
using EntityObject = AMprover.Data.Entities.AM.Object;
using AMprover.BusinessLogic.Models.Rams;
using AMprover.BusinessLogic.Models.Reports;
using AMprover.Data.Entities.AM.SP.Reports;
using AMprover.BlazorApp.Configuration.AutoMapper;
using AMprover.BusinessLogic.Extensions;
using AMprover.BusinessLogic.Models.Import;

namespace AMprover.BlazorApp.Configuration;

public static class MappingConfiguration
{
    public static readonly string[] MappingPrefixes =
    {
        "Ams", //Lookup settings
        "Clc", //cluster cost
        "Clust", //cluster
        "Cltp", //Cluster task plan
        "CmnTask", //Common task
        "CmnCost", //Common cost
        "Crit", //CriticalityRanking
        "Ctc", //Common task cost
        "DerMod", //Derived modified
        "Executor", //Executor
        "Fail", //Failure mode
        "FailCat", // Failure Categories
        "Fmeca", //Fmeca/riskmatrixtemplate
        "Infl", //inflation group
        "Initiator", //Initiator
        "IntUnit", //Interval unit
        "Lcc", //LCC
        "LccEfct", //LCCEffectDetail
        "LccDet", //LCCDetail
        "Lookup", //Lookups
        "Mrb", //MRB/Risks
        "Obj", //Object
        "PckSi", //PickSI
        "Pol", //LookupMXPolicy
        "Page", // PageNavigation
        "Rams", //Rams (block and container)
        "RamsDg", //RamsDiagram
        "RiskObj", //Risk objects
        "Scen", //Scenario's
        "Si", //SignificantItem
        "Spare", //Spare
        "MrbImage", //MRB/Risk screenshots
        "Tsk", //Task
        "UserDefined", //Lookup user defined
        "Wp", //Work packages
        "Vdm", //VDMXL
        "SapaDet", //SapaDetails
        "SapaYear", //SapaYear
        "Sapa", //Sapa
        "Dep", //Department
        "Atch", //Attachment
        "AtchCat", //AttachmentCategory
        "AdditionalData" // LookupAdditionalData
    };

    public static void RegisterMappings(this IServiceCollection services)
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            //Making mapping automated by recognizing prefixes in entity naming
            mc.RecognizePrefixes(MappingPrefixes);
            mc.RecognizeDestinationPrefixes(MappingPrefixes);

            // Custom Extensions Methods for ObjectTypes
            mc.CreateScenarioMapping();
            mc.CreateRiskObjectMapping();
            mc.CreateRiskModelMapping();
            mc.CreateLccMapping();
            mc.CreateLookupMapping();
            mc.CreateClusterMapping();
            mc.CreateAssetMapping();
            mc.CreateCriticalityMapping();
            mc.CreateSapaMapping();

            //Mapping of Stored Procedure
            mc.CreateMap<RiskWithObjects, RiskWithPlainObjectsModel>().ReverseMap();
            mc.CreateMap<TaskWithObjects, TaskWithPlainObjectsModel>().ReverseMap();
            mc.CreateMap<ClusterTaskWithObjects, ClusterTaskWithPlainObjectsModel>().ReverseMap();
            mc.CreateMap<ClusterTaskPlanWithObjects, ClusterTaskPlanWithPlainObjectsModel>().ReverseMap();
            mc.CreateMap<FunctionalTreeReportItem, FunctionalTreeReportItemModel>().ReverseMap();
            mc.CreateMap<CommonActionReportItem, CommonActionReportItemModel>().ReverseMap();
            mc.CreateMap<SignificantItemReportItem, SignificantItemReportItemModel>().ReverseMap();
            mc.CreateMap<ClusterReportItem, ClusterReportItemModel>().ReverseMap();

            mc.CreateMap<EntityObject, ObjectModel>()
                .ForMember(dest => dest.MetaData, opt => opt.MapFrom(src => src.ToEntityMetaData()))
                .ReverseMap();

            mc.CreateMap<RiskAndPreviousActionsReportItem, RiskAndPreviousActionsReportItemModel>()
                .ForMember(dest => dest.TskID, opt => opt.MapFrom(src => src.TskID))
                .ReverseMap();

            mc.CreateMap<RiskAnalysisReportItem, RiskAnalysisReportItemModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.TskId, opt => opt.MapFrom(src => src.TskId))
                .ForMember(dest => dest.Remarks, opt => opt.MapFrom(src => src.Remarks))
                .ForMember(dest => dest.SpareRemarks, opt => opt.MapFrom(src => src.SpareRemarks))
                .ForMember(dest => dest.TskRemark, opt => opt.MapFrom(src => src.TskRemark))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.TskDescription, opt => opt.MapFrom(src => src.TskDescription))
                .ReverseMap();

            mc.CreateMap<TaskPlanReportItem, TaskPlanReportItemModel>().ReverseMap();

            mc.CreateMap<DerivedModifiedModel, DerModified>().ReverseMap();

            mc.CreateMap<Fmeca, RiskMatrixTemplateModel>().AfterMap((_, r) => r.BuildMatrix());
            mc.CreateMap<RiskMatrixTemplateModel, Fmeca>().BeforeMap((r, _) => r.ToFmecaData());

            mc.CreateMap<Spare, SpareModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.SpareId))
                .ReverseMap();

            mc.CreateMap<Spare, SpareImportModel>()
                .ForMember(dest => dest.RiskId, opt => opt.MapFrom(src => src.SpareMrbId))
                .ForMember(dest => dest.NoOfSpares, opt => opt.MapFrom(src => src.SpareNoOfItems));

            mc.CreateMap<Workpackage, WorkPackageModel>()
                .ReverseMap()
                .ForMember(dest => dest.WpId, opt => opt.Ignore())
                .ForMember(dest => dest.WpExecutorNavigation, opt => opt.Ignore())
                .ForMember(dest => dest.WpIntervalUnitNavigation, opt => opt.Ignore());

            mc.CreateMap<Portfolio, PortfolioModel>().ReverseMap();

            mc.CreateMap<Task, TaskModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.TskId))
                .ForMember(dest => dest.InitiatorId, opt => opt.MapFrom(src => src.TskInitiator))
                .ForMember(dest => dest.Initiator, opt => opt.MapFrom(src => src.TskInitiatorNavigation))
                .ForMember(dest => dest.ClusterId, opt => opt.MapFrom(src => src.TskCluster))
                .ForMember(dest => dest.Cluster, opt => opt.MapFrom(src => src.TskClusterNavigation))
                .ForMember(dest => dest.ExecutorId, opt => opt.MapFrom(src => src.TskExecutor))
                .ForMember(dest => dest.Executor, opt => opt.MapFrom(src => src.TskExecutorNavigation))
                .ForMember(dest => dest.IntervalUnitId, opt => opt.MapFrom(src => src.TskIntervalUnit))
                .ForMember(dest => dest.IntervalUnit, opt => opt.MapFrom(src => src.TskIntervalUnitNavigation))
                .ForMember(dest => dest.WorkpackageId, opt => opt.MapFrom(src => src.TskWorkpackage))
                .ForMember(dest => dest.Policy, opt => opt.MapFrom(src => src.TskMxPolicyNavigation))
                .ForMember(dest => dest.WorkPackage, opt => opt.MapFrom(src => src.TskWorkpackageNavigation))
                .ForMember(dest => dest.SapaWorkpackage, opt => opt.MapFrom(src => src.TskSapaWorkpackageNavigation))
                .ForMember(dest => dest.SapaWorkpackageId, opt => opt.MapFrom(src => src.TskSapaWorkpackage))
                // Ignore Risk to prevent circular reference
                .ForMember(dest => dest.Risk, opt => opt.Ignore())
                .ForMember(dest => dest.TaskClusterCosts, opt => opt.MapFrom(src => src.ClusterCost))
                .ForMember(dest => dest.ChildTasks, opt => opt.MapFrom(src => src.TskChildren))
                .ForMember(dest => dest.Parent, opt => opt.MapFrom(src => src.TskParent))
                .ReverseMap()
                .ForMember(dest => dest.TskInitiatorNavigation, opt => opt.Ignore())
                .ForMember(dest => dest.TskClusterNavigation, opt => opt.Ignore())
                .ForMember(dest => dest.TskExecutorNavigation, opt => opt.Ignore())
                .ForMember(dest => dest.TskIntervalUnitNavigation, opt => opt.Ignore())
                .ForMember(dest => dest.TskMxPolicyNavigation, opt => opt.Ignore())
                .ForMember(dest => dest.TskWorkpackageNavigation, opt => opt.Ignore())
                .ForMember(dest => dest.TskSapaWorkpackageNavigation, opt => opt.Ignore())
                .ForMember(dest => dest.TskMrb, opt => opt.Ignore())
                .ForMember(dest => dest.TskMrbId, opt => opt.MapFrom(src => src.Risk.Id));

            mc.CreateMap<Task, TaskImportModel>()
                .ForMember(dest => dest.RiskId, opt => opt.MapFrom(src => src.TskMrbId))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.TskId))
                .ForMember(dest => dest.Remarks, opt => opt.MapFrom(src => src.TskRemark))
                .ForMember(dest => dest.Policy, opt => opt.MapFrom(src => src.TskMxPolicyNavigation.PolName))
                .ForMember(dest => dest.Initiator, opt => opt.MapFrom(src => src.TskInitiatorNavigation.InitiatorName))
                .ForMember(dest => dest.Executor, opt => opt.MapFrom(src => src.TskExecutorNavigation.ExecutorName))
                .ForMember(dest => dest.WorkPackage, opt => opt.MapFrom(src => src.TskWorkpackageNavigation.WpName))
                .ForMember(dest => dest.IntervalUnit,
                    opt => opt.MapFrom(src => src.TskIntervalUnitNavigation.IntUnitName))
                .ForMember(dest => dest.CommonAction, opt => opt.MapFrom(src => src.TskCommonAction.CmnTaskName));

            mc.CreateMap<CommonCostModel, CommonCost>().ReverseMap();

            mc.CreateMap<CommonTaskModel, CommonTask>()
                .ForMember(dest => dest.CmnTaskId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.CmnTaskInitiator, opt => opt.MapFrom(src => src.InitiatorId))
                .ForMember(dest => dest.CmnTaskExecutor, opt => opt.MapFrom(src => src.ExecutorId))
                .ForMember(dest => dest.CmnTaskWorkPackage, opt => opt.MapFrom(src => src.WorkPackageId))
                .ForMember(dest => dest.CmnTaskMxPolicy, opt => opt.MapFrom(src => src.MxPolicyId))
                .ForMember(dest => dest.CmnTaskIntervalUnit, opt => opt.MapFrom(src => src.IntervalUnitId))
                .ReverseMap()
                .ForMember(dest => dest.IntervalUnit, opt => opt.MapFrom(src => src.CmnTaskIntervalUnitNavigation))
                .ForMember(dest => dest.Initiator, opt => opt.MapFrom(src => src.CmnTaskInitiatorNavigation))
                .ForMember(dest => dest.Executor, opt => opt.MapFrom(src => src.CmnTaskExecutorNavigation))
                .ForMember(dest => dest.WorkPackageModel, opt => opt.MapFrom(src => src.CmnTaskWorkPackageNavigation))
                .ForMember(dest => dest.Policy, opt => opt.MapFrom(src => src.CmnTaskMxPolicyNavigation));

            mc.CreateMap<CommonTask, CommonActionImportModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.CmnTaskId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.CmnTaskName))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.CmnTaskDescription))
                .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.CmnTaskReferenceId))
                .ForMember(dest => dest.Strategy, opt => opt.MapFrom(src => src.CmnTaskMxPolicyNavigation.PolName))
                .ForMember(dest => dest.Interval, opt => opt.MapFrom(src => src.CmnTaskInterval))
                .ForMember(dest => dest.IntervalUnit,
                    opt => opt.MapFrom(src => src.CmnTaskIntervalUnitNavigation.IntUnitName))
                .ForMember(dest => dest.IntervalModifiable, opt => opt.MapFrom(src => src.CmnTaskIntervalModifiable))
                .ForMember(dest => dest.Initiator,
                    opt => opt.MapFrom(src => src.CmnTaskInitiatorNavigation.InitiatorName))
                .ForMember(dest => dest.InitiatorModifiable, opt => opt.MapFrom(src => src.CmnTaskInitiatorModifiable))
                .ForMember(dest => dest.Executor, opt => opt.MapFrom(src => src.CmnTaskExecutorNavigation.ExecutorName))
                .ForMember(dest => dest.ExecutorModifiable, opt => opt.MapFrom(src => src.CmnTaskExecutorModifiable))
                .ForMember(dest => dest.Workpackage, opt => opt.MapFrom(src => src.CmnTaskWorkPackageNavigation.WpName))
                .ForMember(dest => dest.WorkPackageModifiable,
                    opt => opt.MapFrom(src => src.CmnTaskWorkPackageModifiable))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.CmnTaskType))
                .ForMember(dest => dest.Costs, opt => opt.MapFrom(src => src.CmnTaskCosts))
                .ForMember(dest => dest.UnitType, opt => opt.MapFrom(src => src.CmnTaskUnitType.ToString()))
                .ForMember(dest => dest.CostsModifiable, opt => opt.MapFrom(src => src.CmnTaskCostModifiable))
                .ForMember(dest => dest.PriorityCode, opt => opt.MapFrom(src => src.CmnTaskPriorityCode))
                .ForMember(dest => dest.FilterRef, opt => opt.MapFrom(src => src.CmnTaskFilterRef));

            mc.CreateMap<CommonTaskCostModel, CommonTaskCost>().ReverseMap();
            mc.CreateMap<InflationGroupModel, LookupInflationGroup>().ReverseMap();

            mc.CreateMap<PageNavigation, PageNavigationModel>().ReverseMap();

            mc.CreateMap<TaskModel, ClusterTaskWithPlainObjectsModel>()
                .ForMember(dest => dest.Action, opt => opt.MapFrom(src => src.Name))
                .ReverseMap();

            mc.CreateMap<MrbImage, RiskImageModel>()
                .ForMember(dest => dest.Risk, opt => opt.MapFrom(src => src.MrbImageNavigation))
                .ReverseMap();

            mc.CreateMap<ClusterTree, ClusterTreeModel>()
                .ForMember(dest => dest.ScenarioChildTypeId, opt => opt.MapFrom(src => src.ScenarioChildType))
                .ForMember(dest => dest.RiskObjectId, opt => opt.MapFrom(src => src.RiskObjId))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id));

            //RAMS
            mc.CreateMap<RamsDiagram, RamsDiagramModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.RamsDgId))
                .ReverseMap()
                .ForMember(dest => dest.RamsDgId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Rams, opt => opt.Ignore())
                .ForMember(dest => dest.Scenario, opt => opt.Ignore());

            mc.CreateMap<Rams, RamsModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.RamsId))
                .ForMember(dest => dest.DiagramId, opt => opt.MapFrom(src => src.RamsDiagramId))
                .ForMember(dest => dest.NodeId, opt => opt.MapFrom(src => src.RamsNodeId))
                .ReverseMap()
                .ForMember(dest => dest.RamsId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.RamsDiagramId, opt => opt.MapFrom(src => src.DiagramId))
                .ForMember(dest => dest.RamsNodeId, opt => opt.MapFrom(src => src.NodeId))
                .ForMember(dest => dest.RamsDiagram, opt => opt.Ignore());

            mc.CreateMap<RamsFlatModel, Rams>()
                .ForMember(dest => dest.RamsNodeId, opt => opt.Condition(src => src.NodeId != Guid.Empty))
                .ForAllMembers(opt => opt.Condition((_, _, srcMember) =>
                    srcMember != null && !(srcMember is Guid guid && guid == Guid.Empty) &&
                    srcMember is not 0));

            mc.CreateMap<ReportFilter, ReportFilterModel>().ReverseMap();

            mc.CreateMap<Department, DepartmentModel>().ReverseMap();
            
            mc.CreateMap<UserAccount, UserAccountModel>();

            mc.CreateMap<Attachment, AttachmentModel>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.AtchId))
                .ForMember(dest => dest.RiskId, opt => opt.MapFrom(src => src.AtchMrbId))
                .ForMember(dest => dest.SapaId, opt => opt.MapFrom(src => src.AtchSapaId))
                .ForMember(dest => dest.TaskId, opt => opt.MapFrom(src => src.AtchTskId))
                .ForMember(dest => dest.RiskObjectId, opt => opt.MapFrom(src => src.AtchRiskObjectId))
                .ReverseMap()
                .ForMember(dest => dest.Risk, opt => opt.Ignore())
                .ForMember(dest => dest.RiskObject, opt => opt.Ignore())
                .ForMember(dest => dest.Sapa, opt => opt.Ignore())
                .ForMember(dest => dest.Task, opt => opt.Ignore());

            mc.CreateMap<AttachmentCategory, AttachmentCategoryModel>()
                .ReverseMap();

            //XML entities to models
            mc.RegisterXmlEntitiesToModels();
        });

        var mapper = mapperConfig.CreateMapper();
        services.AddSingleton(mapper);
    }

    private static void RegisterXmlEntitiesToModels(this IProfileExpression mc)
    {
        // XML Models in DB to usable models
        mc.CreateMap<FmecaSelColumn, RiskMatrixDataColumn>()
            .ForMember(dest => dest.ColumnIndex, opt => opt.MapFrom(src => src.ColIndex))
            .ReverseMap();

        mc.CreateMap<FmecaMainSelGrid, RiskMatrixDataGrid>().ReverseMap();
        mc.CreateMap<DataSubGrid, RiskMatrixDataGrid>().ReverseMap();
    }

    public static BusinessLogic.Enums.Status? GetStatusFromFromInt(int? val)
    {
        if (val == null) return null;
        return (BusinessLogic.Enums.Status) val;
    }

    public static int? GetIntFromStatus(BusinessLogic.Enums.Status? val)
    {
        if (val == null) return null;
        return (int) val;
    }
}
