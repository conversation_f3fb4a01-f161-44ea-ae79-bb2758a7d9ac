using AMprover.BusinessLogic;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Localization;
using Microsoft.JSInterop;
using System;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Services;

public class CookieService
{
    private readonly IJSRuntime _jsRuntime;
    private readonly NavigationManager _navigationManager;
    private readonly ILookupManager _lookupManager;

    public CookieService(
        IJSRuntime jsRuntime,
        NavigationManager navigationManager,
        ILookupManager lookupManager)
    {
        _jsRuntime = jsRuntime;
        _navigationManager = navigationManager;
        _lookupManager = lookupManager;
    }

    public async Task AddOrUpdateCookie(string uri)
    {
        var currentCookieValue = await _jsRuntime.InvokeAsync<string>(
            "ReadCookie.ReadCookie",
            CookieRequestCultureProvider.DefaultCookieName);

        var language = _lookupManager.GetLanguage();

        if (string.IsNullOrWhiteSpace(currentCookieValue) ||
            !currentCookieValue.Contains(language, StringComparison.InvariantCultureIgnoreCase))
        {
            await _jsRuntime.InvokeAsync<object>(
                "WriteCookie.WriteCookie",
                CookieRequestCultureProvider.DefaultCookieName,
                CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(language)),
                DateTime.Now.AddMonths(1));

            _navigationManager.NavigateTo(uri, true);
        }
    }
}