using Microsoft.AspNetCore.Components;
using <PERSON><PERSON><PERSON>;
using System.Threading.Tasks;

namespace AMprover.BlazorApp.Services;

public class TooltipHelper
{
    private readonly TooltipService _tooltipService;
    private string _elementWithMouse;

    public TooltipHelper(TooltipService tooltipService)
    {
            _tooltipService = tooltipService;
        }

    public void ShowTooltip(ElementReference element, string text, 
        TooltipPosition position = TooltipPosition.Top)
    {
            _elementWithMouse = element.Id;
            _tooltipService.Open(element, text, new TooltipOptions { 
                Position = position, 
                Duration = 6000 
            });
        }

    public async Task ShowTooltipWithDelay(ElementReference element, string text, 
        TooltipPosition position = TooltipPosition.Top)
    {
            _elementWithMouse = element.Id;
            await Task.Delay(2000);

            if (element.Id == _elementWithMouse)
                _tooltipService.Open(element, text, new TooltipOptions { 
                    Position = position, 
                    Duration = 6000 
                });
        }

    public async Task HideTooltip(ElementReference element)
    {
            if (element.Id == _elementWithMouse)
            {
                _elementWithMouse = string.Empty;
            }

            await Task.Delay(6000);

            if (string.IsNullOrEmpty(element.Id))
            {
                _tooltipService.Close();
            }
        }
}