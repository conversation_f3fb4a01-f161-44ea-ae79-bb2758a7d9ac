﻿using System;
using System.Threading.Tasks;
using AMprover.Data.Infrastructure;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Routing;

namespace AMprover.BlazorApp.Shared.Components;

public class ApplicationInsightsTracker : ComponentBase, IDisposable
{
    [Inject]
    private TelemetryClient _telemetryClient { get; init; }

    [Inject]
    private NavigationManager _navigationManager { get; init; }

    [Inject]
    private AuthenticationStateProvider _authStateProvider { get; init; }

    [Inject]
    private IAssetManagementPortfolioResolver _assetManagementPortfolioResolver { get; init; }

    protected override void OnInitialized()
    {
        Task<AuthenticationState> _currentAuthenticationStateTask;

        // Act on changes of Authentication state
        _authStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;

        _currentAuthenticationStateTask = _authStateProvider.GetAuthenticationStateAsync();

        OnAuthenticationStateChanged(_currentAuthenticationStateTask);
    }


    private void OnAuthenticationStateChanged(Task<AuthenticationState> authenticationStateTask)
    {
        // taken from: https://github.com/dotnet/aspnetcore/issues/29235 as own attempt event didn't fire
        _ = InvokeAsync(async () =>
        {
            var authState = await authenticationStateTask;

            var user = authState.User;

            var authenticated = user.Identity?.IsAuthenticated ?? false;

            if (authenticated)
            {
                _telemetryClient.Context.User.AuthenticatedUserId = user.Identity.Name.ToLower();
            }
            else
            {
                _telemetryClient.Context.User.AuthenticatedUserId = null;
            }
        });
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            _navigationManager.LocationChanged += NavigationManagerOnLocationChanged;
        }

        EnrichCurrentPortfolio();

        base.OnAfterRender(firstRender);
    }

    private void EnrichCurrentPortfolio()
    {
        if (_telemetryClient.IsEnabled())
        {
            try
            {
                var currentPortfolio = _assetManagementPortfolioResolver.GetCurrentPortfolio();
                if (currentPortfolio != null)
                {
                    var customDimensionKey = "AMproverPortfolio";
                    _telemetryClient.Context.GlobalProperties[customDimensionKey] = currentPortfolio.Name;
                }
            }
            catch (ObjectDisposedException)
            {
                // Ignore disposed context exceptions during navigation
            }
        }
    }

    private void NavigationManagerOnLocationChanged(object sender, LocationChangedEventArgs e)
    {
        Uri newLocationUri = new Uri(e.Location.ToLower(), UriKind.Absolute);
        _telemetryClient.TrackPageView( // can do a TrackPageView here, but that doesn't show up in default Application Insights UI :(
            new PageViewTelemetry
            {
                Name = newLocationUri.PathAndQuery,
                Url = newLocationUri
            });

        EnrichCurrentPortfolio();
    }


    public void Dispose()
    {
        _authStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
        _navigationManager.LocationChanged -= NavigationManagerOnLocationChanged;

    }
}
