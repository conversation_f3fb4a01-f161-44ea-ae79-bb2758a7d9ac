﻿@using AMprover.BlazorApp.Helpers
@using AMprover.Data.Constants
@inherits BaseLayout

@inject DropdownHelper DropdownHelper;

<AMproverToolTip />
<AuthorizeView>
    <Authorized>
        <AuthorizedHandler/>
        <RadzenComponents />
        <div class="sidebar @SideMenuClass">
            <NavMenu/>
        </div>

        <div class="main">
            <div class="top-row px-4 auth">
                <button type="button" id="sidebarCollapse" class="btn" @onclick=@ActivateMenu>
                    <i class="fas fa-align-left"></i>
                    <span></span>
                </button>
                <img src="\images\Logiciel_AMprover.png" height="100%" class="py-1"/>
                <span class="menu menu-title py-1 px-2">AMprover<sup>®</sup>&nbsp5.0 </span>
                <span class="menu menu-catchphrase py-1"> - Tool for Reliability Engineering & Value Optimization</span>
                <div class="menu ml-auto">
                    <PortfolioSwitcher/>
                    <LoginDisplay/>
                    <AuthorizeView Context="_" Roles="@RoleConstants.Administrators">
                        <Authorized>
                            <a href="/Administration">Administration</a>
                        </Authorized>
                    </AuthorizeView>
                </div>
            </div>

            <div class="content px-4">
                @Body

                <AMDropdownOptions @ref=@DropDownOptions />
            </div>

        </div>
    </Authorized>


    <NotAuthorized>
        @*Assumption is that this point is hit when user is NOT Authenticated at all.
            See App.razor as well, which also handles NotAuthorized for users that are authentication but are not having the valid authorization. *@
        <NonAuthorizedHandler />
    </NotAuthorized>
</AuthorizeView>

@code {
    public AMDropdownOptions DropDownOptions { get; set; }

    protected override void OnInitialized()
    {
        DropdownHelper.DropdownOptions = DropDownOptions;
        base.OnInitialized();
    }
}
