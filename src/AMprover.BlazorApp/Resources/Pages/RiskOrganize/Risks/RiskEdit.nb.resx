﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="RaMenuTitle" xml:space="preserve">
    <value>Tilnærming til Verdi Risikovurdering</value>
  </data>
  <data name="RaMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;I denne Verdi Risikovurderingsmodulen kan risiko reduseres med de mest optimale tiltakene (verdier) som skal tas&lt;/p&gt;
&lt;ol&gt;
            &lt;li&gt;Opprette en Verdi Risikovurdering ved å velge &lt;b&gt; Ny&lt;/b&gt; eller redigere en eksisterende Verdi Risikovurdering
            &lt;ol type='a'&gt;
                &lt;li&gt;Tildel Verdi Risikovurderingen under riktig funksjonsnivå i treet ved å velge de riktige &lt;b&gt;hierarkinivåene&lt;/b&gt; i rullegardinmenyen&lt;/li&gt;
                &lt;li&gt;Beskrive &lt;b&gt;funksjonen&lt;/b&gt;, aktivafunksjonaliteten, normer, kapasitet, spesifikasjon osv vurderes i forhold til (primære) proces&lt;/li&gt;
            &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Beskriv &lt;b&gt;Feilmekanisme&lt;/b&gt; ved å
&lt;ol type='a'&gt;
                    &lt;li&gt;Utnevne den dominerende &lt;b&gt;Feilmekanisme&lt;/b&gt; som er relatert til funksjonen&lt;/li&gt;
                    &lt;li&gt;Angi den spesifikke &lt;b&gt;Feilmodus&lt;/b&gt; som beskriver feilen&lt;/li&gt;
                    &lt;li&gt; Velg &lt;b&gt;observerbarheten og handlingsmåten&lt;/b&gt; som beskriver feilmodus&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Beskriv årsaken &lt;b&gt;til&lt;/b&gt; funksjonsfeilen&lt;/li&gt;
            &lt;li&gt;Beskrive &lt;b&gt;konsekvensene og effekten&lt;/b&gt; av feilen i forhold til (primær) prosessen&lt;/li&gt;
            &lt;li&gt;Projisere Feilmodusen og dens konsekvenser på &lt;b&gt;risikomatrisen uten at det&lt;/b&gt; iverksettes tiltak
&lt;ol type='a'&gt;
                    &lt;li&gt; Velg de forskjellige effektene&lt;/li&gt;
                    &lt;li&gt;velg &lt;b&gt;sannsynlighet&lt;/b&gt; (MTBF)&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
&lt;li&gt;Definer de beste &lt;b&gt;forebyggende tiltakene&lt;/b&gt;
                &lt;ol type='a'&gt;
                    &lt;li&gt;Definer nødvendige &lt;b&gt;reservedeler&lt;/b&gt;&lt;/li&gt;
                    &lt;li&gt;Definere andre &lt;b&gt;forebyggende handlinger&lt;/b&gt;&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Projisere feilmodusen og konsekvensene av &lt;b&gt;Verdi Risikomatrisen med handlinger&lt;/b&gt; som utføres&lt;/li&gt;
            &lt;li&gt;Prøv å &lt;b&gt;optimalisere&lt;/b&gt; handlingene i forhold til den angitte Verdir Risikoen&lt;/li&gt;
        &lt;/ol&gt;</value>
  </data>
  <data name="RaHeaderTxt" xml:space="preserve">
    <value>Vurdering av Verdi Risiko</value>
  </data>
  <data name="RaDeleteBtnTxt" xml:space="preserve">
    <value>Slette</value>
  </data>
  <data name="RaNewBtnTxt" xml:space="preserve">
    <value>Ny</value>
  </data>
  <data name="RaCopyBtnTxt" xml:space="preserve">
    <value>Kopi</value>
  </data>
  <data name="RaFailModeLbl" xml:space="preserve">
    <value>Feilmodus</value>
  </data>
  <data name="RaFailMechLbl" xml:space="preserve">
    <value>Feilmekanisme</value>
  </data>
  <data name="RaCauseLbl" xml:space="preserve">
    <value>Årsak</value>
  </data>
  <data name="RaConsequenceLbl" xml:space="preserve">
    <value>Konsekvens</value>
  </data>
  <data name="RaEffectLbl" xml:space="preserve">
    <value>Effekt</value>
  </data>
  <data name="RaFailCat1Lbl" xml:space="preserve">
    <value>Feil type</value>
  </data>
  <data name="RaFailCat2Lbl" xml:space="preserve">
    <value>Observerbarhet</value>
  </data>
  <data name="RaRemarksLbl" xml:space="preserve">
    <value>Merknader</value>
  </data>
  <data name="RaOptimizeTabTxt" xml:space="preserve">
    <value>Optimalisere</value>
  </data>
  <data name="RaFunctionLbl" xml:space="preserve">
    <value>Funksjon</value>
  </data>
  <data name="RaAssesmentStatusLbl" xml:space="preserve">
    <value>Status for Analyse</value>
  </data>
  <data name="RaMiscTabTxt" xml:space="preserve">
    <value>Diverse</value>
  </data>
  <data name="RaInitiatedByLbl" xml:space="preserve">
    <value>Igangsatt av</value>
  </data>
  <data name="RaOnLbl" xml:space="preserve">
    <value>På</value>
  </data>
  <data name="RaResponsibleLbl" xml:space="preserve">
    <value>Ansvarlig</value>
  </data>
  <data name="RaRiskBeforeTabTxt" xml:space="preserve">
    <value>Verdi Risiko uten handlinger</value>
  </data>
  <data name="RaRiskAfterTabTxt" xml:space="preserve">
    <value>Verdi Risiko etter handlinger</value>
  </data>
  <data name="RaValuePerYearTxt" xml:space="preserve">
    <value>Oversikt per år</value>
  </data>
  <data name="RaActionsTabTxt" xml:space="preserve">
    <value>(Forebyggende) handlinger</value>
  </data>
  <data name="RaSparesTabTxt" xml:space="preserve">
    <value>Reservedeler</value>
  </data>
  <data name="RaMtbfLbl" xml:space="preserve">
    <value>MTBF</value>
  </data>
  <data name="RaYearLbl" xml:space="preserve">
    <value>År</value>
  </data>
  <data name="RaShowValuesLbl" xml:space="preserve">
    <value>Vis Verdier (standard)</value>
  </data>
  <data name="RaShowCustValuesLbl" xml:space="preserve">
    <value>Vis egendefinerte Verdier</value>
  </data>
  <data name="RaShowPointsLbl" xml:space="preserve">
    <value>Vis Poeng</value>
  </data>
  <data name="RaSparesPMOTabTxt" xml:space="preserve">
    <value>Nåværende reservedeler</value>
  </data>
  <data name="RaActionsPMOTabTxt" xml:space="preserve">
    <value>Gjeldende handlinger</value>
  </data>
  <data name="RaPMOHeaderTxt" xml:space="preserve">
    <value>PMO</value>
  </data>
  <data name="RaSubOptimalRightTxt" xml:space="preserve">
    <value>Sub optimal, vurder færre tiltak</value>
  </data>
  <data name="RaSubOptimalLeftTxt" xml:space="preserve">
    <value>Sub optimal, vurder flere tiltak</value>
  </data>
  <data name="RaOptimalTxt" xml:space="preserve">
    <value>Optimal situasjon</value>
  </data>
  <data name="RcWCopyAllBtn" xml:space="preserve">
    <value>Kopier alle</value>
  </data>
  <data name="RcWRiskCountTxt" xml:space="preserve">
    <value>Risikoer valgt. Hvor vil du kopiere de merkede risikoene til?</value>
  </data>
  <data name="RaPrevActDetailsTxt" xml:space="preserve">
    <value>Detaljer forebyggende handlinger</value>
  </data>
  <data name="RaSparePartDetailsTxt" xml:space="preserve">
    <value>Detaljer for reservedeler</value>
  </data>
  <data name="RaSaveBtnTxt" xml:space="preserve">
    <value>Lagre</value>
  </data>
  <data name="RaRiskPmoTabTxt" xml:space="preserve">
    <value>Gjeldende handlinger for Verdi Risiko</value>
  </data>
  <data name="RaComparisonLeftTxt" xml:space="preserve">
    <value>Risiko: vurder flere tiltak</value>
  </data>
  <data name="RaComparisonRightTxt" xml:space="preserve">
    <value>Risiko: vurder færre tiltak</value>
  </data>
  <data name="RaComparisonTxt" xml:space="preserve">
    <value>Risiko: Optimal situasjon</value>
  </data>
  <data name="RaRiskOptimumPreventiveActionsAYearTxt" xml:space="preserve">
    <value>Indikasjon på optimale handlinger</value>
  </data>
  <data name="RaComparisonPmoRightTxt" xml:space="preserve">
    <value>PMO: Optimal situasjon</value>
  </data>
  <data name="RaComparisonPmoLeftTxt" xml:space="preserve">
    <value>PMO: Ingen forbedring</value>
  </data>
  <data name="RaComparisonPmoTxt" xml:space="preserve">
    <value>PMO: Liten forbedring</value>
  </data>
  <data name="RaDeleteRiskWarning" xml:space="preserve">
    <value>Advarsel: Denne handlingen kan ikke angre</value>
  </data>
  <data name="RaBtnImpRisksorTasks" xml:space="preserve">
    <value>Importere</value>
  </data>
  <data name="RaOverrideCostsTitle" xml:space="preserve">
    <value>Forebyggende handlinger inneholder klyngekostnader</value>
  </data>
  <data name="RaOverrideCostsText" xml:space="preserve">
    <value>Vil du overstyre kostnadene fra de estimerte kostnadene?</value>
  </data>
  <data name="RaModifiedByLbl" xml:space="preserve">
    <value>Endret av</value>
  </data>
  <data name="RaRiskTabTxt" xml:space="preserve">
    <value>Risiko</value>
  </data>
  <data name="RaWoActionsTxt" xml:space="preserve">
    <value>Uten (forebyggende) handlinger</value>
  </data>
  <data name="RaVrTxt" xml:space="preserve">
    <value>Verdi Risiko</value>
  </data>
  <data name="RaActTxt" xml:space="preserve">
    <value>Handlinger</value>
  </data>
  <data name="RaTotTxt" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="RaWithActionsTxt" xml:space="preserve">
    <value>Med (forebyggende) handlinger</value>
  </data>
  <data name="RaWithActionsPMOTabTxt" xml:space="preserve">
    <value>Med Gjeldende handlinger</value>
  </data>
  <data name="RiskCosts" xml:space="preserve">
    <value>Handlinger kostnader</value>
  </data>
  <data name="RaValueRiskCosts" xml:space="preserve">
    <value>Kostnader Verdi Risiko</value>
  </data>
  <data name="RaCSIRrTxt" xml:space="preserve">
    <value>CSIR punkter</value>
  </data>
  <data name="RaWTotTxt" xml:space="preserve">
    <value>Vektede poeng</value>
  </data>
  <data name="RaCSIRwTxt" xml:space="preserve">
    <value>Motstandsnivå</value>
  </data>
  <data name="RaDirectRiskCosts" xml:space="preserve">
    <value>Direkte risikokostnad</value>
  </data>
  <data name="RaActionCosts" xml:space="preserve">
    <value>Handlinger kostnader</value>
  </data>
  <data name="RaAttachmentTabTxt" xml:space="preserve">
    <value>Vedlegg</value>
  </data>
  <data name="RaRiskSortOrder" xml:space="preserve">
    <value />
  </data>
  <data name="RaAdditionalDataLbl" xml:space="preserve">
    <value />
  </data>
</root>