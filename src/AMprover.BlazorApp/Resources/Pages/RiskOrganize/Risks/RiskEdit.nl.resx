﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="RaMenuTitle" xml:space="preserve">
    <value>Aanpak voor de analyse van Waarde Risico</value>
  </data>
  <data name="RaMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;In deze Waarde Risico Analyse module kunnen risico's worden gemitigeerd middels de meest optimale te nemen maatregelen (Waarden)&lt;/p&gt;
&lt;ol&gt;
            &lt;li&gt;Maak een Waarde Risico Analyse door &lt;b&gt;Nieuw&lt;/b&gt; te selecteren of bewerk een bestaande Waarde Risico Analyse
            &lt;ol type='a'&gt;
                &lt;li&gt;Wijs de Waarde Risico Analyse toe aan het juiste functionele niveau in de boom door de juiste &lt;b&gt;Generieke Hiërarchieniveaus&lt;/b&gt; te selecteren in het pulldown menu&lt;/li&gt;
                &lt;li&gt;Beschrijf de &lt;b&gt;functie&lt;/b&gt;, functionaliteit van activa, normen, capaciteit, specificatie enz. die wordt beoordeeld in relatie tot het (primaire) proces&lt;/li&gt;
            &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Beschrijf het &lt;b&gt;Faalmechanisme&lt;/b&gt; door
&lt;ol type='a'&gt;
                    &lt;li&gt;Het dominante &lt;b&gt;Faalmechanisme&lt;/b&gt; aan te wijzen, die gerelateerd is aan de functie&lt;/li&gt;
                    &lt;li&gt;Geef de specifieke &lt;b&gt;Faalmode&lt;/b&gt; op, dat de fout beschrijft&lt;/li&gt;
                    &lt;li&gt;Selecteer de  &lt;b&gt;Waarneembaarheid en Wijze van optreden&lt;/b&gt; die de Faalmode beschrijven&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Beschrijf de &lt;b&gt;oorzaak&lt;/b&gt; van de functionele storing&lt;/li&gt;
            &lt;li&gt;Beschrijf de &lt;b&gt;gevolgen en het effect&lt;/b&gt; van de storing in relatie tot het (primaire) proces&lt;/li&gt;
            &lt;li&gt;Projecteer de Faal Mode en de gevolgen ervan op de &lt;b&gt;Waarde Risico matrix zonder dat er (preventieve) acties&lt;/b&gt; worden ondernomen
&lt;ol type='a'&gt;
                    &lt;li&gt;Selecteer de verschillende effecten&lt;/li&gt;
                    &lt;li&gt;Selecteer de &lt;b&gt;kans van optreden&lt;/b&gt; (MTBF)&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
&lt;li&gt;Definieer de beste &lt;b&gt;preventieve acties&lt;/b&gt;
                &lt;ol type='a'&gt;
                    &lt;li&gt;Definieer de &lt;b&gt;benodigde reserveonderdelen&lt;/b&gt;&lt;/li&gt;
                    &lt;li&gt;Definieer andere &lt;b&gt;preventieve acties &lt;/b&gt;&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Projecteer de Faal Modus en de gevolgen ervan op de &lt;b&gt;Waarde Risico matrix met acties&lt;/b&gt; die worden ondernomen&lt;/li&gt;
            &lt;li&gt;Probeer de acties met betrekking tot het opgegeven risico te &lt;b&gt;optimaliseren&lt;/b&gt;&lt;/li&gt;
        &lt;/ol&gt;</value>
  </data>
  <data name="RaHeaderTxt" xml:space="preserve">
    <value>Waarde Risico Analyse</value>
  </data>
  <data name="RaDeleteBtnTxt" xml:space="preserve">
    <value>Verwijder</value>
  </data>
  <data name="RaNewBtnTxt" xml:space="preserve">
    <value>Nieuw</value>
  </data>
  <data name="RaCopyBtnTxt" xml:space="preserve">
    <value>Kopieer</value>
  </data>
  <data name="RaFailModeLbl" xml:space="preserve">
    <value>Faalmode</value>
  </data>
  <data name="RaFailMechLbl" xml:space="preserve">
    <value>Faalmechanisme</value>
  </data>
  <data name="RaCauseLbl" xml:space="preserve">
    <value>Oorzaak</value>
  </data>
  <data name="RaConsequenceLbl" xml:space="preserve">
    <value>Gevolg</value>
  </data>
  <data name="RaEffectLbl" xml:space="preserve">
    <value>Effect</value>
  </data>
  <data name="RaFailCat1Lbl" xml:space="preserve">
    <value>Faaltype</value>
  </data>
  <data name="RaFailCat2Lbl" xml:space="preserve">
    <value>Waarneembaarheid</value>
  </data>
  <data name="RaRemarksLbl" xml:space="preserve">
    <value>Opmerkingen</value>
  </data>
  <data name="RaOptimizeTabTxt" xml:space="preserve">
    <value>Optimaliseer</value>
  </data>
  <data name="RaFunctionLbl" xml:space="preserve">
    <value>Functie</value>
  </data>
  <data name="RaAssesmentStatusLbl" xml:space="preserve">
    <value>Analyse Status</value>
  </data>
  <data name="RaMiscTabTxt" xml:space="preserve">
    <value>Divers</value>
  </data>
  <data name="RaInitiatedByLbl" xml:space="preserve">
    <value>Geïnitieerd door</value>
  </data>
  <data name="RaOnLbl" xml:space="preserve">
    <value>Op</value>
  </data>
  <data name="RaResponsibleLbl" xml:space="preserve">
    <value>Verantwoordelijk</value>
  </data>
  <data name="RaRiskBeforeTabTxt" xml:space="preserve">
    <value>Waarde Risico zonder acties</value>
  </data>
  <data name="RaRiskAfterTabTxt" xml:space="preserve">
    <value>Waarde Risico na acties</value>
  </data>
  <data name="RaValuePerYearTxt" xml:space="preserve">
    <value>Overzicht per jaar</value>
  </data>
  <data name="RaActionsTabTxt" xml:space="preserve">
    <value>(Preventieve) Acties</value>
  </data>
  <data name="RaSparesTabTxt" xml:space="preserve">
    <value>Reservedelen</value>
  </data>
  <data name="RaMtbfLbl" xml:space="preserve">
    <value>MTBF</value>
  </data>
  <data name="RaYearLbl" xml:space="preserve">
    <value>Jaar</value>
  </data>
  <data name="RaShowValuesLbl" xml:space="preserve">
    <value>Toon Waarden (Standaard)</value>
  </data>
  <data name="RaShowCustValuesLbl" xml:space="preserve">
    <value>Toon aangepaste Waarden</value>
  </data>
  <data name="RaShowPointsLbl" xml:space="preserve">
    <value>Toon Punten</value>
  </data>
  <data name="RaSparesPMOTabTxt" xml:space="preserve">
    <value>Huidige Reservedelen</value>
  </data>
  <data name="RaActionsPMOTabTxt" xml:space="preserve">
    <value>Huidige acties</value>
  </data>
  <data name="RaPMOHeaderTxt" xml:space="preserve">
    <value>PMO</value>
  </data>
  <data name="RaSubOptimalRightTxt" xml:space="preserve">
    <value>Suboptimaal, overweeg minder preventieve acties</value>
  </data>
  <data name="RaSubOptimalLeftTxt" xml:space="preserve">
    <value>Suboptimaal, overweeg meer preventieve acties</value>
  </data>
  <data name="RaOptimalTxt" xml:space="preserve">
    <value>Optimale situatie</value>
  </data>
  <data name="RcWCopyAllBtn" xml:space="preserve">
    <value>Alles kopiëren</value>
  </data>
  <data name="RcWRiskCountTxt" xml:space="preserve">
    <value>geselecteerde Waarde Risico's. Waar wilt u de geselecteerde risico's naartoe kopiëren?</value>
  </data>
  <data name="RaPrevActDetailsTxt" xml:space="preserve">
    <value>Details preventieve actie</value>
  </data>
  <data name="RaSparePartDetailsTxt" xml:space="preserve">
    <value>Details reserveonderdelen</value>
  </data>
  <data name="RaSaveBtnTxt" xml:space="preserve">
    <value>Opslaan</value>
  </data>
  <data name="RaRiskPmoTabTxt" xml:space="preserve">
    <value>Waarde Risico met huidige acties</value>
  </data>
  <data name="RaComparisonLeftTxt" xml:space="preserve">
    <value>Risico: overweeg meer preventieve acties</value>
  </data>
  <data name="RaComparisonRightTxt" xml:space="preserve">
    <value>Risico: overweeg minder preventieve acties</value>
  </data>
  <data name="RaComparisonTxt" xml:space="preserve">
    <value>Risico: Optimale situatie</value>
  </data>
  <data name="RaRiskOptimumPreventiveActionsAYearTxt" xml:space="preserve">
    <value>Indicatie optimale maatregelen</value>
  </data>
  <data name="RaComparisonPmoRightTxt" xml:space="preserve">
    <value>PMO: Optimale situatie</value>
  </data>
  <data name="RaComparisonPmoLeftTxt" xml:space="preserve">
    <value>PMO: Geen verbetering</value>
  </data>
  <data name="RaComparisonPmoTxt" xml:space="preserve">
    <value>PMO: Kleine verbetering</value>
  </data>
  <data name="RaDeleteRiskWarning" xml:space="preserve">
    <value>Waarschuwing: deze actie kan niet ongedaan worden gemaakt</value>
  </data>
  <data name="RaBtnImpRisksorTasks" xml:space="preserve">
    <value>Importeren</value>
  </data>
  <data name="RaOverrideCostsTitle" xml:space="preserve">
    <value>Preventieve actie bevat Clusterkosten</value>
  </data>
  <data name="RaOverrideCostsText" xml:space="preserve">
    <value>Wilt u de kosten van de geschatte kosten overschrijven?</value>
  </data>
  <data name="RaModifiedByLbl" xml:space="preserve">
    <value>Gewijzigd door</value>
  </data>
  <data name="RaRiskTabTxt" xml:space="preserve">
    <value>Risico</value>
  </data>
  <data name="RaWoActionsTxt" xml:space="preserve">
    <value>Zonder acties</value>
  </data>
  <data name="RaVrTxt" xml:space="preserve">
    <value>Waarde Risico</value>
  </data>
  <data name="RaActTxt" xml:space="preserve">
    <value>Acties</value>
  </data>
  <data name="RaTotTxt" xml:space="preserve">
    <value>Totaal</value>
  </data>
  <data name="RaWithActionsTxt" xml:space="preserve">
    <value>Met (preventieve) acties</value>
  </data>
  <data name="RaWithActionsPMOTabTxt" xml:space="preserve">
    <value>Met (huidige) acties</value>
  </data>
  <data name="RiskCosts" xml:space="preserve">
    <value>Actie kosten</value>
  </data>
  <data name="RaValueRiskCosts" xml:space="preserve">
    <value>Waarde Risico kosten</value>
  </data>
  <data name="RaCSIRrTxt" xml:space="preserve">
    <value>CSIR punten</value>
  </data>
  <data name="RaWTotTxt" xml:space="preserve">
    <value>Gewogen punten</value>
  </data>
  <data name="RaCSIRwTxt" xml:space="preserve">
    <value>Weerstands niveau</value>
  </data>
  <data name="RaSapaDetailsTxt" xml:space="preserve">
    <value>Investeringsvoorstel</value>
  </data>
  <data name="RaWithSapaActionsTxt" xml:space="preserve">
    <value>Met acties</value>
  </data>
  <data name="RaSapaActionsTabTxt" xml:space="preserve">
    <value>(Investerings) Acties</value>
  </data>
  <data name="RaRulLbl" xml:space="preserve">
    <value>Rest levensduur</value>
  </data>
  <data name="RaDataLbl" xml:space="preserve">
    <value>Toelichting info</value>
  </data>
  <data name="RaDirectRiskCosts" xml:space="preserve">
    <value>Directe risico kosten</value>
  </data>
  <data name="RaActionCosts" xml:space="preserve">
    <value>Actie kosten</value>
  </data>
  <data name="RaAttachmentTabTxt" xml:space="preserve">
    <value>Bijlagen</value>
  </data>
  <data name="RaRiskSortOrder" xml:space="preserve">
    <value />
  </data>
  <data name="RaAdditionalDataLbl" xml:space="preserve">
    <value>Extra data</value>
  </data>
</root>