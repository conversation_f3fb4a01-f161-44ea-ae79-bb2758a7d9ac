﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="RaMenuTitle" xml:space="preserve">
    <value>Approach for Value Risk Assessment</value>
  </data>
  <data name="RaMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;In this Value Risk Assessment module, Risks can be mitigated with the most optimal measures (Values) to be taken&lt;/p&gt;
        &lt;ol&gt;
            &lt;li&gt;Create a Value Risk Assessment, by selecting &lt;b&gt; New&lt;/b&gt;, or edit an existing Value Risk Assessment
            &lt;ol type='a'&gt;
                &lt;li&gt;Allocate the value risk assessment under the appropriate functional level in the tree by selecting the correct &lt;b&gt;Hierarchy Levels&lt;/b&gt; in the pulldown menu&lt;/li&gt;
                &lt;li&gt;Describe the &lt;b&gt;function&lt;/b&gt;, asset functionality, norms, capacity, specification etc that is being assessed in relation to the (primary) proces&lt;/li&gt;
            &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Describe the &lt;b&gt;Failure Mechanism&lt;/b&gt; by
                &lt;ol type='a'&gt;
                    &lt;li&gt;Appoint the dominant &lt;b&gt;Failure Mechanism&lt;/b&gt; that is related to the function&lt;/li&gt;
                    &lt;li&gt;Specify the specific &lt;b&gt;Failure Mode&lt;/b&gt; that describes the failure&lt;/li&gt;
                    &lt;li&gt; Select the &lt;b&gt;observability and Mode of Occurrence&lt;/b&gt; that describe the Failure Mode&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Describe the &lt;b&gt;Cause&lt;/b&gt; of the functional failure&lt;/li&gt;
            &lt;li&gt;Describe the &lt;b&gt;Consequences and Effect&lt;/b&gt; of the failure in relation to the (primary) process&lt;/li&gt;
            &lt;li&gt;Project the Failure and its consequences on the &lt;b&gt;Risk Matrix without any Actions&lt;/b&gt; are taken
                &lt;ol type='a'&gt;
                    &lt;li&gt;Select the different effects&lt;/li&gt;
                    &lt;li&gt;Select the &lt;b&gt;Probability&lt;/b&gt; (MTBF)&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Define the best &lt;b&gt;Preventive Actions&lt;/b&gt;
                &lt;ol type='a'&gt;
                    &lt;li&gt;Define necessary &lt;b&gt;Spares&lt;/b&gt;&lt;/li&gt;
                    &lt;li&gt;Define other &lt;b&gt;Preventive Actions&lt;/b&gt;&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Project the Failure Mode and its consequences on the &lt;b&gt;Value Risk Matrix with Actions&lt;/b&gt; that are taken&lt;/li&gt;
            &lt;li&gt;Try to &lt;b&gt;Optimize&lt;/b&gt; the actions in relation to the Risk specified&lt;/li&gt;
        &lt;/ol&gt;</value>
  </data>
  <data name="RaHeaderTxt" xml:space="preserve">
    <value>Value Risk Assessment</value>
  </data>
  <data name="RaDeleteBtnTxt" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="RaNewBtnTxt" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="RaCopyBtnTxt" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="RaFailModeLbl" xml:space="preserve">
    <value>Failure Mode</value>
  </data>
  <data name="RaFailMechLbl" xml:space="preserve">
    <value>Failure Mechanism</value>
  </data>
  <data name="RaCauseLbl" xml:space="preserve">
    <value>Cause</value>
  </data>
  <data name="RaConsequenceLbl" xml:space="preserve">
    <value>Consequence</value>
  </data>
  <data name="RaEffectLbl" xml:space="preserve">
    <value>Effect</value>
  </data>
  <data name="RaFailCat1Lbl" xml:space="preserve">
    <value>Failure Type</value>
  </data>
  <data name="RaFailCat2Lbl" xml:space="preserve">
    <value>Observability</value>
  </data>
  <data name="RaRemarksLbl" xml:space="preserve">
    <value>Remarks</value>
  </data>
  <data name="RaOptimizeTabTxt" xml:space="preserve">
    <value>Optimize</value>
  </data>
  <data name="RaFunctionLbl" xml:space="preserve">
    <value>Function</value>
  </data>
  <data name="RaAssesmentStatusLbl" xml:space="preserve">
    <value>Analysis Status</value>
  </data>
  <data name="RaMiscTabTxt" xml:space="preserve">
    <value>Miscellaneous</value>
  </data>
  <data name="RaInitiatedByLbl" xml:space="preserve">
    <value>Initiated by</value>
  </data>
  <data name="RaOnLbl" xml:space="preserve">
    <value>On</value>
  </data>
  <data name="RaResponsibleLbl" xml:space="preserve">
    <value>Responsible</value>
  </data>
  <data name="RaRiskBeforeTabTxt" xml:space="preserve">
    <value>Value Risk without Actions</value>
  </data>
  <data name="RaRiskAfterTabTxt" xml:space="preserve">
    <value>Value Risk after Actions</value>
  </data>
  <data name="RaValuePerYearTxt" xml:space="preserve">
    <value>Overview per year</value>
  </data>
  <data name="RaActionsTabTxt" xml:space="preserve">
    <value>(Preventive) Actions</value>
  </data>
  <data name="RaSparesTabTxt" xml:space="preserve">
    <value>Spares</value>
  </data>
  <data name="RaMtbfLbl" xml:space="preserve">
    <value>MTBF</value>
  </data>
  <data name="RaYearLbl" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="RaShowValuesLbl" xml:space="preserve">
    <value>Show Values (default)</value>
  </data>
  <data name="RaShowCustValuesLbl" xml:space="preserve">
    <value>Show custom Values</value>
  </data>
  <data name="RaShowPointsLbl" xml:space="preserve">
    <value>Show Points</value>
  </data>
  <data name="RaSparesPMOTabTxt" xml:space="preserve">
    <value>Current Spares</value>
  </data>
  <data name="RaActionsPMOTabTxt" xml:space="preserve">
    <value>Current actions</value>
  </data>
  <data name="RaPMOHeaderTxt" xml:space="preserve">
    <value>PMO Assessment</value>
  </data>
  <data name="RaSubOptimalRightTxt" xml:space="preserve">
    <value>Sub optimal, consider fewer preventive actions</value>
  </data>
  <data name="RaSubOptimalLeftTxt" xml:space="preserve">
    <value>Sub optimal, consider more preventive actions</value>
  </data>
  <data name="RaOptimalTxt" xml:space="preserve">
    <value>Optimal situation</value>
  </data>
  <data name="RcWCopyAllBtn" xml:space="preserve">
    <value>Copy All</value>
  </data>
  <data name="RcWRiskCountTxt" xml:space="preserve">
    <value>Value Risks selected. Where would you like to copy the selected risks to?</value>
  </data>
  <data name="RaPrevActDetailsTxt" xml:space="preserve">
    <value>Preventive action details</value>
  </data>
  <data name="RaSparePartDetailsTxt" xml:space="preserve">
    <value>Spare part details</value>
  </data>
  <data name="RaSaveBtnTxt" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="RaRiskPmoTabTxt" xml:space="preserve">
    <value>Value Risk current Actions</value>
  </data>
  <data name="RaComparisonLeftTxt" xml:space="preserve">
    <value>Risk: consider more preventive actions</value>
  </data>
  <data name="RaComparisonRightTxt" xml:space="preserve">
    <value>Risk: consider fewer preventive actions</value>
  </data>
  <data name="RaComparisonTxt" xml:space="preserve">
    <value>Risk: Optimal situation</value>
  </data>
  <data name="RaRiskOptimumPreventiveActionsAYearTxt" xml:space="preserve">
    <value>Indication optimal actions</value>
  </data>
  <data name="RaComparisonPmoRightTxt" xml:space="preserve">
    <value>PMO: Optimal situation</value>
  </data>
  <data name="RaComparisonPmoLeftTxt" xml:space="preserve">
    <value>PMO: No improvement</value>
  </data>
  <data name="RaComparisonPmoTxt" xml:space="preserve">
    <value>PMO: Slight improvement</value>
  </data>
  <data name="RaDeleteRiskWarning" xml:space="preserve">
    <value>Warning: This action can not undone</value>
  </data>
  <data name="RaBtnImpRisksorTasks" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="RaOverrideCostsTitle" xml:space="preserve">
    <value>Preventive action contains Cluster Costs</value>
  </data>
  <data name="RaOverrideCostsText" xml:space="preserve">
    <value>Do you want to override the costs from the Estimated Costs?</value>
  </data>
  <data name="RaModifiedByLbl" xml:space="preserve">
    <value>Modified by</value>
  </data>
  <data name="RaRiskTabTxt" xml:space="preserve">
    <value>Risk</value>
  </data>
  <data name="RaWoActionsTxt" xml:space="preserve">
    <value>Without actions</value>
  </data>
  <data name="RaVrTxt" xml:space="preserve">
    <value>Value Risk</value>
  </data>
  <data name="RaActTxt" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="RaTotTxt" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="RaWithActionsTxt" xml:space="preserve">
    <value>With (Preventive) Actions</value>
  </data>
  <data name="RaWithActionsPMOTabTxt" xml:space="preserve">
    <value>With Current Actions</value>
  </data>
  <data name="RiskCosts" xml:space="preserve">
    <value>Action Costs</value>
  </data>
  <data name="RaValueRiskCosts" xml:space="preserve">
    <value>Value Risk Costs</value>
  </data>
  <data name="RaRiskImportIdTxt" xml:space="preserve">
    <value>Import ID</value>
  </data>
  <data name="RaCSIRrTxt" xml:space="preserve">
    <value>CSIR points</value>
  </data>
  <data name="RaWTotTxt" xml:space="preserve">
    <value>Weighted points</value>
  </data>
  <data name="RaCriticalityTxt" xml:space="preserve">
    <value>Criticality</value>
  </data>
  <data name="RaCSIRwTxt" xml:space="preserve">
    <value>Resistance level</value>
  </data>
  <data name="RaCSIRcsTxt" xml:space="preserve">
    <value>Cyber Security</value>
  </data>
  <data name="RaSapaDetailsTxt" xml:space="preserve">
    <value>Investment proposal</value>
  </data>
  <data name="RaWithSapaActionsTxt" xml:space="preserve">
    <value>With actions</value>
  </data>
  <data name="RaSapaActionsTabTxt" xml:space="preserve">
    <value>(Investement) Actions</value>
  </data>
  <data name="RaSapaIndexTxt" xml:space="preserve">
    <value>SAPA index</value>
  </data>
  <data name="RaSapaTotTxt" xml:space="preserve">
    <value>Asset Risk Profile</value>
  </data>
  <data name="RaRulLbl" xml:space="preserve">
    <value>Remaining Useful Life</value>
  </data>
  <data name="RaCbiLbl" xml:space="preserve">
    <value>Corparate Business Index</value>
  </data>
  <data name="RaDataLbl" xml:space="preserve">
    <value>Data explanation</value>
  </data>
  <data name="RaAssesmentStatusFilterLbl" xml:space="preserve">
    <value>Status Filter</value>
  </data>
  <data name="RaDirectRiskCosts" xml:space="preserve">
    <value>Direct Risk Cost</value>
  </data>
  <data name="RaActionCosts" xml:space="preserve">
    <value>Action Costs</value>
  </data>
  <data name="RaAttachmentTabTxt" xml:space="preserve">
    <value>Attachments</value>
  </data>
  <data name="RaScreenshotBtnTxt" xml:space="preserve">
    <value>Screenshot</value>
  </data>
  <data name="RaRiskSortOrder" xml:space="preserve">
    <value>Risk sort order</value>
  </data>
  <data name="RaAdditionalDataLbl" xml:space="preserve">
    <value>Additional Data</value>
  </data>
</root>