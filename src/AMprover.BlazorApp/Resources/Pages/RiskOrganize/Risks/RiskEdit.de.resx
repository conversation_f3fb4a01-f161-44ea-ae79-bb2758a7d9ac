﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="RaMenuTitle" xml:space="preserve">
    <value>Ansatz für Bewertung des Wertrisikos</value>
  </data>
  <data name="RaMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;In diesem Modul Value Risk Assessment können Risiken mit den optimalsten Maßnahmen (Werten) gemindert werden.&lt;/p&gt;
&lt;ol&gt;
            &lt;li&gt;Erstellen Sie eine Wertrisikobewertung, indem Sie &lt;b&gt;Neu&lt;/b&gt; wählen, oder bearbeiten Sie eine vorhandene Wertrisikobewertung
            &lt;ol type='a'&gt;
                &lt;li&gt;Ordnen Sie den Wert Risikobewertung unter der entsprechenden funktionalen Ebene im Baum zu, indem Sie die richtigen &lt;b&gt;Hierarchieebenen&lt;/b&gt; im Pulldown-Menü auswählen&lt;/li&gt;
                &lt;li&gt;Beschreiben Sie die &lt;b&gt;Funktion&lt;/b&gt;, Anlagenfunktionalität, Normen, Kapazität, Spezifikation usw., die in Bezug auf den (primären) Prozess bewertet werden&lt;/li&gt;
            &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Beschreiben Sie das &lt;b&gt;Mechanismus des Fehlers&lt;/b&gt; durch
&lt;ol type='a'&gt;
                    &lt;li&gt;Festlegen des dominanten &lt;b&gt;Mechanismus des Fehlers&lt;/b&gt;, der sich auf die Funktion bezieht&lt;/li&gt;
                    &lt;li&gt;Angeben des spezifischen &lt;b&gt;Fehlermodus&lt;/b&gt;, der den Fehler beschreibt&lt;/li&gt;
                    &lt;li&gt; Wählen Sie die &lt;b&gt;Beobachtbarkeit und Wirkungsweise&lt;/b&gt;, die den Versagensmodus beschreiben &lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Beschreiben Sie die &lt;b&gt;Ursache&lt;/b&gt; des Funktionsfehlers&lt;/li&gt;
            &lt;li&gt;Beschreiben Sie die &lt;b&gt;Folgen und Auswirkungen&lt;/b&gt; des Ausfalls in Bezug auf den (primären) Prozess&lt;/li&gt;
            &lt;li&gt;Projizieren Sie den Fehlermodus und seine Folgen auf die &lt;b&gt;Risikomatrix, ohne dass Maßnahmen&lt;/b&gt; ergriffen werden
&lt;ol type='a'&gt;
                    &lt;li&gt;Wählen Sie die verschiedenen Effekte&lt;/li&gt;
                    &lt;li&gt;Wählen Sie die &lt;b&gt;Wahrscheinlichkeit&lt;/b&gt; (MTBF)&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
&lt;li&gt;Definieren Sie die besten &lt;b&gt;Präventionsmaßnahmen&lt;/b&gt;
                &lt;ol type='a'&gt;
                    &lt;li&gt;Definieren Sie notwendige &lt;b&gt;Ersatzteile&lt;/b&gt;&lt;/li&gt;
                    &lt;li&gt;Definieren Sie andere &lt;b&gt;vorbeugende Maßnahmen&lt;/b&gt;&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Projizieren Sie den Fehlermodus und seine Auswirkungen auf die &lt;b&gt;Wertrisikomatrix mit Aktionen&lt;/b&gt;, die ausgeführt werden&lt;/li&gt;
            &lt;li&gt;Versuchen Sie, die Aktionen in Bezug auf das angegebene Risiko zu &lt;b&gt;optimieren&lt;/b&gt;&lt;/li&gt;
        &lt;/ol&gt;</value>
  </data>
  <data name="RaHeaderTxt" xml:space="preserve">
    <value>Wert Risiko Bewertung</value>
  </data>
  <data name="RaDeleteBtnTxt" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="RaNewBtnTxt" xml:space="preserve">
    <value>Neu</value>
  </data>
  <data name="RaCopyBtnTxt" xml:space="preserve">
    <value>Kopieren</value>
  </data>
  <data name="RaFailModeLbl" xml:space="preserve">
    <value>Fehlermodus</value>
  </data>
  <data name="RaFailMechLbl" xml:space="preserve">
    <value>Mechanismus des Fehlers</value>
  </data>
  <data name="RaCauseLbl" xml:space="preserve">
    <value>Ursache</value>
  </data>
  <data name="RaConsequenceLbl" xml:space="preserve">
    <value>Folge</value>
  </data>
  <data name="RaEffectLbl" xml:space="preserve">
    <value>Effekt</value>
  </data>
  <data name="RaFailCat1Lbl" xml:space="preserve">
    <value>Fehlerart</value>
  </data>
  <data name="RaFailCat2Lbl" xml:space="preserve">
    <value>Beobachtbarkeit</value>
  </data>
  <data name="RaRemarksLbl" xml:space="preserve">
    <value>Bemerkungen</value>
  </data>
  <data name="RaOptimizeTabTxt" xml:space="preserve">
    <value>Optimiere</value>
  </data>
  <data name="RaFunctionLbl" xml:space="preserve">
    <value>Funktion</value>
  </data>
  <data name="RaAssesmentStatusLbl" xml:space="preserve">
    <value>Stand der Analyse</value>
  </data>
  <data name="RaMiscTabTxt" xml:space="preserve">
    <value>Verschiedenes</value>
  </data>
  <data name="RaInitiatedByLbl" xml:space="preserve">
    <value>Initiiert von</value>
  </data>
  <data name="RaOnLbl" xml:space="preserve">
    <value>An</value>
  </data>
  <data name="RaResponsibleLbl" xml:space="preserve">
    <value>Verantwortlich</value>
  </data>
  <data name="RaRiskBeforeTabTxt" xml:space="preserve">
    <value>Wert Risiko ohne Maßnahmen</value>
  </data>
  <data name="RaRiskAfterTabTxt" xml:space="preserve">
    <value>Wert Risiko nach Maßnahmen</value>
  </data>
  <data name="RaValuePerYearTxt" xml:space="preserve">
    <value>Überblick pro Jahr</value>
  </data>
  <data name="RaActionsTabTxt" xml:space="preserve">
    <value>(Präventive) Maßnahmen</value>
  </data>
  <data name="RaSparesTabTxt" xml:space="preserve">
    <value>Ersatzteile</value>
  </data>
  <data name="RaMtbfLbl" xml:space="preserve">
    <value>MTBF</value>
  </data>
  <data name="RaYearLbl" xml:space="preserve">
    <value>Jahr</value>
  </data>
  <data name="RaShowValuesLbl" xml:space="preserve">
    <value>Werte anzeigen (Standard)</value>
  </data>
  <data name="RaShowCustValuesLbl" xml:space="preserve">
    <value>Eigene Werte anzeigen</value>
  </data>
  <data name="RaShowPointsLbl" xml:space="preserve">
    <value>Punkte anzeigen</value>
  </data>
  <data name="RaSparesPMOTabTxt" xml:space="preserve">
    <value>Aktuelle Ersatzteile</value>
  </data>
  <data name="RaActionsPMOTabTxt" xml:space="preserve">
    <value>Aktuelle Maßnahmen</value>
  </data>
  <data name="RaPMOHeaderTxt" xml:space="preserve">
    <value>PMO</value>
  </data>
  <data name="RaSubOptimalRightTxt" xml:space="preserve">
    <value>Suboptimal, Weniger Maßnahmen in Betracht ziehen</value>
  </data>
  <data name="RaSubOptimalLeftTxt" xml:space="preserve">
    <value>Suboptimal, Erwägen mehr Maßnahmen</value>
  </data>
  <data name="RaOptimalTxt" xml:space="preserve">
    <value>Optimale Situation</value>
  </data>
  <data name="RcWCopyAllBtn" xml:space="preserve">
    <value>Alles kopieren</value>
  </data>
  <data name="RcWRiskCountTxt" xml:space="preserve">
    <value>Ausgewählte Risiken. Wohin möchten Sie die ausgewählten Wert risiken kopieren?</value>
  </data>
  <data name="RaPrevActDetailsTxt" xml:space="preserve">
    <value>Details vorbeugenden Maßnahmen</value>
  </data>
  <data name="RaSparePartDetailsTxt" xml:space="preserve">
    <value>Ersatzteile Details</value>
  </data>
  <data name="RaSaveBtnTxt" xml:space="preserve">
    <value>Speichern</value>
  </data>
  <data name="RaRiskPmoTabTxt" xml:space="preserve">
    <value>Value Risk aktuelle Maßnahmen</value>
  </data>
  <data name="RaComparisonLeftTxt" xml:space="preserve">
    <value>Risiko: Erwägen mehr vorbeugende Maßnahmen</value>
  </data>
  <data name="RaComparisonRightTxt" xml:space="preserve">
    <value>Risiko: Weniger vorbeugende Maßnahmen in Betracht ziehen</value>
  </data>
  <data name="RaComparisonTxt" xml:space="preserve">
    <value>Risiko: Optimale Situation</value>
  </data>
  <data name="RaRiskOptimumPreventiveActionsAYearTxt" xml:space="preserve">
    <value>Angabe optimaler Maßnahmen</value>
  </data>
  <data name="RaComparisonPmoRightTxt" xml:space="preserve">
    <value>PMO: Optimale Situation</value>
  </data>
  <data name="RaComparisonPmoLeftTxt" xml:space="preserve">
    <value>PMO: Keine Verbesserung</value>
  </data>
  <data name="RaComparisonPmoTxt" xml:space="preserve">
    <value>PMO: Leichte Verbesserung</value>
  </data>
  <data name="RaDeleteRiskWarning" xml:space="preserve">
    <value>Warnung: Diese Aktion kann nicht rückgängig gemacht werden</value>
  </data>
  <data name="RaBtnImpRisksorTasks" xml:space="preserve">
    <value>Importieren</value>
  </data>
  <data name="RaOverrideCostsTitle" xml:space="preserve">
    <value>Präventivmaßnahmen enthalten Cluster Kosten</value>
  </data>
  <data name="RaOverrideCostsText" xml:space="preserve">
    <value>Möchten Sie die Kosten von den geschätzten Kosten überschreiben?</value>
  </data>
  <data name="RaModifiedByLbl" xml:space="preserve">
    <value>Geändert von</value>
  </data>
  <data name="RaRiskTabTxt" xml:space="preserve">
    <value>Risiko</value>
  </data>
  <data name="RaWoActionsTxt" xml:space="preserve">
    <value>Ohne (Präventive) Maßnahmen</value>
  </data>
  <data name="RaVrTxt" xml:space="preserve">
    <value>Wert Risiko</value>
  </data>
  <data name="RaActTxt" xml:space="preserve">
    <value>Maßnahmen</value>
  </data>
  <data name="RaTotTxt" xml:space="preserve">
    <value>Gesamt</value>
  </data>
  <data name="RaWithActionsTxt" xml:space="preserve">
    <value>Mit (Präventive) Maßnahmen</value>
  </data>
  <data name="RaWithActionsPMOTabTxt" xml:space="preserve">
    <value>Mit Aktuelle Maßnahmen</value>
  </data>
  <data name="RiskCosts" xml:space="preserve">
    <value>Maßnahmen Kosten</value>
  </data>
  <data name="RaValueRiskCosts" xml:space="preserve">
    <value>Wert Risiko Kosten</value>
  </data>
  <data name="RaCSIRrTxt" xml:space="preserve">
    <value>CSIR punkte</value>
  </data>
  <data name="RaWTotTxt" xml:space="preserve">
    <value>Gewichtete Punkte</value>
  </data>
  <data name="RaCSIRwTxt" xml:space="preserve">
    <value>Widerstandsniveau</value>
  </data>
  <data name="RaDirectRiskCosts" xml:space="preserve">
    <value>Direkte Risikokosten</value>
  </data>
  <data name="RaActionCosts" xml:space="preserve">
    <value>Maßnahmen Kosten</value>
  </data>
  <data name="RaAttachmentTabTxt" xml:space="preserve">
    <value>Anhängsel</value>
  </data>
  <data name="RaRiskSortOrder" xml:space="preserve">
    <value />
  </data>
  <data name="RaAdditionalDataLbl" xml:space="preserve">
    <value />
  </data>
</root>