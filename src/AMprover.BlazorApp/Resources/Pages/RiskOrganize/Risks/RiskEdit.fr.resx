﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="RaMenuTitle" xml:space="preserve">
    <value>Approche de l'évaluation du Valeur/ Risque</value>
  </data>
  <data name="RaMenuTxt" xml:space="preserve">
    <value>&lt;p&gt;Dans ce module d’évaluation des Valeurs/Risques liés, les risques peuvent être atténués avec les mesures les plus optimales (valeurs) à prendre. &lt;/p&gt;
&lt;ol&gt;
            &lt;li&gt;Créer une évaluation du Valeur/ Risque, en sélectionnant &lt;b&gt;Nouveau&lt;/b&gt;, ou modifier une évaluation du Valeur/ Risque existante
            &lt;ol type='a'&gt;
                &lt;li&gt;Attribuer l'évaluation du Valeur/ Risque sous le niveau fonctionnel approprié dans l'arbre en sélectionnant les &lt;b&gt;niveaux hiérarchiques&lt;/b&gt; corrects dans le menu déroulant&lt;/li&gt;
                &lt;li&gt;Décrire la &lt;b&gt;fonction&lt;/b&gt;, la fonctionnalité de l'actif, les normes, la capacité, les spécifications, etc. qui est évaluée en relation avec le processus (primaire) &lt;/li&gt;
            &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt; Décrivez le &lt;b&gt;Mécanisme de défaillance&lt;/b&gt; en
&lt;ol type='a'&gt;
                    &lt;li&gt; Nommer le &lt;b&gt;Mécanisme de défaillance&lt;/b&gt; dominant lié à la fonction&lt;/li&gt;
                    &lt;li&gt;Spécifier le &lt;b&gt;Mode de défaillance&lt;/b&gt; spécifique qui décrit la défaillance&lt;/li&gt;
                    &lt;li&gt;Sélectionnez les 1ère et 2ème &lt;b&gt;catégories de défaillance&lt;/b&gt; qui définissent le modus de défaillance&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Décrire la &lt;b&gt;cause&lt;/b&gt; de la défaillance fonctionnelle&lt;/li&gt;
            &lt;li&gt; Sélectionnez la &lt;b&gt;observabilité et le mode d'action&lt;/b&gt; qui décrivent le mécanisme de défaillance &lt;/li&gt;
            &lt;li&gt;Projeter le Mode de Défaillance et ses conséquences sur la &lt;b&gt;matrice des risques sans qu’aucune action ne soit&lt;/b&gt; prise
&lt;ol type='a'&gt;
                    &lt;li&gt;Sélectionnez les différents effets&lt;/li&gt;
                    &lt;li&gt;Sélectionnez la &lt;b&gt;probabilité&lt;/b&gt; (MTBF)&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
&lt;li&gt;Définir les &lt;b&gt;meilleures actions préventives&lt;/b&gt;
                &lt;ol type='a'&gt;
                    &lt;li&gt;Définir les &lt;b&gt;pièces de rechange nécessaires&lt;/b&gt;&lt;/li&gt;
                    &lt;li&gt;Définir d’autres &lt;b&gt;actions préventives&lt;/b&gt;&lt;/li&gt;
                &lt;/ol&gt;
            &lt;/li&gt;
            &lt;li&gt;Projeter le mode de défaillance et ses conséquences sur la &lt;b&gt;matrice de Valeur/ Risque avec les actions&lt;/b&gt; qui sont prises&lt;/li&gt;
            &lt;li&gt;Essayez &lt;b&gt;d’optimiser&lt;/b&gt; les actions par rapport au risque spécifié&lt;/li&gt;
        &lt;/ol&gt;</value>
  </data>
  <data name="RaHeaderTxt" xml:space="preserve">
    <value>Évaluation Valeur/ Risque</value>
  </data>
  <data name="RaDeleteBtnTxt" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="RaNewBtnTxt" xml:space="preserve">
    <value>Nouveau</value>
  </data>
  <data name="RaCopyBtnTxt" xml:space="preserve">
    <value>Copie</value>
  </data>
  <data name="RaFailModeLbl" xml:space="preserve">
    <value>Mode de défaillance</value>
  </data>
  <data name="RaFailMechLbl" xml:space="preserve">
    <value>Mécanisme de défaillance</value>
  </data>
  <data name="RaCauseLbl" xml:space="preserve">
    <value>Cause</value>
  </data>
  <data name="RaConsequenceLbl" xml:space="preserve">
    <value>Conséquence</value>
  </data>
  <data name="RaEffectLbl" xml:space="preserve">
    <value>Effet</value>
  </data>
  <data name="RaFailCat1Lbl" xml:space="preserve">
    <value>Type de défaillance</value>
  </data>
  <data name="RaFailCat2Lbl" xml:space="preserve">
    <value>Observabilité</value>
  </data>
  <data name="RaRemarksLbl" xml:space="preserve">
    <value>Remarques</value>
  </data>
  <data name="RaOptimizeTabTxt" xml:space="preserve">
    <value>Optimisez</value>
  </data>
  <data name="RaFunctionLbl" xml:space="preserve">
    <value>Fonction</value>
  </data>
  <data name="RaAssesmentStatusLbl" xml:space="preserve">
    <value>État de l'analyse</value>
  </data>
  <data name="RaMiscTabTxt" xml:space="preserve">
    <value>Divers</value>
  </data>
  <data name="RaInitiatedByLbl" xml:space="preserve">
    <value>Initiée par</value>
  </data>
  <data name="RaOnLbl" xml:space="preserve">
    <value>On</value>
  </data>
  <data name="RaResponsibleLbl" xml:space="preserve">
    <value>Responsable</value>
  </data>
  <data name="RaRiskBeforeTabTxt" xml:space="preserve">
    <value>Valeur/ Risque sans actions</value>
  </data>
  <data name="RaRiskAfterTabTxt" xml:space="preserve">
    <value>Valeur/ Risque après actions</value>
  </data>
  <data name="RaValuePerYearTxt" xml:space="preserve">
    <value>Aperçu par an</value>
  </data>
  <data name="RaActionsTabTxt" xml:space="preserve">
    <value>Actions (Préventives)</value>
  </data>
  <data name="RaSparesTabTxt" xml:space="preserve">
    <value>Pièces de rechange</value>
  </data>
  <data name="RaMtbfLbl" xml:space="preserve">
    <value>MTBF</value>
  </data>
  <data name="RaYearLbl" xml:space="preserve">
    <value>Année</value>
  </data>
  <data name="RaShowValuesLbl" xml:space="preserve">
    <value>Afficher les Valeurs (défaut)</value>
  </data>
  <data name="RaShowCustValuesLbl" xml:space="preserve">
    <value>Afficher Valeurs personnalisées</value>
  </data>
  <data name="RaShowPointsLbl" xml:space="preserve">
    <value>Afficher points</value>
  </data>
  <data name="RaSparesPMOTabTxt" xml:space="preserve">
    <value>Pièces de rechange actuelles</value>
  </data>
  <data name="RaActionsPMOTabTxt" xml:space="preserve">
    <value>Actions actuel</value>
  </data>
  <data name="RaPMOHeaderTxt" xml:space="preserve">
    <value>PMO</value>
  </data>
  <data name="RaSubOptimalRightTxt" xml:space="preserve">
    <value>Sous-optimal, envisager moins actions préventives</value>
  </data>
  <data name="RaSubOptimalLeftTxt" xml:space="preserve">
    <value>Sous-optimal, envisager plus actions préventives</value>
  </data>
  <data name="RaOptimalTxt" xml:space="preserve">
    <value>Situation optimale</value>
  </data>
  <data name="RcWCopyAllBtn" xml:space="preserve">
    <value>Copier tout</value>
  </data>
  <data name="RcWRiskCountTxt" xml:space="preserve">
    <value>Valeurs/ Risques sélectionnés. Où voulez-vous copier les risques sélectionnés ?</value>
  </data>
  <data name="RaPrevActDetailsTxt" xml:space="preserve">
    <value>Détails action préventive</value>
  </data>
  <data name="RaSparePartDetailsTxt" xml:space="preserve">
    <value>Détails pièce de rechange</value>
  </data>
  <data name="RaSaveBtnTxt" xml:space="preserve">
    <value>Sauvez</value>
  </data>
  <data name="RaRiskPmoTabTxt" xml:space="preserve">
    <value>Valeur Risque actions actuelles</value>
  </data>
  <data name="RaComparisonLeftTxt" xml:space="preserve">
    <value>Risque: envisager plus actions préventives</value>
  </data>
  <data name="RaComparisonRightTxt" xml:space="preserve">
    <value>Risque: envisager moins actions préventives</value>
  </data>
  <data name="RaComparisonTxt" xml:space="preserve">
    <value>Risque: Situation optimale</value>
  </data>
  <data name="RaRiskOptimumPreventiveActionsAYearTxt" xml:space="preserve">
    <value>Indication des mesures optimales</value>
  </data>
  <data name="RaComparisonPmoRightTxt" xml:space="preserve">
    <value>PMO: Situation optimale</value>
  </data>
  <data name="RaComparisonPmoLeftTxt" xml:space="preserve">
    <value>PMO : Pas d’amélioration</value>
  </data>
  <data name="RaComparisonPmoTxt" xml:space="preserve">
    <value>PMO: Légère amélioration</value>
  </data>
  <data name="RaDeleteRiskWarning" xml:space="preserve">
    <value>Avertissement : Cette action ne peut pas être annulée</value>
  </data>
  <data name="RaBtnImpRisksorTasks" xml:space="preserve">
    <value>Importer</value>
  </data>
  <data name="RaOverrideCostsTitle" xml:space="preserve">
    <value>L’action préventive contient les coûts des clusters</value>
  </data>
  <data name="RaOverrideCostsText" xml:space="preserve">
    <value>Voulez-vous remplacer les coûts des coûts estimés?</value>
  </data>
  <data name="RaModifiedByLbl" xml:space="preserve">
    <value>Modifié par</value>
  </data>
  <data name="RaRiskTabTxt" xml:space="preserve">
    <value>Risque</value>
  </data>
  <data name="RaWoActionsTxt" xml:space="preserve">
    <value>Sans actions</value>
  </data>
  <data name="RaVrTxt" xml:space="preserve">
    <value>Valeur/ Risque</value>
  </data>
  <data name="RaActTxt" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="RaTotTxt" xml:space="preserve">
    <value>Totale</value>
  </data>
  <data name="RaWithActionsTxt" xml:space="preserve">
    <value>Avec actions (Préventives)</value>
  </data>
  <data name="RaWithActionsPMOTabTxt" xml:space="preserve">
    <value>Avec actions actuel</value>
  </data>
  <data name="RiskCosts" xml:space="preserve">
    <value>Coûts Actions</value>
  </data>
  <data name="RaValueRiskCosts" xml:space="preserve">
    <value>Coûts Valeur/ Risque</value>
  </data>
  <data name="RaCSIRrTxt" xml:space="preserve">
    <value>CSIR de risque</value>
  </data>
  <data name="RaWTotTxt" xml:space="preserve">
    <value>Points pondérés</value>
  </data>
  <data name="RaCSIRwTxt" xml:space="preserve">
    <value>Niveau de résistance</value>
  </data>
  <data name="RaDirectRiskCosts" xml:space="preserve">
    <value>Coût du risque direct</value>
  </data>
  <data name="RaActionCosts" xml:space="preserve">
    <value>Coûts Actions</value>
  </data>
  <data name="RaAttachmentTabTxt" xml:space="preserve">
    <value>Pièces jointes</value>
  </data>
  <data name="RaRiskSortOrder" xml:space="preserve">
    <value />
  </data>
  <data name="RaAdditionalDataLbl" xml:space="preserve">
    <value />
  </data>
</root>