﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="SapaRiskObjects" xml:space="preserve">
    <value>Risk Object</value>
  </data>
  <data name="SapaGenerateSapa" xml:space="preserve">
    <value>Generate SAPA</value>
  </data>
  <data name="SapaDeleteSapa" xml:space="preserve">
    <value>Delete Item</value>
  </data>
  <data name="SapaId" xml:space="preserve">
    <value>Id</value>
  </data>
  <data name="SapaFirstYear" xml:space="preserve">
    <value>First Year</value>
  </data>
  <data name="SapaLastYear" xml:space="preserve">
    <value>Last Year</value>
  </data>
  <data name="SapaSapaType" xml:space="preserve">
    <value>Scenario or RiskObject</value>
  </data>
  <data name="SapaScenarios" xml:space="preserve">
    <value>Scenario</value>
  </data>
  <data name="SapaBudget" xml:space="preserve">
    <value>Total Budget</value>
  </data>
  <data name="SapaBudgetYear" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="SapaTotalRequested" xml:space="preserve">
    <value>Total amount requested</value>
  </data>
  <data name="SapaTotalApproved" xml:space="preserve">
    <value>Total amount approved</value>
  </data>
  <data name="SapaBudgetRemain" xml:space="preserve">
    <value>Budget remainder</value>
  </data>
  <data name="SapaDescription" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="SapaInitiator" xml:space="preserve">
    <value>Initiatior</value>
  </data>
  <data name="SapaExecutor" xml:space="preserve">
    <value>Executor</value>
  </data>
  <data name="SapaResponsible" xml:space="preserve">
    <value>Responsible</value>
  </data>
  <data name="SapaInfoTitle" xml:space="preserve">
    <value>Sapa Explanation</value>
  </data>
  <data name="SapaInfoTxt" xml:space="preserve">
    <value>Explanation about Sapa to be added</value>
  </data>
  <data name="SapaNoWorkpackage" xml:space="preserve">
    <value>[No Workpackage]</value>
  </data>
  <data name="SapaNoSapaExists" xml:space="preserve">
    <value>No Sapa exists for the selected item. Click top right button to generate it.</value>
  </data>
  <data name="SapaShowTasksInFuture" xml:space="preserve">
    <value>Show tasks that start in the future</value>
  </data>
  <data name="SapaSummaryTxt" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="SapaTotalNotApproved" xml:space="preserve">
    <value>Not approved</value>
  </data>
  <data name="SapaApproved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="SapaDeclined" xml:space="preserve">
    <value>Declined</value>
  </data>
  <data name="SapaRequested" xml:space="preserve">
    <value>Requested</value>
  </data>
  <data name="SapaName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="SapaRemark" xml:space="preserve">
    <value>Remark</value>
  </data>
  <data name="SapaCbiItem" xml:space="preserve">
    <value>CBI Category</value>
  </data>
  <data name="SapaCbiScore" xml:space="preserve">
    <value>CBI Score</value>
  </data>
  <data name="SapaApproveForAllYears" xml:space="preserve">
    <value>Approve Tasks for all years</value>
  </data>
  <data name="SapaStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="SapaApproveAllText" xml:space="preserve">
    <value>As many tasks as possible will be approved based on the order the currently are shown in.</value>
  </data>
  <data name="SapaApproveAllBtn" xml:space="preserve">
    <value>Approve all</value>
  </data>
  <data name="SapaTabTxt" xml:space="preserve">
    <value>Asset Portfolio Analysis</value>
  </data>
  <data name="SapaSettingTabTxt" xml:space="preserve">
    <value>Extra</value>
  </data>
  <data name="SapaTreeTxt" xml:space="preserve">
    <value>Portfolio</value>
  </data>
  <data name="SapaOverviewTabTxt" xml:space="preserve">
    <value>Portfiolio Overview</value>
  </data>
  <data name="SapaOverviewTxt" xml:space="preserve">
    <value>Overview</value>
  </data>
  <data name="SapaUpdateSapa" xml:space="preserve">
    <value>Update Sapa</value>
  </data>
  <data name="SapaGraphTxt" xml:space="preserve">
    <value>Executive Dashboard</value>
  </data>
  <data name="SapaBudgetAssigned" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="SapaFilterTxt" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="SapaNotApproved" xml:space="preserve">
    <value>Not approved</value>
  </data>
  <data name="SapaMaxYearsError" xml:space="preserve">
    <value>First year in tasks is {0}, last year in tasks is {1}. Generating Sapa is only supported for a maximum of {2} years.</value>
  </data>
</root>